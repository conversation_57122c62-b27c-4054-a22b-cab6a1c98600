{"remainingRequest": "D:\\shangchen\\casz-fat-j21\\casz-fat-ui\\node_modules\\babel-loader\\lib\\index.js!D:\\shangchen\\casz-fat-j21\\casz-fat-ui\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!D:\\shangchen\\casz-fat-j21\\casz-fat-ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\shangchen\\casz-fat-j21\\casz-fat-ui\\src\\components\\menu\\index.vue?vue&type=script&lang=js", "dependencies": [{"path": "D:\\shangchen\\casz-fat-j21\\casz-fat-ui\\src\\components\\menu\\index.vue", "mtime": 1752668935190}, {"path": "D:\\shangchen\\casz-fat-j21\\casz-fat-ui\\babel.config.js", "mtime": 1752668935560}, {"path": "D:\\shangchen\\casz-fat-j21\\casz-fat-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1747273083419}, {"path": "D:\\shangchen\\casz-fat-j21\\casz-fat-ui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1747273094151}, {"path": "D:\\shangchen\\casz-fat-j21\\casz-fat-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1747273083419}, {"path": "D:\\shangchen\\casz-fat-j21\\casz-fat-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1747273098384}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "names": ["_openImSdkWasm", "require", "props", "userInfo", "type", "Object", "default", "data", "menuData", "iconArr", "pathArr", "IMSDK", "created", "getSDK", "methods", "jumpPage", "path", "className", "$emit"], "sources": ["src/components/menu/index.vue"], "sourcesContent": ["<template>\r\n  <!-- 功能区 -->\r\n  <div class=\"ribbon h flex vcenter column\">\r\n    <!-- 头像 -->\r\n    <div class=\"portrait w flex lcenter vcenter column\" style=\"margin: 70% 0 30% 0;\">\r\n      <el-avatar shape=\"square\" size=\"medium\" :src=\"userInfo.faceURL\" icon=\"el-icon-user-solid\"></el-avatar>\r\n    </div>\r\n    <!-- 常用功能 -->\r\n    <div class=\"function w flex vcenter column ribbonIconStyle\" style=\"flex:1;\">\r\n      <i v-for=\"(item, index) in menuData.iconArr\" :key=\"index\" :class=\"item\" class=\"pointer menuIcon\"\r\n        @click=\"jumpPage(menuData.pathArr[index], item)\"></i>\r\n    </div>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport { getSDK } from 'open-im-sdk-wasm';\r\nexport default {\r\n  props: {\r\n    userInfo: {\r\n      type: Object,\r\n      default: () => {\r\n        return {}\r\n      }\r\n    }\r\n  },\r\n  data() {\r\n\r\n    return {\r\n      menuData: {\r\n        // iconArr: ['el-icon-chat-round', 'el-icon-user', \"el-icon-collection\", \"el-icon-folder\",\r\n        //   \"el-icon-help\", \"el-icon-link\", \"el-icon-mobile-phone\", \"el-icon-setting\"],\r\n        // pathArr: ['/chat', '/friend', '/', '/',\r\n        //   '/', '/', '/', '/login'],\r\n        iconArr: ['el-icon-chat-round', 'el-icon-user'],\r\n        pathArr: ['/chat', '/friend'],\r\n        IMSDK: null,\r\n\r\n      }\r\n    }\r\n  },\r\n  created() {\r\n    this.IMSDK = getSDK();\r\n\r\n  },\r\n  methods: {\r\n\r\n    jumpPage(path, className) {\r\n      this.$emit('jumpPage', path)\r\n      // Array.prototype.forEach.call(document.getElementsByClassName('menuIcon'),(el)=>{\r\n      //   el.classList.remove('is-chack')\r\n      // })\r\n      // document.getElementsByClassName(className)[0].classList.add('is-chack')\r\n\r\n\r\n      // if (path == '/login') {\r\n      //   localStorage.clear();\r\n      //   this.$store.commit('setChatUserInfo', {\r\n      //     isLogin: false,\r\n      //     username: '',\r\n      //     account: '',\r\n      //   })\r\n      // }\r\n      // this.$router.push(path)\r\n    },\r\n  }\r\n}\r\n</script>\r\n\r\n<style scoped>\r\n.ribbon {\r\n  width: 6.5%;\r\n  background-color: rgb(46, 46, 46, 0.9);\r\n}\r\n\r\n.content {\r\n  width: 94%;\r\n}\r\n\r\n.ribbonIconStyle {\r\n  color: rgb(147, 147, 147, 0.8);\r\n  font-size: 23px;\r\n}\r\n\r\n.menuIcon {\r\n  margin-bottom: 40%;\r\n}\r\n\r\n.menuIcon:hover {\r\n  color: rgba(192, 192, 192, 0.9);\r\n}\r\n\r\n.is-chack {\r\n  color: rgba(7, 193, 96, 0.9);\r\n}\r\n\r\n</style>"], "mappings": ";;;;;;AAgBA,IAAAA,cAAA,GAAAC,OAAA;;;;;;;;;;;;;;;;kCACA;EACAC,KAAA;IACAC,QAAA;MACAC,IAAA,EAAAC,MAAA;MACAC,OAAA,WAAAA,SAAA;QACA;MACA;IACA;EACA;EACAC,IAAA,WAAAA,KAAA;IAEA;MACAC,QAAA;QACA;QACA;QACA;QACA;QACAC,OAAA;QACAC,OAAA;QACAC,KAAA;MAEA;IACA;EACA;EACAC,OAAA,WAAAA,QAAA;IACA,KAAAD,KAAA,OAAAE,qBAAA;EAEA;EACAC,OAAA;IAEAC,QAAA,WAAAA,SAAAC,IAAA,EAAAC,SAAA;MACA,KAAAC,KAAA,aAAAF,IAAA;MACA;MACA;MACA;MACA;;MAGA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;IACA;EACA;AACA", "ignoreList": []}]}