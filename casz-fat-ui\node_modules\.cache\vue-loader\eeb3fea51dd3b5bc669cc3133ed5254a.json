{"remainingRequest": "D:\\shangchen\\casz-fat-j21\\casz-fat-ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\shangchen\\casz-fat-j21\\casz-fat-ui\\src\\views\\baseCondition\\pillHave\\index.vue?vue&type=style&index=0&id=322f9b83&lang=css", "dependencies": [{"path": "D:\\shangchen\\casz-fat-j21\\casz-fat-ui\\src\\views\\baseCondition\\pillHave\\index.vue", "mtime": 1752668935345}, {"path": "D:\\shangchen\\casz-fat-j21\\casz-fat-ui\\node_modules\\css-loader\\dist\\cjs.js", "mtime": 1747273089646}, {"path": "D:\\shangchen\\casz-fat-j21\\casz-fat-ui\\node_modules\\vue-loader\\lib\\loaders\\stylePostLoader.js", "mtime": 1747273105021}, {"path": "D:\\shangchen\\casz-fat-j21\\casz-fat-ui\\node_modules\\postcss-loader\\src\\index.js", "mtime": 1747273094093}, {"path": "D:\\shangchen\\casz-fat-j21\\casz-fat-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1747273083419}, {"path": "D:\\shangchen\\casz-fat-j21\\casz-fat-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1747273098384}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:CgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCg0KLmVsLXRhZyArIC5lbC10YWcgew0KICBtYXJnaW4tbGVmdDogMTBweDsNCn0NCi5idXR0b24tbmV3LXRhZyB7DQogIG1hcmdpbi1sZWZ0OiAxMHB4Ow0KICBoZWlnaHQ6IDMycHg7DQogIGxpbmUtaGVpZ2h0OiAzMHB4Ow0KICBwYWRkaW5nLXRvcDogMDsNCiAgcGFkZGluZy1ib3R0b206IDA7DQp9DQouaW5wdXQtbmV3LXRhZyB7DQogIHdpZHRoOiA5MHB4Ow0KICBtYXJnaW4tbGVmdDogMTBweDsNCiAgdmVydGljYWwtYWxpZ246IGJvdHRvbTsNCn0NCg=="}, {"version": 3, "sources": ["index.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAwhBA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "index.vue", "sourceRoot": "src/views/baseCondition/pillHave", "sourcesContent": ["<template>\r\n  <div class=\"app-container\">\r\n    <el-form\r\n      :model=\"queryParams\"\r\n      ref=\"queryForm\"\r\n      size=\"small\"\r\n      :inline=\"true\"\r\n      v-show=\"showSearch\"\r\n      label-width=\"68px\"\r\n    >\r\n      <!-- <el-form-item label=\"是否具备\" prop=\"alreadyHave\">\r\n        <el-select v-model=\"queryParams.alreadyHave\" placeholder=\"请选择是否具备\" clearable>\r\n          <el-option\r\n            v-for=\"dict in dict.type.sys_yes_no\"\r\n            :key=\"dict.value\"\r\n            :label=\"dict.label\"\r\n            :value=\"dict.value\"\r\n          />\r\n        </el-select>\r\n      </el-form-item> -->\r\n      <el-form-item label=\"药品类别\" prop=\"pillTypeName\">\r\n        <el-input\r\n          v-model=\"queryParams.pillTypeName\"\r\n          placeholder=\"请输入药品类别\"\r\n          clearable\r\n          @keyup.enter.native=\"handleQuery\"\r\n        />\r\n      </el-form-item>\r\n      <el-form-item>\r\n        <el-button\r\n          type=\"primary\"\r\n          icon=\"el-icon-search\"\r\n          size=\"mini\"\r\n          @click=\"handleQuery\"\r\n          >搜索</el-button\r\n        >\r\n        <el-button icon=\"el-icon-refresh\" size=\"mini\" @click=\"resetQuery\"\r\n          >重置</el-button\r\n        >\r\n      </el-form-item>\r\n    </el-form>\r\n\r\n    <el-row :gutter=\"10\" class=\"mb8\" v-show=\"false\">\r\n      <el-col :span=\"1.5\">\r\n        <el-button\r\n          type=\"primary\"\r\n          plain\r\n          icon=\"el-icon-plus\"\r\n          size=\"mini\"\r\n          @click=\"handleAdd\"\r\n          v-hasPermi=\"['baseCondition:pillHave:add']\"\r\n          >新增</el-button\r\n        >\r\n      </el-col>\r\n      <el-col :span=\"1.5\">\r\n        <el-button\r\n          type=\"primary\"\r\n          plain\r\n          icon=\"el-icon-edit\"\r\n          size=\"mini\"\r\n          :disabled=\"single\"\r\n          @click=\"handleUpdate\"\r\n          v-hasPermi=\"['baseCondition:pillHave:edit']\"\r\n          >修改</el-button\r\n        >\r\n      </el-col>\r\n      <el-col :span=\"1.5\">\r\n        <el-button\r\n          type=\"primary\"\r\n          plain\r\n          icon=\"el-icon-delete\"\r\n          size=\"mini\"\r\n          :disabled=\"multiple\"\r\n          @click=\"handleDelete\"\r\n          v-hasPermi=\"['baseCondition:pillHave:remove']\"\r\n          >删除</el-button\r\n        >\r\n      </el-col>\r\n      <el-col :span=\"1.5\">\r\n        <el-button\r\n          type=\"primary\"\r\n          plain\r\n          icon=\"el-icon-download\"\r\n          size=\"mini\"\r\n          @click=\"handleExport\"\r\n          v-hasPermi=\"['baseCondition:pillHave:export']\"\r\n          >导出</el-button\r\n        >\r\n      </el-col>\r\n      <right-toolbar\r\n        :showSearch.sync=\"showSearch\"\r\n        @queryTable=\"getList\"\r\n      ></right-toolbar>\r\n    </el-row>\r\n\r\n    <el-table\r\n      v-loading=\"loading\"\r\n      :data=\"pillHaveList\"\r\n      @selection-change=\"handleSelectionChange\"\r\n    >\r\n      <el-table-column type=\"selection\" width=\"55\" align=\"center\" />\r\n      <!-- <el-table-column label=\"ID\" align=\"center\" prop=\"id\" /> -->\r\n      <el-table-column\r\n        label=\"药品类别\"\r\n        align=\"center\"\r\n        prop=\"pillTypeName\"\r\n        width=\"280\"\r\n      />\r\n      <el-table-column\r\n        label=\"需要具备\"\r\n        align=\"center\"\r\n        prop=\"suggestHave\"\r\n        width=\"180\"\r\n      >\r\n        <template slot-scope=\"scope\">\r\n          <dict-tag\r\n            :options=\"dict.type.base_require\"\r\n            :value=\"scope.row.suggestHave\"\r\n          />\r\n        </template>\r\n      </el-table-column>\r\n      <el-table-column\r\n        label=\"是否具备\"\r\n        align=\"center\"\r\n        prop=\"alreadyHave\"\r\n        width=\"180\"\r\n      >\r\n        <template slot-scope=\"scope\">\r\n          <el-switch\r\n            v-model=\"scope.row.alreadyHave\"\r\n            active-value=\"Y\"\r\n            inactive-value=\"N\"\r\n            @change=\"haveChange(scope.row)\"\r\n          >\r\n          </el-switch>\r\n        </template>\r\n      </el-table-column>\r\n      <el-table-column label=\"药品名称\" align=\"center\" prop=\"pillNames\">\r\n        <template slot-scope=\"scope\">\r\n          <el-tag\r\n            :key=\"tag\"\r\n            v-for=\"tag in scope.row.pillNames\r\n              ? scope.row.pillNames.split(',')\r\n              : []\"\r\n            closable\r\n            :disable-transitions=\"false\"\r\n            @close=\"handlePillClose(scope.row, tag)\"\r\n          >\r\n            {{ tag }}\r\n          </el-tag>\r\n          <el-input\r\n            class=\"input-new-tag\"\r\n            v-if=\"scope.row.inputPillVisible\"\r\n            v-model=\"inputValue\"\r\n            :ref=\"'savePillTagInput' + scope.row.id\"\r\n            size=\"small\"\r\n            @keyup.enter.native=\"handlePillInputConfirm(scope.row)\"\r\n            @blur=\"handlePillInputConfirm(scope.row)\"\r\n          >\r\n          </el-input>\r\n          <el-button\r\n            v-else\r\n            class=\"button-new-tag\"\r\n            size=\"small\"\r\n            @click=\"showPillInput(scope.row)\"\r\n            >+ 药品名称</el-button\r\n          >\r\n        </template>\r\n      </el-table-column>\r\n      <el-table-column label=\"品牌\" align=\"center\" prop=\"brands\">\r\n        <template slot-scope=\"scope\">\r\n          <el-tag\r\n            :key=\"tag\"\r\n            v-for=\"tag in scope.row.brands ? scope.row.brands.split(',') : []\"\r\n            closable\r\n            :disable-transitions=\"false\"\r\n            @close=\"handleBrandClose(scope.row, tag)\"\r\n          >\r\n            {{ tag }}\r\n          </el-tag>\r\n          <el-input\r\n            class=\"input-new-tag\"\r\n            v-if=\"scope.row.inputBrandVisible\"\r\n            v-model=\"inputValue\"\r\n            :ref=\"'saveBrandTagInput' + scope.row.id\"\r\n            size=\"small\"\r\n            @keyup.enter.native=\"handleBrandInputConfirm(scope.row)\"\r\n            @blur=\"handleBrandInputConfirm(scope.row)\"\r\n          >\r\n          </el-input>\r\n          <el-button\r\n            v-else\r\n            class=\"button-new-tag\"\r\n            size=\"small\"\r\n            @click=\"showBrandInput(scope.row)\"\r\n            >+ 品牌</el-button\r\n          >\r\n        </template>\r\n      </el-table-column>\r\n      <el-table-column\r\n        v-if=\"false\"\r\n        label=\"操作\"\r\n        align=\"center\"\r\n        class-name=\"small-padding fixed-width\"\r\n      >\r\n        <template slot-scope=\"scope\">\r\n          <el-button\r\n            size=\"mini\"\r\n            type=\"text\"\r\n            icon=\"el-icon-edit\"\r\n            @click=\"handleUpdate(scope.row)\"\r\n            v-hasPermi=\"['baseCondition:pillHave:edit']\"\r\n            >修改</el-button\r\n          >\r\n          <el-button\r\n            size=\"mini\"\r\n            type=\"text\"\r\n            icon=\"el-icon-delete\"\r\n            @click=\"handleDelete(scope.row)\"\r\n            v-hasPermi=\"['baseCondition:pillHave:remove']\"\r\n            >删除</el-button\r\n          >\r\n        </template>\r\n      </el-table-column>\r\n    </el-table>\r\n\r\n    <pagination\r\n      v-show=\"total > 0\"\r\n      :total=\"total\"\r\n      :page.sync=\"queryParams.pageNum\"\r\n      :limit.sync=\"queryParams.pageSize\"\r\n      @pagination=\"getList\"\r\n    />\r\n\r\n    <!-- 添加或修改医疗机构药品配备对话框 -->\r\n    <el-dialog\r\n      :title=\"title\"\r\n      :visible.sync=\"open\"\r\n      width=\"500px\"\r\n      :close-on-click-modal=\"false\"\r\n      append-to-body\r\n    >\r\n      <el-form ref=\"form\" :model=\"form\" :rules=\"rules\" label-width=\"80px\">\r\n        <el-form-item label=\"药品类别\" prop=\"pillTypeName\">\r\n          <el-input\r\n            v-model=\"form.pillTypeName\"\r\n            disabled\r\n            placeholder=\"请输入药品类别\"\r\n          />\r\n        </el-form-item>\r\n        <el-form-item label=\"是否具备\" prop=\"alreadyHave\">\r\n          <el-radio-group v-model=\"form.alreadyHave\">\r\n            <el-radio\r\n              v-for=\"dict in dict.type.sys_yes_no\"\r\n              :key=\"dict.value\"\r\n              :label=\"dict.value\"\r\n              >{{ dict.label }}</el-radio\r\n            >\r\n          </el-radio-group>\r\n        </el-form-item>\r\n        <el-form-item\r\n          v-show=\"form.alreadyHave == 'Y'\"\r\n          label=\"药品名称\"\r\n          prop=\"pillNames\"\r\n        >\r\n          <el-input\r\n            v-model=\"form.pillNames\"\r\n            type=\"textarea\"\r\n            placeholder=\"请输入内容\"\r\n          />\r\n        </el-form-item>\r\n        <el-form-item\r\n          label=\"品牌\"\r\n          v-show=\"form.alreadyHave == 'Y'\"\r\n          prop=\"brands\"\r\n        >\r\n          <el-input\r\n            v-model=\"form.brands\"\r\n            type=\"textarea\"\r\n            placeholder=\"请输入内容\"\r\n          />\r\n        </el-form-item>\r\n      </el-form>\r\n      <div slot=\"footer\" class=\"dialog-footer\">\r\n        <el-button type=\"primary\" @click=\"submitForm\">确 定</el-button>\r\n        <el-button @click=\"cancel\">取 消</el-button>\r\n      </div>\r\n    </el-dialog>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport {\r\n  listPillHave,\r\n  getPillHave,\r\n  delPillHave,\r\n  addPillHave,\r\n  updatePillHave,\r\n} from \"@/api/baseCondition/pillHave\";\r\n\r\nexport default {\r\n  name: \"PillHave\",\r\n  dicts: [\"sys_yes_no\", \"base_require\"],\r\n  data() {\r\n    return {\r\n      // 遮罩层\r\n      loading: true,\r\n      // 选中数组\r\n      ids: [],\r\n      // 非单个禁用\r\n      single: true,\r\n      // 非多个禁用\r\n      multiple: true,\r\n      // 显示搜索条件\r\n      showSearch: true,\r\n      // 总条数\r\n      total: 0,\r\n      // 医疗机构药品配备表格数据\r\n      pillHaveList: [],\r\n      // 弹出层标题\r\n      title: \"\",\r\n      // 是否显示弹出层\r\n      open: false,\r\n      // 查询参数\r\n      queryParams: {\r\n        pageNum: 1,\r\n        pageSize: 10,\r\n        brands: null,\r\n        pillNames: null,\r\n        alreadyHave: null,\r\n        pillTypeName: null,\r\n      },\r\n      // 表单参数\r\n      form: {},\r\n      // 表单校验\r\n      rules: {},\r\n      inputVisible: false,\r\n      inputValue: \"\",\r\n    };\r\n  },\r\n  created() {\r\n    this.getList();\r\n  },\r\n  methods: {\r\n    haveChange(row) {\r\n      console.log(row);\r\n      this.updatePillHaveDebounced(row);\r\n    },\r\n    updatePillHaveDebounced: _.debounce(function (row) {\r\n      updatePillHave(row).then((response) => {\r\n        this.$modal.msgSuccess(\"修改成功\");\r\n      });\r\n    }, 1500),\r\n    handlePillClose(row, tag) {\r\n      let arr = row.pillNames.split(\",\");\r\n      arr.splice(arr.indexOf(tag), 1);\r\n      row.pillNames = arr.join(\",\");\r\n      this.updatePillHaveDebounced(row);\r\n    },\r\n\r\n    showPillInput(row) {\r\n      this.$set(row, \"inputPillVisible\", true);\r\n      this.$nextTick((_) => {\r\n        this.$refs[`savePillTagInput${row.id}`].$refs.input.focus();\r\n      });\r\n    },\r\n    handlePillInputConfirm(row) {\r\n      let inputValue = this.inputValue;\r\n      if (inputValue) {\r\n        if (row.pillNames) {\r\n          //验证是否重复\r\n          if (row.pillNames.split(\",\").indexOf(inputValue) > -1) {\r\n            this.$message.error(\"药品已存在\");\r\n            return;\r\n          } else {\r\n            row.pillNames += \",\" + inputValue;\r\n          }\r\n        } else {\r\n          row.pillNames = inputValue;\r\n        }\r\n      }\r\n      row.inputPillVisible = false;\r\n      this.inputValue = \"\";\r\n      this.updatePillHaveDebounced(row);\r\n    },\r\n\r\n    handleBrandClose(row, tag) {\r\n      let arr = row.brands.split(\",\");\r\n      arr.splice(arr.indexOf(tag), 1);\r\n      row.brands = arr.join(\",\");\r\n      this.updatePillHaveDebounced(row);\r\n    },\r\n\r\n    showBrandInput(row) {\r\n      this.$set(row, \"inputBrandVisible\", true);\r\n      this.$nextTick((_) => {\r\n        this.$refs[`saveBrandTagInput${row.id}`].$refs.input.focus();\r\n      });\r\n    },\r\n    handleBrandInputConfirm(row) {\r\n      let inputValue = this.inputValue;\r\n      if (inputValue) {\r\n        if (row.brands) {\r\n          //验证是否重复\r\n          if (row.brands.split(\",\").indexOf(inputValue) > -1) {\r\n            this.$message.error(\"品牌已存在\");\r\n            return;\r\n          } else {\r\n            row.brands += \",\" + inputValue;\r\n          }\r\n        } else {\r\n          row.brands = inputValue;\r\n        }\r\n      }\r\n      row.inputBrandVisible = false;\r\n      this.inputValue = \"\";\r\n      this.updatePillHaveDebounced(row);\r\n    },\r\n\r\n    /** 查询医疗机构药品配备列表 */\r\n    getList() {\r\n      this.loading = true;\r\n      listPillHave(this.queryParams).then((response) => {\r\n        console.log(response);\r\n        if (response.code == 200) {\r\n          this.pillHaveList = response.data;\r\n        }\r\n        // this.total = response.total;\r\n        this.loading = false;\r\n      });\r\n    },\r\n    // 取消按钮\r\n    cancel() {\r\n      this.open = false;\r\n      this.reset();\r\n    },\r\n    // 表单重置\r\n    reset() {\r\n      this.form = {\r\n        id: null,\r\n        tenantId: null,\r\n        revision: null,\r\n        createdBy: null,\r\n        createdTime: null,\r\n        updatedBy: null,\r\n        updatedTime: null,\r\n        hosId: null,\r\n        brands: null,\r\n        pillNames: null,\r\n        alreadyHave: null,\r\n        pillTypeName: null,\r\n        pillStandardId: null,\r\n        delFlag: null,\r\n      };\r\n      this.resetForm(\"form\");\r\n    },\r\n    /** 搜索按钮操作 */\r\n    handleQuery() {\r\n      this.queryParams.pageNum = 1;\r\n      this.getList();\r\n    },\r\n    /** 重置按钮操作 */\r\n    resetQuery() {\r\n      this.resetForm(\"queryForm\");\r\n      this.handleQuery();\r\n    },\r\n    // 多选框选中数据\r\n    handleSelectionChange(selection) {\r\n      this.ids = selection.map((item) => item.id);\r\n      this.single = selection.length !== 1;\r\n      this.multiple = !selection.length;\r\n    },\r\n    /** 新增按钮操作 */\r\n    handleAdd() {\r\n      this.reset();\r\n      this.open = true;\r\n      this.title = \"添加医疗机构药品配备\";\r\n    },\r\n    /** 修改按钮操作 */\r\n    handleUpdate(row) {\r\n      this.reset();\r\n      const id = row.id || this.ids;\r\n      getPillHave(id).then((response) => {\r\n        this.form = response.data;\r\n        this.open = true;\r\n        this.title = \"修改医疗机构药品配备\";\r\n      });\r\n    },\r\n    /** 提交按钮 */\r\n    submitForm() {\r\n      this.$refs[\"form\"].validate((valid) => {\r\n        if (valid) {\r\n          if (this.form.id != null) {\r\n            updatePillHave(this.form).then((response) => {\r\n              this.$modal.msgSuccess(\"修改成功\");\r\n              this.open = false;\r\n              this.getList();\r\n            });\r\n          } else {\r\n            addPillHave(this.form).then((response) => {\r\n              this.$modal.msgSuccess(\"新增成功\");\r\n              this.open = false;\r\n              this.getList();\r\n            });\r\n          }\r\n        }\r\n      });\r\n    },\r\n    /** 删除按钮操作 */\r\n    handleDelete(row) {\r\n      const ids = row.id || this.ids;\r\n      this.$modal\r\n        .confirm('是否确认删除医疗机构药品配备编号为\"' + ids + '\"的数据项？')\r\n        .then(function () {\r\n          return delPillHave(ids);\r\n        })\r\n        .then(() => {\r\n          this.getList();\r\n          this.$modal.msgSuccess(\"删除成功\");\r\n        })\r\n        .catch(() => {});\r\n    },\r\n    /** 导出按钮操作 */\r\n    handleExport() {\r\n      this.download(\r\n        \"baseCondition/pillHave/export\",\r\n        {\r\n          ...this.queryParams,\r\n        },\r\n        `pillHave_${new Date().getTime()}.xlsx`\r\n      );\r\n    },\r\n  },\r\n};\r\n</script>\r\n<style>\r\n.el-tag + .el-tag {\r\n  margin-left: 10px;\r\n}\r\n.button-new-tag {\r\n  margin-left: 10px;\r\n  height: 32px;\r\n  line-height: 30px;\r\n  padding-top: 0;\r\n  padding-bottom: 0;\r\n}\r\n.input-new-tag {\r\n  width: 90px;\r\n  margin-left: 10px;\r\n  vertical-align: bottom;\r\n}\r\n</style>"]}]}