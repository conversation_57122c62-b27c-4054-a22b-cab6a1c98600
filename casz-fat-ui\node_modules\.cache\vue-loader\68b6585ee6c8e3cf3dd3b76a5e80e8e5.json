{"remainingRequest": "D:\\shangchen\\casz-fat-j21\\casz-fat-ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\shangchen\\casz-fat-j21\\casz-fat-ui\\src\\components\\UserSelect\\index.vue?vue&type=script&lang=js", "dependencies": [{"path": "D:\\shangchen\\casz-fat-j21\\casz-fat-ui\\src\\components\\UserSelect\\index.vue", "mtime": 1752668935318}, {"path": "D:\\shangchen\\casz-fat-j21\\casz-fat-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1747273083419}, {"path": "D:\\shangchen\\casz-fat-j21\\casz-fat-ui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1747273094151}, {"path": "D:\\shangchen\\casz-fat-j21\\casz-fat-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1747273083419}, {"path": "D:\\shangchen\\casz-fat-j21\\casz-fat-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1747273098384}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:Ly8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KDQppbXBvcnQgew0KICBsaXN0VXNlciwNCiAgZ2V0VXNlciwNCiAgZGVsVXNlciwNCiAgYWRkVXNlciwNCiAgdXBkYXRlVXNlciwNCiAgcmVzZXRVc2VyUHdkLA0KICBjaGFuZ2VVc2VyU3RhdHVzLA0KICBkZXB0VHJlZVNlbGVjdCwNCn0gZnJvbSAiQC9hcGkvc3lzdGVtL3VzZXIiOw0KaW1wb3J0IHsgZ2V0VG9rZW4gfSBmcm9tICJAL3V0aWxzL2F1dGgiOw0KaW1wb3J0IFRyZWVzZWxlY3QgZnJvbSAiQHJpb3BoYWUvdnVlLXRyZWVzZWxlY3QiOw0KaW1wb3J0ICJAcmlvcGhhZS92dWUtdHJlZXNlbGVjdC9kaXN0L3Z1ZS10cmVlc2VsZWN0LmNzcyI7DQoNCmV4cG9ydCBkZWZhdWx0IHsNCiAgbmFtZTogIlVzZXIiLA0KICBkaWN0czogWyJzeXNfbm9ybWFsX2Rpc2FibGUiLCAic3lzX3VzZXJfc2V4Il0sDQogIGNvbXBvbmVudHM6IHsgVHJlZXNlbGVjdCB9LA0KICBwcm9wczogew0KICAgIHNlbGVjdFVzZXJzOiB7DQogICAgICB0eXBlOiBBcnJheSwNCiAgICAgIGRlZmF1bHQ6IGZ1bmN0aW9uICgpIHsNCiAgICAgICAgcmV0dXJuIFtdOw0KICAgICAgfSwNCiAgICB9LA0KICB9LA0KICB3YXRjaDogew0KICAgIHNlbGVjdFVzZXJzOiB7DQogICAgICBoYW5kbGVyKHZhbCkgew0KICAgICAgICBjb25zb2xlLmxvZygic2VsZWN0VXNlcnMgY2hhbmdlIiwgdmFsKTsNCiAgICAgIH0sDQogICAgICBpbW1lZGlhdGU6IHRydWUsDQogICAgICBkZWVwOiB0cnVlLA0KICAgIH0sDQogIH0sDQogIGRhdGEoKSB7DQogICAgcmV0dXJuIHsNCiAgICAgIC8vIOmBrue9qeWxgg0KICAgICAgbG9hZGluZzogdHJ1ZSwNCiAgICAgIC8vIOmAieS4reaVsOe7hA0KICAgICAgaWRzOiBbXSwNCiAgICAgIC8vIOmdnuWNleS4quemgeeUqA0KICAgICAgc2luZ2xlOiB0cnVlLA0KICAgICAgLy8g6Z2e5aSa5Liq56aB55SoDQogICAgICBtdWx0aXBsZTogdHJ1ZSwNCiAgICAgIC8vIOaYvuekuuaQnOe0ouadoeS7tg0KICAgICAgc2hvd1NlYXJjaDogdHJ1ZSwNCiAgICAgIC8vIOaAu+adoeaVsA0KICAgICAgdG90YWw6IDAsDQogICAgICAvLyDnlKjmiLfooajmoLzmlbDmja4NCiAgICAgIHVzZXJMaXN0OiBudWxsLA0KICAgICAgLy8g5by55Ye65bGC5qCH6aKYDQogICAgICB0aXRsZTogIiIsDQogICAgICAvLyDpg6jpl6jmoJHpgInpobkNCiAgICAgIGRlcHRPcHRpb25zOiB1bmRlZmluZWQsDQogICAgICAvLyDmmK/lkKbmmL7npLrlvLnlh7rlsYINCiAgICAgIG9wZW46IGZhbHNlLA0KICAgICAgLy8g6YOo6Zeo5ZCN56ewDQogICAgICBkZXB0TmFtZTogdW5kZWZpbmVkLA0KICAgICAgLy8g6buY6K6k5a+G56CBDQogICAgICBpbml0UGFzc3dvcmQ6IHVuZGVmaW5lZCwNCiAgICAgIC8vIOaXpeacn+iMg+WbtA0KICAgICAgZGF0ZVJhbmdlOiBbXSwNCiAgICAgIC8vIOWyl+S9jemAiemhuQ0KICAgICAgcG9zdE9wdGlvbnM6IFtdLA0KICAgICAgLy8g6KeS6Imy6YCJ6aG5DQogICAgICByb2xlT3B0aW9uczogW10sDQogICAgICAvLyDooajljZXlj4LmlbANCiAgICAgIGZvcm06IHt9LA0KICAgICAgZGVmYXVsdFByb3BzOiB7DQogICAgICAgIGNoaWxkcmVuOiAiY2hpbGRyZW4iLA0KICAgICAgICBsYWJlbDogImxhYmVsIiwNCiAgICAgIH0sDQogICAgICAvLyDnlKjmiLflr7zlhaXlj4LmlbANCiAgICAgIHVwbG9hZDogew0KICAgICAgICAvLyDmmK/lkKbmmL7npLrlvLnlh7rlsYLvvIjnlKjmiLflr7zlhaXvvIkNCiAgICAgICAgb3BlbjogZmFsc2UsDQogICAgICAgIC8vIOW8ueWHuuWxguagh+mimO+8iOeUqOaIt+WvvOWFpe+8iQ0KICAgICAgICB0aXRsZTogIiIsDQogICAgICAgIC8vIOaYr+WQpuemgeeUqOS4iuS8oA0KICAgICAgICBpc1VwbG9hZGluZzogZmFsc2UsDQogICAgICAgIC8vIOaYr+WQpuabtOaWsOW3sue7j+WtmOWcqOeahOeUqOaIt+aVsOaNrg0KICAgICAgICB1cGRhdGVTdXBwb3J0OiAwLA0KICAgICAgICAvLyDorr7nva7kuIrkvKDnmoTor7fmsYLlpLTpg6gNCiAgICAgICAgaGVhZGVyczogeyBBdXRob3JpemF0aW9uOiAiQmVhcmVyICIgKyBnZXRUb2tlbigpIH0sDQogICAgICAgIC8vIOS4iuS8oOeahOWcsOWdgA0KICAgICAgICB1cmw6IHByb2Nlc3MuZW52LlZVRV9BUFBfQkFTRV9BUEkgKyAiL3N5c3RlbS91c2VyL2ltcG9ydERhdGEiLA0KICAgICAgfSwNCiAgICAgIC8vIOafpeivouWPguaVsA0KICAgICAgcXVlcnlQYXJhbXM6IHsNCiAgICAgICAgcGFnZU51bTogMSwNCiAgICAgICAgcGFnZVNpemU6IDEwLA0KICAgICAgICB1c2VyTmFtZTogdW5kZWZpbmVkLA0KICAgICAgICBwaG9uZW51bWJlcjogdW5kZWZpbmVkLA0KICAgICAgICBzdGF0dXM6IHVuZGVmaW5lZCwNCiAgICAgICAgZGVwdElkOiB1bmRlZmluZWQsDQogICAgICB9LA0KICAgICAgLy8g5YiX5L+h5oGvDQogICAgICBjb2x1bW5zOiBbDQogICAgICAgIHsga2V5OiAwLCBsYWJlbDogYOeUqOaIt+e8luWPt2AsIHZpc2libGU6IHRydWUgfSwNCiAgICAgICAgeyBrZXk6IDEsIGxhYmVsOiBg55So5oi35ZCN56ewYCwgdmlzaWJsZTogdHJ1ZSB9LA0KICAgICAgICB7IGtleTogMiwgbGFiZWw6IGDnlKjmiLfmmLXnp7BgLCB2aXNpYmxlOiB0cnVlIH0sDQogICAgICAgIHsga2V5OiAzLCBsYWJlbDogYOmDqOmXqGAsIHZpc2libGU6IHRydWUgfSwNCiAgICAgICAgeyBrZXk6IDQsIGxhYmVsOiBg5omL5py65Y+356CBYCwgdmlzaWJsZTogdHJ1ZSB9LA0KICAgICAgICB7IGtleTogNSwgbGFiZWw6IGDnirbmgIFgLCB2aXNpYmxlOiB0cnVlIH0sDQogICAgICAgIHsga2V5OiA2LCBsYWJlbDogYOWIm+W7uuaXtumXtGAsIHZpc2libGU6IHRydWUgfSwNCiAgICAgIF0sDQogICAgICAvLyDooajljZXmoKHpqowNCiAgICAgIHJ1bGVzOiB7DQogICAgICAgIHVzZXJOYW1lOiBbDQogICAgICAgICAgeyByZXF1aXJlZDogdHJ1ZSwgbWVzc2FnZTogIueUqOaIt+WQjeensOS4jeiDveS4uuepuiIsIHRyaWdnZXI6ICJibHVyIiB9LA0KICAgICAgICAgIHsNCiAgICAgICAgICAgIG1pbjogMiwNCiAgICAgICAgICAgIG1heDogMjAsDQogICAgICAgICAgICBtZXNzYWdlOiAi55So5oi35ZCN56ew6ZW/5bqm5b+F6aG75LuL5LqOIDIg5ZKMIDIwIOS5i+mXtCIsDQogICAgICAgICAgICB0cmlnZ2VyOiAiYmx1ciIsDQogICAgICAgICAgfSwNCiAgICAgICAgXSwNCiAgICAgICAgbmlja05hbWU6IFsNCiAgICAgICAgICB7IHJlcXVpcmVkOiB0cnVlLCBtZXNzYWdlOiAi55So5oi35pi156ew5LiN6IO95Li656m6IiwgdHJpZ2dlcjogImJsdXIiIH0sDQogICAgICAgIF0sDQogICAgICAgIHBhc3N3b3JkOiBbDQogICAgICAgICAgeyByZXF1aXJlZDogdHJ1ZSwgbWVzc2FnZTogIueUqOaIt+WvhueggeS4jeiDveS4uuepuiIsIHRyaWdnZXI6ICJibHVyIiB9LA0KICAgICAgICAgIHsNCiAgICAgICAgICAgIG1pbjogNSwNCiAgICAgICAgICAgIG1heDogMjAsDQogICAgICAgICAgICBtZXNzYWdlOiAi55So5oi35a+G56CB6ZW/5bqm5b+F6aG75LuL5LqOIDUg5ZKMIDIwIOS5i+mXtCIsDQogICAgICAgICAgICB0cmlnZ2VyOiAiYmx1ciIsDQogICAgICAgICAgfSwNCiAgICAgICAgXSwNCiAgICAgICAgZW1haWw6IFsNCiAgICAgICAgICB7DQogICAgICAgICAgICB0eXBlOiAiZW1haWwiLA0KICAgICAgICAgICAgbWVzc2FnZTogIuivt+i+k+WFpeato+ehrueahOmCrueuseWcsOWdgCIsDQogICAgICAgICAgICB0cmlnZ2VyOiBbImJsdXIiLCAiY2hhbmdlIl0sDQogICAgICAgICAgfSwNCiAgICAgICAgXSwNCiAgICAgICAgcGhvbmVudW1iZXI6IFsNCiAgICAgICAgICB7DQogICAgICAgICAgICBwYXR0ZXJuOiAvXjFbM3w0fDV8Nnw3fDh8OV1bMC05XVxkezh9JC8sDQogICAgICAgICAgICBtZXNzYWdlOiAi6K+36L6T5YWl5q2j56Gu55qE5omL5py65Y+356CBIiwNCiAgICAgICAgICAgIHRyaWdnZXI6ICJibHVyIiwNCiAgICAgICAgICB9LA0KICAgICAgICBdLA0KICAgICAgfSwNCiAgICB9Ow0KICB9LA0KICB3YXRjaDogew0KICAgIC8vIOagueaNruWQjeensOetm+mAiemDqOmXqOagkQ0KICAgIGRlcHROYW1lKHZhbCkgew0KICAgICAgdGhpcy4kcmVmcy50cmVlLmZpbHRlcih2YWwpOw0KICAgIH0sDQogICAgc2VsZWN0VXNlcnModmFsKSB7DQogICAgICB0aGlzLnVwZGF0ZVRhYmxlUm93U2VsKHZhbCk7DQogICAgfSwNCiAgfSwNCiAgY3JlYXRlZCgpIHsNCiAgICB0aGlzLmdldExpc3QoKTsNCiAgICB0aGlzLmdldERlcHRUcmVlKCk7DQogICAgdGhpcy5nZXRDb25maWdLZXkoInN5cy51c2VyLmluaXRQYXNzd29yZCIpLnRoZW4oKHJlc3BvbnNlKSA9PiB7DQogICAgICB0aGlzLmluaXRQYXNzd29yZCA9IHJlc3BvbnNlLm1zZzsNCiAgICB9KTsNCiAgfSwNCiAgbWV0aG9kczogew0KICAgIGdldFJvd0tleXModmFsKSB7DQogICAgICByZXR1cm4gdmFsLnVzZXJJZDsNCiAgICB9LA0KICAgIHVwZGF0ZVRhYmxlUm93U2VsKHVzZXIpIHsNCiAgICAgIC8v5Y+Y5pu06YCJ5oupDQogICAgICBpZiAodGhpcy4kcmVmcy5mb3JtVGFibGUpIHsNCiAgICAgICAgY29uc29sZS5sb2coIuWPmOabtOmAieaLqSIsIHVzZXIpOw0KICAgICAgICB0aGlzLiRuZXh0VGljaygoKSA9PiB7DQogICAgICAgICAgdGhpcy4kcmVmcy5mb3JtVGFibGUudG9nZ2xlUm93U2VsZWN0aW9uKHVzZXJbMF0sIGZhbHNlKTsNCiAgICAgICAgfSk7DQogICAgICB9DQoNCiAgICAgIC8vICAgaWYgKHRoaXMuJHJlZnMuZm9ybVRhYmxlKSB0aGlzLiRyZWZzLmZvcm1UYWJsZS5jbGVhclNlbGVjdGlvbigpOw0KICAgICAgLy8gICB0aGlzLiRuZXh0VGljaygoKSA9PiB7DQogICAgICAvLyAgICAgdXNlckxpc3QuZm9yRWFjaCgoa2V5KSA9PiB7DQogICAgICAvLyAgICAgICB1c2Vycy5mb3JFYWNoKCh1c2VyKSA9PiB7DQogICAgICAvLyAgICAgICAgIGlmICh0aGlzLiRyZWZzLmZvcm1UYWJsZSkgew0KICAgICAgLy8gICAgICAgICAgIGlmICh1c2VyLmlkID09PSBrZXkuaWQpIHsNCiAgICAgIC8vICAgICAgICAgICAgIGNvbnNvbGUubG9nKCLpgInkuK0xIiwga2V5LCB1c2VyKTsNCiAgICAgIC8vICAgICAgICAgICAgIHRoaXMuJHJlZnMuZm9ybVRhYmxlLnRvZ2dsZVJvd1NlbGVjdGlvbih1c2VyLCB0cnVlKTsNCiAgICAgIC8vICAgICAgICAgICB9DQogICAgICAvLyAgICAgICAgIH0NCiAgICAgIC8vICAgICAgIH0pOw0KICAgICAgLy8gICAgIH0pOw0KICAgICAgLy8gICB9KTsNCiAgICB9LA0KICAgIC8qKiDmn6Xor6LnlKjmiLfliJfooaggKi8NCiAgICBnZXRMaXN0KCkgew0KICAgICAgdGhpcy5sb2FkaW5nID0gdHJ1ZTsNCiAgICAgIGxpc3RVc2VyKHRoaXMuYWRkRGF0ZVJhbmdlKHRoaXMucXVlcnlQYXJhbXMsIHRoaXMuZGF0ZVJhbmdlKSkudGhlbigNCiAgICAgICAgKHJlc3BvbnNlKSA9PiB7DQogICAgICAgICAgdGhpcy51c2VyTGlzdCA9IHJlc3BvbnNlLnJvd3M7DQogICAgICAgICAgdGhpcy50b3RhbCA9IHJlc3BvbnNlLnRvdGFsOw0KICAgICAgICAgIHRoaXMubG9hZGluZyA9IGZhbHNlOw0KICAgICAgICB9DQogICAgICApOw0KICAgIH0sDQogICAgLyoqIOafpeivoumDqOmXqOS4i+aLieagkee7k+aehCAqLw0KICAgIGdldERlcHRUcmVlKCkgew0KICAgICAgZGVwdFRyZWVTZWxlY3QoKS50aGVuKChyZXNwb25zZSkgPT4gew0KICAgICAgICB0aGlzLmRlcHRPcHRpb25zID0gcmVzcG9uc2UuZGF0YTsNCiAgICAgIH0pOw0KICAgIH0sDQogICAgLy8g562b6YCJ6IqC54K5DQogICAgZmlsdGVyTm9kZSh2YWx1ZSwgZGF0YSkgew0KICAgICAgaWYgKCF2YWx1ZSkgcmV0dXJuIHRydWU7DQogICAgICByZXR1cm4gZGF0YS5sYWJlbC5pbmRleE9mKHZhbHVlKSAhPT0gLTE7DQogICAgfSwNCiAgICAvLyDoioLngrnljZXlh7vkuovku7YNCiAgICBoYW5kbGVOb2RlQ2xpY2soZGF0YSkgew0KICAgICAgdGhpcy5xdWVyeVBhcmFtcy5kZXB0SWQgPSBkYXRhLmlkOw0KICAgICAgdGhpcy5oYW5kbGVRdWVyeSgpOw0KICAgIH0sDQogICAgLy8g55So5oi354q25oCB5L+u5pS5DQogICAgaGFuZGxlU3RhdHVzQ2hhbmdlKHJvdykgew0KICAgICAgbGV0IHRleHQgPSByb3cuc3RhdHVzID09PSAiMCIgPyAi5ZCv55SoIiA6ICLlgZznlKgiOw0KICAgICAgdGhpcy4kbW9kYWwNCiAgICAgICAgLmNvbmZpcm0oJ+ehruiupOimgSInICsgdGV4dCArICciIicgKyByb3cudXNlck5hbWUgKyAnIueUqOaIt+WQl++8nycpDQogICAgICAgIC50aGVuKGZ1bmN0aW9uICgpIHsNCiAgICAgICAgICByZXR1cm4gY2hhbmdlVXNlclN0YXR1cyhyb3cudXNlcklkLCByb3cuc3RhdHVzKTsNCiAgICAgICAgfSkNCiAgICAgICAgLnRoZW4oKCkgPT4gew0KICAgICAgICAgIHRoaXMuJG1vZGFsLm1zZ1N1Y2Nlc3ModGV4dCArICLmiJDlip8iKTsNCiAgICAgICAgfSkNCiAgICAgICAgLmNhdGNoKGZ1bmN0aW9uICgpIHsNCiAgICAgICAgICByb3cuc3RhdHVzID0gcm93LnN0YXR1cyA9PT0gIjAiID8gIjEiIDogIjAiOw0KICAgICAgICB9KTsNCiAgICB9LA0KICAgIC8vIOWPlua2iOaMiemSrg0KICAgIGNhbmNlbCgpIHsNCiAgICAgIHRoaXMub3BlbiA9IGZhbHNlOw0KICAgICAgdGhpcy5yZXNldCgpOw0KICAgIH0sDQogICAgLy8g6KGo5Y2V6YeN572uDQogICAgcmVzZXQoKSB7DQogICAgICB0aGlzLmZvcm0gPSB7DQogICAgICAgIHVzZXJJZDogdW5kZWZpbmVkLA0KICAgICAgICBkZXB0SWQ6IHVuZGVmaW5lZCwNCiAgICAgICAgdXNlck5hbWU6IHVuZGVmaW5lZCwNCiAgICAgICAgbmlja05hbWU6IHVuZGVmaW5lZCwNCiAgICAgICAgcGFzc3dvcmQ6IHVuZGVmaW5lZCwNCiAgICAgICAgcGhvbmVudW1iZXI6IHVuZGVmaW5lZCwNCiAgICAgICAgZW1haWw6IHVuZGVmaW5lZCwNCiAgICAgICAgc2V4OiB1bmRlZmluZWQsDQogICAgICAgIHN0YXR1czogIjAiLA0KICAgICAgICByZW1hcms6IHVuZGVmaW5lZCwNCiAgICAgICAgcG9zdElkczogW10sDQogICAgICAgIHJvbGVJZHM6IFtdLA0KICAgICAgfTsNCiAgICAgIHRoaXMucmVzZXRGb3JtKCJmb3JtIik7DQogICAgfSwNCiAgICAvKiog5pCc57Si5oyJ6ZKu5pON5L2cICovDQogICAgaGFuZGxlUXVlcnkoKSB7DQogICAgICB0aGlzLnF1ZXJ5UGFyYW1zLnBhZ2VOdW0gPSAxOw0KICAgICAgdGhpcy5nZXRMaXN0KCk7DQogICAgfSwNCiAgICAvKiog6YeN572u5oyJ6ZKu5pON5L2cICovDQogICAgcmVzZXRRdWVyeSgpIHsNCiAgICAgIHRoaXMuZGF0ZVJhbmdlID0gW107DQogICAgICB0aGlzLnJlc2V0Rm9ybSgicXVlcnlGb3JtIik7DQogICAgICB0aGlzLnF1ZXJ5UGFyYW1zLmRlcHRJZCA9IHVuZGVmaW5lZDsNCiAgICAgIHRoaXMuJHJlZnMudHJlZS5zZXRDdXJyZW50S2V5KG51bGwpOw0KICAgICAgdGhpcy5oYW5kbGVRdWVyeSgpOw0KICAgIH0sDQogICAgLy8g5aSa6YCJ5qGG6YCJ5Lit5pWw5o2uDQogICAgaGFuZGxlU2VsZWN0aW9uQ2hhbmdlKHNlbGVjdGlvbikgew0KICAgICAgdGhpcy5pZHMgPSBzZWxlY3Rpb24ubWFwKChpdGVtKSA9PiBpdGVtLnVzZXJJZCk7DQogICAgICB0aGlzLnNpbmdsZSA9IHNlbGVjdGlvbi5sZW5ndGggIT0gMTsNCiAgICAgIHRoaXMubXVsdGlwbGUgPSAhc2VsZWN0aW9uLmxlbmd0aDsNCiAgICAgIGNvbnNvbGUubG9nKCJzZWxlY3Rpb24iLCBzZWxlY3Rpb24pOw0KICAgICAgdGhpcy4kZW1pdCgic2VsZWN0Q2hhbmdlIiwgc2VsZWN0aW9uKTsNCiAgICB9LA0KICAgIC8vIOabtOWkmuaTjeS9nOinpuWPkQ0KICAgIGhhbmRsZUNvbW1hbmQoY29tbWFuZCwgcm93KSB7DQogICAgICBzd2l0Y2ggKGNvbW1hbmQpIHsNCiAgICAgICAgY2FzZSAiaGFuZGxlUmVzZXRQd2QiOg0KICAgICAgICAgIHRoaXMuaGFuZGxlUmVzZXRQd2Qocm93KTsNCiAgICAgICAgICBicmVhazsNCiAgICAgICAgY2FzZSAiaGFuZGxlQXV0aFJvbGUiOg0KICAgICAgICAgIHRoaXMuaGFuZGxlQXV0aFJvbGUocm93KTsNCiAgICAgICAgICBicmVhazsNCiAgICAgICAgZGVmYXVsdDoNCiAgICAgICAgICBicmVhazsNCiAgICAgIH0NCiAgICB9LA0KICAgIC8qKiDmlrDlop7mjInpkq7mk43kvZwgKi8NCiAgICBoYW5kbGVBZGQoKSB7DQogICAgICB0aGlzLnJlc2V0KCk7DQogICAgICBnZXRVc2VyKCkudGhlbigocmVzcG9uc2UpID0+IHsNCiAgICAgICAgdGhpcy5wb3N0T3B0aW9ucyA9IHJlc3BvbnNlLnBvc3RzOw0KICAgICAgICB0aGlzLnJvbGVPcHRpb25zID0gcmVzcG9uc2Uucm9sZXM7DQogICAgICAgIHRoaXMub3BlbiA9IHRydWU7DQogICAgICAgIHRoaXMudGl0bGUgPSAi5re75Yqg55So5oi3IjsNCiAgICAgICAgdGhpcy5mb3JtLnBhc3N3b3JkID0gdGhpcy5pbml0UGFzc3dvcmQ7DQogICAgICB9KTsNCiAgICB9LA0KICAgIC8qKiDkv67mlLnmjInpkq7mk43kvZwgKi8NCiAgICBoYW5kbGVVcGRhdGUocm93KSB7DQogICAgICB0aGlzLnJlc2V0KCk7DQogICAgICBjb25zdCB1c2VySWQgPSByb3cudXNlcklkIHx8IHRoaXMuaWRzOw0KICAgICAgZ2V0VXNlcih1c2VySWQpLnRoZW4oKHJlc3BvbnNlKSA9PiB7DQogICAgICAgIHRoaXMuZm9ybSA9IHJlc3BvbnNlLmRhdGE7DQogICAgICAgIHRoaXMucG9zdE9wdGlvbnMgPSByZXNwb25zZS5wb3N0czsNCiAgICAgICAgdGhpcy5yb2xlT3B0aW9ucyA9IHJlc3BvbnNlLnJvbGVzOw0KICAgICAgICB0aGlzLiRzZXQodGhpcy5mb3JtLCAicG9zdElkcyIsIHJlc3BvbnNlLnBvc3RJZHMpOw0KICAgICAgICB0aGlzLiRzZXQodGhpcy5mb3JtLCAicm9sZUlkcyIsIHJlc3BvbnNlLnJvbGVJZHMpOw0KICAgICAgICB0aGlzLm9wZW4gPSB0cnVlOw0KICAgICAgICB0aGlzLnRpdGxlID0gIuS/ruaUueeUqOaItyI7DQogICAgICAgIHRoaXMuZm9ybS5wYXNzd29yZCA9ICIiOw0KICAgICAgfSk7DQogICAgfSwNCiAgICAvKiog6YeN572u5a+G56CB5oyJ6ZKu5pON5L2cICovDQogICAgaGFuZGxlUmVzZXRQd2Qocm93KSB7DQogICAgICB0aGlzLiRwcm9tcHQoJ+ivt+i+k+WFpSInICsgcm93LnVzZXJOYW1lICsgJyLnmoTmlrDlr4bnoIEnLCAi5o+Q56S6Iiwgew0KICAgICAgICBjb25maXJtQnV0dG9uVGV4dDogIuehruWumiIsDQogICAgICAgIGNhbmNlbEJ1dHRvblRleHQ6ICLlj5bmtogiLA0KICAgICAgICBjbG9zZU9uQ2xpY2tNb2RhbDogZmFsc2UsDQogICAgICAgIGlucHV0UGF0dGVybjogL14uezUsMjB9JC8sDQogICAgICAgIGlucHV0RXJyb3JNZXNzYWdlOiAi55So5oi35a+G56CB6ZW/5bqm5b+F6aG75LuL5LqOIDUg5ZKMIDIwIOS5i+mXtCIsDQogICAgICB9KQ0KICAgICAgICAudGhlbigoeyB2YWx1ZSB9KSA9PiB7DQogICAgICAgICAgcmVzZXRVc2VyUHdkKHJvdy51c2VySWQsIHZhbHVlKS50aGVuKChyZXNwb25zZSkgPT4gew0KICAgICAgICAgICAgdGhpcy4kbW9kYWwubXNnU3VjY2Vzcygi5L+u5pS55oiQ5Yqf77yM5paw5a+G56CB5piv77yaIiArIHZhbHVlKTsNCiAgICAgICAgICB9KTsNCiAgICAgICAgfSkNCiAgICAgICAgLmNhdGNoKCgpID0+IHt9KTsNCiAgICB9LA0KICAgIC8qKiDliIbphY3op5LoibLmk43kvZwgKi8NCiAgICBoYW5kbGVBdXRoUm9sZTogZnVuY3Rpb24gKHJvdykgew0KICAgICAgY29uc3QgdXNlcklkID0gcm93LnVzZXJJZDsNCiAgICAgIHRoaXMuJHJvdXRlci5wdXNoKCIvc3lzdGVtL3VzZXItYXV0aC9yb2xlLyIgKyB1c2VySWQpOw0KICAgIH0sDQogICAgLyoqIOaPkOS6pOaMiemSriAqLw0KICAgIHN1Ym1pdEZvcm06IGZ1bmN0aW9uICgpIHsNCiAgICAgIHRoaXMuJHJlZnNbImZvcm0iXS52YWxpZGF0ZSgodmFsaWQpID0+IHsNCiAgICAgICAgaWYgKHZhbGlkKSB7DQogICAgICAgICAgaWYgKHRoaXMuZm9ybS51c2VySWQgIT0gdW5kZWZpbmVkKSB7DQogICAgICAgICAgICB1cGRhdGVVc2VyKHRoaXMuZm9ybSkudGhlbigocmVzcG9uc2UpID0+IHsNCiAgICAgICAgICAgICAgdGhpcy4kbW9kYWwubXNnU3VjY2Vzcygi5L+u5pS55oiQ5YqfIik7DQogICAgICAgICAgICAgIHRoaXMub3BlbiA9IGZhbHNlOw0KICAgICAgICAgICAgICB0aGlzLmdldExpc3QoKTsNCiAgICAgICAgICAgIH0pOw0KICAgICAgICAgIH0gZWxzZSB7DQogICAgICAgICAgICBhZGRVc2VyKHRoaXMuZm9ybSkudGhlbigocmVzcG9uc2UpID0+IHsNCiAgICAgICAgICAgICAgdGhpcy4kbW9kYWwubXNnU3VjY2Vzcygi5paw5aKe5oiQ5YqfIik7DQogICAgICAgICAgICAgIHRoaXMub3BlbiA9IGZhbHNlOw0KICAgICAgICAgICAgICB0aGlzLmdldExpc3QoKTsNCiAgICAgICAgICAgIH0pOw0KICAgICAgICAgIH0NCiAgICAgICAgfQ0KICAgICAgfSk7DQogICAgfSwNCiAgICAvKiog5Yig6Zmk5oyJ6ZKu5pON5L2cICovDQogICAgaGFuZGxlRGVsZXRlKHJvdykgew0KICAgICAgY29uc3QgdXNlcklkcyA9IHJvdy51c2VySWQgfHwgdGhpcy5pZHM7DQogICAgICB0aGlzLiRtb2RhbA0KICAgICAgICAuY29uZmlybSgn5piv5ZCm56Gu6K6k5Yig6Zmk55So5oi357yW5Y+35Li6IicgKyB1c2VySWRzICsgJyLnmoTmlbDmja7pobnvvJ8nKQ0KICAgICAgICAudGhlbihmdW5jdGlvbiAoKSB7DQogICAgICAgICAgcmV0dXJuIGRlbFVzZXIodXNlcklkcyk7DQogICAgICAgIH0pDQogICAgICAgIC50aGVuKCgpID0+IHsNCiAgICAgICAgICB0aGlzLmdldExpc3QoKTsNCiAgICAgICAgICB0aGlzLiRtb2RhbC5tc2dTdWNjZXNzKCLliKDpmaTmiJDlip8iKTsNCiAgICAgICAgfSkNCiAgICAgICAgLmNhdGNoKCgpID0+IHt9KTsNCiAgICB9LA0KICAgIC8qKiDlr7zlh7rmjInpkq7mk43kvZwgKi8NCiAgICBoYW5kbGVFeHBvcnQoKSB7DQogICAgICB0aGlzLmRvd25sb2FkKA0KICAgICAgICAic3lzdGVtL3VzZXIvZXhwb3J0IiwNCiAgICAgICAgew0KICAgICAgICAgIC4uLnRoaXMucXVlcnlQYXJhbXMsDQogICAgICAgIH0sDQogICAgICAgIGB1c2VyXyR7bmV3IERhdGUoKS5nZXRUaW1lKCl9Lnhsc3hgDQogICAgICApOw0KICAgIH0sDQogICAgLyoqIOWvvOWFpeaMiemSruaTjeS9nCAqLw0KICAgIGhhbmRsZUltcG9ydCgpIHsNCiAgICAgIHRoaXMudXBsb2FkLnRpdGxlID0gIueUqOaIt+WvvOWFpSI7DQogICAgICB0aGlzLnVwbG9hZC5vcGVuID0gdHJ1ZTsNCiAgICB9LA0KICAgIC8qKiDkuIvovb3mqKHmnb/mk43kvZwgKi8NCiAgICBpbXBvcnRUZW1wbGF0ZSgpIHsNCiAgICAgIHRoaXMuZG93bmxvYWQoDQogICAgICAgICJzeXN0ZW0vdXNlci9pbXBvcnRUZW1wbGF0ZSIsDQogICAgICAgIHt9LA0KICAgICAgICBgdXNlcl90ZW1wbGF0ZV8ke25ldyBEYXRlKCkuZ2V0VGltZSgpfS54bHN4YA0KICAgICAgKTsNCiAgICB9LA0KICAgIC8vIOaWh+S7tuS4iuS8oOS4reWkhOeQhg0KICAgIGhhbmRsZUZpbGVVcGxvYWRQcm9ncmVzcyhldmVudCwgZmlsZSwgZmlsZUxpc3QpIHsNCiAgICAgIHRoaXMudXBsb2FkLmlzVXBsb2FkaW5nID0gdHJ1ZTsNCiAgICB9LA0KICAgIC8vIOaWh+S7tuS4iuS8oOaIkOWKn+WkhOeQhg0KICAgIGhhbmRsZUZpbGVTdWNjZXNzKHJlc3BvbnNlLCBmaWxlLCBmaWxlTGlzdCkgew0KICAgICAgdGhpcy51cGxvYWQub3BlbiA9IGZhbHNlOw0KICAgICAgdGhpcy51cGxvYWQuaXNVcGxvYWRpbmcgPSBmYWxzZTsNCiAgICAgIHRoaXMuJHJlZnMudXBsb2FkLmNsZWFyRmlsZXMoKTsNCiAgICAgIHRoaXMuJGFsZXJ0KA0KICAgICAgICAiPGRpdiBzdHlsZT0nb3ZlcmZsb3c6IGF1dG87b3ZlcmZsb3cteDogaGlkZGVuO21heC1oZWlnaHQ6IDcwdmg7cGFkZGluZzogMTBweCAyMHB4IDA7Jz4iICsNCiAgICAgICAgICByZXNwb25zZS5tc2cgKw0KICAgICAgICAgICI8L2Rpdj4iLA0KICAgICAgICAi5a+85YWl57uT5p6cIiwNCiAgICAgICAgeyBkYW5nZXJvdXNseVVzZUhUTUxTdHJpbmc6IHRydWUgfQ0KICAgICAgKTsNCiAgICAgIHRoaXMuZ2V0TGlzdCgpOw0KICAgIH0sDQogICAgLy8g5o+Q5Lqk5LiK5Lyg5paH5Lu2DQogICAgc3VibWl0RmlsZUZvcm0oKSB7DQogICAgICB0aGlzLiRyZWZzLnVwbG9hZC5zdWJtaXQoKTsNCiAgICB9LA0KICB9LA0KfTsNCg=="}, {"version": 3, "sources": ["index.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAyf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file": "index.vue", "sourceRoot": "src/components/UserSelect", "sourcesContent": ["<template>\r\n  <div class=\"app-container\">\r\n    <el-row :gutter=\"20\">\r\n      <!--部门数据-->\r\n      <el-col :span=\"6\" :xs=\"24\">\r\n        <div class=\"head-container\">\r\n          <el-input\r\n            v-model=\"deptName\"\r\n            placeholder=\"请输入部门名称\"\r\n            clearable\r\n            size=\"small\"\r\n            prefix-icon=\"el-icon-search\"\r\n            style=\"margin-bottom: 20px\"\r\n          />\r\n        </div>\r\n        <div class=\"head-container\">\r\n          <el-tree\r\n            :data=\"deptOptions\"\r\n            :props=\"defaultProps\"\r\n            :expand-on-click-node=\"false\"\r\n            :filter-node-method=\"filterNode\"\r\n            ref=\"tree\"\r\n            node-key=\"id\"\r\n            default-expand-all\r\n            highlight-current\r\n            @node-click=\"handleNodeClick\"\r\n          />\r\n        </div>\r\n      </el-col>\r\n      <!--用户数据-->\r\n      <el-col :span=\"18\" :xs=\"24\">\r\n        <el-form\r\n          :model=\"queryParams\"\r\n          ref=\"queryForm\"\r\n          size=\"small\"\r\n          :inline=\"true\"\r\n          v-show=\"showSearch\"\r\n          label-width=\"68px\"\r\n        >\r\n          <el-form-item label=\"用户名称\" prop=\"userName\">\r\n            <el-input\r\n              v-model=\"queryParams.userName\"\r\n              placeholder=\"请输入用户名称\"\r\n              clearable\r\n              style=\"width: 240px\"\r\n              @keyup.enter.native=\"handleQuery\"\r\n            />\r\n          </el-form-item>\r\n          <el-form-item label=\"手机号码\" prop=\"phonenumber\">\r\n            <el-input\r\n              v-model=\"queryParams.phonenumber\"\r\n              placeholder=\"请输入手机号码\"\r\n              clearable\r\n              style=\"width: 240px\"\r\n              @keyup.enter.native=\"handleQuery\"\r\n            />\r\n          </el-form-item>\r\n          <el-form-item label=\"状态\" prop=\"status\">\r\n            <el-select\r\n              v-model=\"queryParams.status\"\r\n              placeholder=\"用户状态\"\r\n              clearable\r\n              style=\"width: 240px\"\r\n            >\r\n              <el-option\r\n                v-for=\"dict in dict.type.sys_normal_disable\"\r\n                :key=\"dict.value\"\r\n                :label=\"dict.label\"\r\n                :value=\"dict.value\"\r\n              />\r\n            </el-select>\r\n          </el-form-item>\r\n          <!-- <el-form-item label=\"创建时间\">\r\n            <el-date-picker\r\n              v-model=\"dateRange\"\r\n              style=\"width: 240px\"\r\n              value-format=\"yyyy-MM-dd\"\r\n              type=\"daterange\"\r\n              range-separator=\"-\"\r\n              start-placeholder=\"开始日期\"\r\n              end-placeholder=\"结束日期\"\r\n            ></el-date-picker>\r\n          </el-form-item> -->\r\n          <el-form-item>\r\n            <el-button\r\n              type=\"primary\"\r\n              icon=\"el-icon-search\"\r\n              size=\"mini\"\r\n              @click=\"handleQuery\"\r\n              >搜索</el-button\r\n            >\r\n            <el-button icon=\"el-icon-refresh\" size=\"mini\" @click=\"resetQuery\"\r\n              >重置</el-button\r\n            >\r\n          </el-form-item>\r\n        </el-form>\r\n\r\n        <!-- <el-row :gutter=\"10\" class=\"mb8\">\r\n          <el-col :span=\"1.5\">\r\n            <el-button\r\n              type=\"primary\"\r\n              plain\r\n              icon=\"el-icon-plus\"\r\n              size=\"mini\"\r\n              @click=\"handleAdd\"\r\n              v-hasPermi=\"['system:user:add']\"\r\n              >新增</el-button\r\n            >\r\n          </el-col>\r\n          <el-col :span=\"1.5\">\r\n            <el-button\r\n              type=\"primary\"\r\n              plain\r\n              icon=\"el-icon-edit\"\r\n              size=\"mini\"\r\n              :disabled=\"single\"\r\n              @click=\"handleUpdate\"\r\n              v-hasPermi=\"['system:user:edit']\"\r\n              >修改</el-button\r\n            >\r\n          </el-col>\r\n          <el-col :span=\"1.5\">\r\n            <el-button\r\n              type=\"primary\"\r\n              plain\r\n              icon=\"el-icon-delete\"\r\n              size=\"mini\"\r\n              :disabled=\"multiple\"\r\n              @click=\"handleDelete\"\r\n              v-hasPermi=\"['system:user:remove']\"\r\n              >删除</el-button\r\n            >\r\n          </el-col>\r\n          <el-col :span=\"1.5\">\r\n            <el-button\r\n              type=\"primary\"\r\n              plain\r\n              icon=\"el-icon-upload2\"\r\n              size=\"mini\"\r\n              @click=\"handleImport\"\r\n              v-hasPermi=\"['system:user:import']\"\r\n              >导入</el-button\r\n            >\r\n          </el-col>\r\n          <el-col :span=\"1.5\">\r\n            <el-button\r\n              type=\"primary\"\r\n              plain\r\n              icon=\"el-icon-download\"\r\n              size=\"mini\"\r\n              @click=\"handleExport\"\r\n              v-hasPermi=\"['system:user:export']\"\r\n              >导出</el-button\r\n            >\r\n          </el-col>\r\n          <right-toolbar\r\n            :showSearch.sync=\"showSearch\"\r\n            @queryTable=\"getList\"\r\n            :columns=\"columns\"\r\n          ></right-toolbar>\r\n        </el-row> -->\r\n\r\n        <el-table\r\n          v-loading=\"loading\"\r\n          :data=\"userList\"\r\n          ref=\"formTable\"\r\n          :row-key=\"getRowKeys\"\r\n          @selection-change=\"handleSelectionChange\"\r\n        >\r\n          <el-table-column\r\n            type=\"selection\"\r\n            width=\"50\"\r\n            align=\"center\"\r\n            :reserve-selection=\"true\"\r\n          />\r\n          <el-table-column\r\n            label=\"用户编号\"\r\n            align=\"center\"\r\n            key=\"userId\"\r\n            prop=\"userId\"\r\n            v-if=\"columns[0].visible\"\r\n          />\r\n          <!-- <el-table-column\r\n            label=\"用户名称\"\r\n            align=\"center\"\r\n            key=\"userName\"\r\n            prop=\"userName\"\r\n            v-if=\"columns[1].visible\"\r\n            :show-overflow-tooltip=\"true\"\r\n          /> -->\r\n          <el-table-column\r\n            label=\"用户昵称\"\r\n            align=\"center\"\r\n            key=\"nickName\"\r\n            prop=\"nickName\"\r\n            v-if=\"columns[2].visible\"\r\n            :show-overflow-tooltip=\"true\"\r\n          />\r\n          <el-table-column\r\n            label=\"部门\"\r\n            align=\"center\"\r\n            key=\"deptName\"\r\n            prop=\"dept.deptName\"\r\n            v-if=\"columns[3].visible\"\r\n            :show-overflow-tooltip=\"true\"\r\n          />\r\n          <el-table-column\r\n            label=\"手机号码\"\r\n            align=\"center\"\r\n            key=\"phonenumber\"\r\n            prop=\"phonenumber\"\r\n            v-if=\"columns[4].visible\"\r\n            width=\"120\"\r\n          />\r\n          <!-- <el-table-column\r\n            label=\"状态\"\r\n            align=\"center\"\r\n            key=\"status\"\r\n            v-if=\"columns[5].visible\"\r\n          >\r\n            <template slot-scope=\"scope\">\r\n              <el-switch\r\n                v-model=\"scope.row.status\"\r\n                active-value=\"0\"\r\n                inactive-value=\"1\"\r\n                @change=\"handleStatusChange(scope.row)\"\r\n              ></el-switch>\r\n            </template>\r\n          </el-table-column> -->\r\n          <!-- <el-table-column\r\n            label=\"创建时间\"\r\n            align=\"center\"\r\n            prop=\"createTime\"\r\n            v-if=\"columns[6].visible\"\r\n            width=\"160\"\r\n          >\r\n            <template slot-scope=\"scope\">\r\n              <span>{{ parseTime(scope.row.createTime) }}</span>\r\n            </template>\r\n          </el-table-column> -->\r\n          <!-- <el-table-column\r\n            label=\"操作\"\r\n            align=\"center\"\r\n            width=\"160\"\r\n            class-name=\"small-padding fixed-width\"\r\n          >\r\n            <template slot-scope=\"scope\" v-if=\"scope.row.userId !== 1\">\r\n              <el-button\r\n                size=\"mini\"\r\n                type=\"text\"\r\n                icon=\"el-icon-edit\"\r\n                @click=\"handleUpdate(scope.row)\"\r\n                v-hasPermi=\"['system:user:edit']\"\r\n                >修改</el-button\r\n              >\r\n              <el-button\r\n                size=\"mini\"\r\n                type=\"text\"\r\n                icon=\"el-icon-delete\"\r\n                @click=\"handleDelete(scope.row)\"\r\n                v-hasPermi=\"['system:user:remove']\"\r\n                >删除</el-button\r\n              >\r\n              <el-dropdown\r\n                size=\"mini\"\r\n                @command=\"(command) => handleCommand(command, scope.row)\"\r\n                v-hasPermi=\"['system:user:resetPwd', 'system:user:edit']\"\r\n              >\r\n                <el-button size=\"mini\" type=\"text\" icon=\"el-icon-d-arrow-right\"\r\n                  >更多</el-button\r\n                >\r\n                <el-dropdown-menu slot=\"dropdown\">\r\n                  <el-dropdown-item\r\n                    command=\"handleResetPwd\"\r\n                    icon=\"el-icon-key\"\r\n                    v-hasPermi=\"['system:user:resetPwd']\"\r\n                    >重置密码</el-dropdown-item\r\n                  >\r\n                  <el-dropdown-item\r\n                    command=\"handleAuthRole\"\r\n                    icon=\"el-icon-circle-check\"\r\n                    v-hasPermi=\"['system:user:edit']\"\r\n                    >分配角色</el-dropdown-item\r\n                  >\r\n                </el-dropdown-menu>\r\n              </el-dropdown>\r\n            </template>\r\n          </el-table-column> -->\r\n        </el-table>\r\n\r\n        <pagination\r\n          v-show=\"total > 0\"\r\n          :total=\"total\"\r\n          :page.sync=\"queryParams.pageNum\"\r\n          :limit.sync=\"queryParams.pageSize\"\r\n          @pagination=\"getList\"\r\n        />\r\n      </el-col>\r\n    </el-row>\r\n\r\n    <!-- 添加或修改用户配置对话框 -->\r\n    <el-dialog\r\n      :title=\"title\"\r\n      :visible.sync=\"open\"\r\n      width=\"600px\"\r\n      :close-on-click-modal=\"false\"\r\n      append-to-body\r\n    >\r\n      <el-form ref=\"form\" :model=\"form\" :rules=\"rules\" label-width=\"80px\">\r\n        <el-row>\r\n          <el-col :span=\"12\">\r\n            <el-form-item label=\"用户昵称\" prop=\"nickName\">\r\n              <el-input\r\n                v-model=\"form.nickName\"\r\n                placeholder=\"请输入用户昵称\"\r\n                maxlength=\"30\"\r\n              />\r\n            </el-form-item>\r\n          </el-col>\r\n          <el-col :span=\"12\">\r\n            <el-form-item label=\"归属部门\" prop=\"deptId\">\r\n              <treeselect\r\n                v-model=\"form.deptId\"\r\n                :options=\"deptOptions\"\r\n                :show-count=\"true\"\r\n                placeholder=\"请选择归属部门\"\r\n              />\r\n            </el-form-item>\r\n          </el-col>\r\n        </el-row>\r\n        <el-row>\r\n          <el-col :span=\"12\">\r\n            <el-form-item label=\"手机号码\" prop=\"phonenumber\">\r\n              <el-input\r\n                v-model=\"form.phonenumber\"\r\n                placeholder=\"请输入手机号码\"\r\n                maxlength=\"11\"\r\n              />\r\n            </el-form-item>\r\n          </el-col>\r\n          <el-col :span=\"12\">\r\n            <el-form-item label=\"邮箱\" prop=\"email\">\r\n              <el-input\r\n                v-model=\"form.email\"\r\n                placeholder=\"请输入邮箱\"\r\n                maxlength=\"50\"\r\n              />\r\n            </el-form-item>\r\n          </el-col>\r\n        </el-row>\r\n        <el-row>\r\n          <el-col :span=\"12\">\r\n            <el-form-item\r\n              v-if=\"form.userId == undefined\"\r\n              label=\"用户名称\"\r\n              prop=\"userName\"\r\n            >\r\n              <el-input\r\n                v-model=\"form.userName\"\r\n                placeholder=\"请输入用户名称\"\r\n                maxlength=\"30\"\r\n              />\r\n            </el-form-item>\r\n          </el-col>\r\n          <el-col :span=\"12\">\r\n            <el-form-item\r\n              v-if=\"form.userId == undefined\"\r\n              label=\"用户密码\"\r\n              prop=\"password\"\r\n            >\r\n              <el-input\r\n                v-model=\"form.password\"\r\n                placeholder=\"请输入用户密码\"\r\n                type=\"password\"\r\n                maxlength=\"20\"\r\n                show-password\r\n              />\r\n            </el-form-item>\r\n          </el-col>\r\n        </el-row>\r\n        <el-row>\r\n          <el-col :span=\"12\">\r\n            <el-form-item label=\"用户性别\">\r\n              <el-select v-model=\"form.sex\" placeholder=\"请选择性别\">\r\n                <el-option\r\n                  v-for=\"dict in dict.type.sys_user_sex\"\r\n                  :key=\"dict.value\"\r\n                  :label=\"dict.label\"\r\n                  :value=\"dict.value\"\r\n                ></el-option>\r\n              </el-select>\r\n            </el-form-item>\r\n          </el-col>\r\n          <el-col :span=\"12\">\r\n            <el-form-item label=\"状态\">\r\n              <el-radio-group v-model=\"form.status\">\r\n                <el-radio\r\n                  v-for=\"dict in dict.type.sys_normal_disable\"\r\n                  :key=\"dict.value\"\r\n                  :label=\"dict.value\"\r\n                  >{{ dict.label }}</el-radio\r\n                >\r\n              </el-radio-group>\r\n            </el-form-item>\r\n          </el-col>\r\n        </el-row>\r\n        <el-row>\r\n          <el-col :span=\"12\">\r\n            <el-form-item label=\"岗位\">\r\n              <el-select\r\n                v-model=\"form.postIds\"\r\n                multiple\r\n                placeholder=\"请选择岗位\"\r\n              >\r\n                <el-option\r\n                  v-for=\"item in postOptions\"\r\n                  :key=\"item.postId\"\r\n                  :label=\"item.postName\"\r\n                  :value=\"item.postId\"\r\n                  :disabled=\"item.status == 1\"\r\n                ></el-option>\r\n              </el-select>\r\n            </el-form-item>\r\n          </el-col>\r\n          <el-col :span=\"12\">\r\n            <el-form-item label=\"角色\">\r\n              <el-select\r\n                v-model=\"form.roleIds\"\r\n                multiple\r\n                placeholder=\"请选择角色\"\r\n              >\r\n                <el-option\r\n                  v-for=\"item in roleOptions\"\r\n                  :key=\"item.roleId\"\r\n                  :label=\"item.roleName\"\r\n                  :value=\"item.roleId\"\r\n                  :disabled=\"item.status == 1\"\r\n                ></el-option>\r\n              </el-select>\r\n            </el-form-item>\r\n          </el-col>\r\n        </el-row>\r\n        <el-row>\r\n          <el-col :span=\"24\">\r\n            <el-form-item label=\"备注\">\r\n              <el-input\r\n                v-model=\"form.remark\"\r\n                type=\"textarea\"\r\n                placeholder=\"请输入内容\"\r\n              ></el-input>\r\n            </el-form-item>\r\n          </el-col>\r\n        </el-row>\r\n      </el-form>\r\n      <div slot=\"footer\" class=\"dialog-footer\">\r\n        <el-button type=\"primary\" @click=\"submitForm\">确 定</el-button>\r\n        <el-button @click=\"cancel\">取 消</el-button>\r\n      </div>\r\n    </el-dialog>\r\n\r\n    <!-- 用户导入对话框 -->\r\n    <el-dialog\r\n      :title=\"upload.title\"\r\n      :visible.sync=\"upload.open\"\r\n      width=\"400px\"\r\n      append-to-body\r\n    >\r\n      <el-upload\r\n        ref=\"upload\"\r\n        :limit=\"1\"\r\n        accept=\".xlsx, .xls\"\r\n        :headers=\"upload.headers\"\r\n        :action=\"upload.url + '?updateSupport=' + upload.updateSupport\"\r\n        :disabled=\"upload.isUploading\"\r\n        :on-progress=\"handleFileUploadProgress\"\r\n        :on-success=\"handleFileSuccess\"\r\n        :auto-upload=\"false\"\r\n        drag\r\n      >\r\n        <i class=\"el-icon-upload\"></i>\r\n        <div class=\"el-upload__text\">将文件拖到此处，或<em>点击上传</em></div>\r\n        <div class=\"el-upload__tip text-center\" slot=\"tip\">\r\n          <div class=\"el-upload__tip\" slot=\"tip\">\r\n            <el-checkbox v-model=\"upload.updateSupport\" />\r\n            是否更新已经存在的用户数据\r\n          </div>\r\n          <span>仅允许导入xls、xlsx格式文件。</span>\r\n          <el-link\r\n            type=\"primary\"\r\n            :underline=\"false\"\r\n            style=\"font-size: 12px; vertical-align: baseline\"\r\n            @click=\"importTemplate\"\r\n            >下载模板</el-link\r\n          >\r\n        </div>\r\n      </el-upload>\r\n      <div slot=\"footer\" class=\"dialog-footer\">\r\n        <el-button type=\"primary\" @click=\"submitFileForm\">确 定</el-button>\r\n        <el-button @click=\"upload.open = false\">取 消</el-button>\r\n      </div>\r\n    </el-dialog>\r\n  </div>\r\n</template>\r\n  \r\n  <script>\r\nimport {\r\n  listUser,\r\n  getUser,\r\n  delUser,\r\n  addUser,\r\n  updateUser,\r\n  resetUserPwd,\r\n  changeUserStatus,\r\n  deptTreeSelect,\r\n} from \"@/api/system/user\";\r\nimport { getToken } from \"@/utils/auth\";\r\nimport Treeselect from \"@riophae/vue-treeselect\";\r\nimport \"@riophae/vue-treeselect/dist/vue-treeselect.css\";\r\n\r\nexport default {\r\n  name: \"User\",\r\n  dicts: [\"sys_normal_disable\", \"sys_user_sex\"],\r\n  components: { Treeselect },\r\n  props: {\r\n    selectUsers: {\r\n      type: Array,\r\n      default: function () {\r\n        return [];\r\n      },\r\n    },\r\n  },\r\n  watch: {\r\n    selectUsers: {\r\n      handler(val) {\r\n        console.log(\"selectUsers change\", val);\r\n      },\r\n      immediate: true,\r\n      deep: true,\r\n    },\r\n  },\r\n  data() {\r\n    return {\r\n      // 遮罩层\r\n      loading: true,\r\n      // 选中数组\r\n      ids: [],\r\n      // 非单个禁用\r\n      single: true,\r\n      // 非多个禁用\r\n      multiple: true,\r\n      // 显示搜索条件\r\n      showSearch: true,\r\n      // 总条数\r\n      total: 0,\r\n      // 用户表格数据\r\n      userList: null,\r\n      // 弹出层标题\r\n      title: \"\",\r\n      // 部门树选项\r\n      deptOptions: undefined,\r\n      // 是否显示弹出层\r\n      open: false,\r\n      // 部门名称\r\n      deptName: undefined,\r\n      // 默认密码\r\n      initPassword: undefined,\r\n      // 日期范围\r\n      dateRange: [],\r\n      // 岗位选项\r\n      postOptions: [],\r\n      // 角色选项\r\n      roleOptions: [],\r\n      // 表单参数\r\n      form: {},\r\n      defaultProps: {\r\n        children: \"children\",\r\n        label: \"label\",\r\n      },\r\n      // 用户导入参数\r\n      upload: {\r\n        // 是否显示弹出层（用户导入）\r\n        open: false,\r\n        // 弹出层标题（用户导入）\r\n        title: \"\",\r\n        // 是否禁用上传\r\n        isUploading: false,\r\n        // 是否更新已经存在的用户数据\r\n        updateSupport: 0,\r\n        // 设置上传的请求头部\r\n        headers: { Authorization: \"Bearer \" + getToken() },\r\n        // 上传的地址\r\n        url: process.env.VUE_APP_BASE_API + \"/system/user/importData\",\r\n      },\r\n      // 查询参数\r\n      queryParams: {\r\n        pageNum: 1,\r\n        pageSize: 10,\r\n        userName: undefined,\r\n        phonenumber: undefined,\r\n        status: undefined,\r\n        deptId: undefined,\r\n      },\r\n      // 列信息\r\n      columns: [\r\n        { key: 0, label: `用户编号`, visible: true },\r\n        { key: 1, label: `用户名称`, visible: true },\r\n        { key: 2, label: `用户昵称`, visible: true },\r\n        { key: 3, label: `部门`, visible: true },\r\n        { key: 4, label: `手机号码`, visible: true },\r\n        { key: 5, label: `状态`, visible: true },\r\n        { key: 6, label: `创建时间`, visible: true },\r\n      ],\r\n      // 表单校验\r\n      rules: {\r\n        userName: [\r\n          { required: true, message: \"用户名称不能为空\", trigger: \"blur\" },\r\n          {\r\n            min: 2,\r\n            max: 20,\r\n            message: \"用户名称长度必须介于 2 和 20 之间\",\r\n            trigger: \"blur\",\r\n          },\r\n        ],\r\n        nickName: [\r\n          { required: true, message: \"用户昵称不能为空\", trigger: \"blur\" },\r\n        ],\r\n        password: [\r\n          { required: true, message: \"用户密码不能为空\", trigger: \"blur\" },\r\n          {\r\n            min: 5,\r\n            max: 20,\r\n            message: \"用户密码长度必须介于 5 和 20 之间\",\r\n            trigger: \"blur\",\r\n          },\r\n        ],\r\n        email: [\r\n          {\r\n            type: \"email\",\r\n            message: \"请输入正确的邮箱地址\",\r\n            trigger: [\"blur\", \"change\"],\r\n          },\r\n        ],\r\n        phonenumber: [\r\n          {\r\n            pattern: /^1[3|4|5|6|7|8|9][0-9]\\d{8}$/,\r\n            message: \"请输入正确的手机号码\",\r\n            trigger: \"blur\",\r\n          },\r\n        ],\r\n      },\r\n    };\r\n  },\r\n  watch: {\r\n    // 根据名称筛选部门树\r\n    deptName(val) {\r\n      this.$refs.tree.filter(val);\r\n    },\r\n    selectUsers(val) {\r\n      this.updateTableRowSel(val);\r\n    },\r\n  },\r\n  created() {\r\n    this.getList();\r\n    this.getDeptTree();\r\n    this.getConfigKey(\"sys.user.initPassword\").then((response) => {\r\n      this.initPassword = response.msg;\r\n    });\r\n  },\r\n  methods: {\r\n    getRowKeys(val) {\r\n      return val.userId;\r\n    },\r\n    updateTableRowSel(user) {\r\n      //变更选择\r\n      if (this.$refs.formTable) {\r\n        console.log(\"变更选择\", user);\r\n        this.$nextTick(() => {\r\n          this.$refs.formTable.toggleRowSelection(user[0], false);\r\n        });\r\n      }\r\n\r\n      //   if (this.$refs.formTable) this.$refs.formTable.clearSelection();\r\n      //   this.$nextTick(() => {\r\n      //     userList.forEach((key) => {\r\n      //       users.forEach((user) => {\r\n      //         if (this.$refs.formTable) {\r\n      //           if (user.id === key.id) {\r\n      //             console.log(\"选中1\", key, user);\r\n      //             this.$refs.formTable.toggleRowSelection(user, true);\r\n      //           }\r\n      //         }\r\n      //       });\r\n      //     });\r\n      //   });\r\n    },\r\n    /** 查询用户列表 */\r\n    getList() {\r\n      this.loading = true;\r\n      listUser(this.addDateRange(this.queryParams, this.dateRange)).then(\r\n        (response) => {\r\n          this.userList = response.rows;\r\n          this.total = response.total;\r\n          this.loading = false;\r\n        }\r\n      );\r\n    },\r\n    /** 查询部门下拉树结构 */\r\n    getDeptTree() {\r\n      deptTreeSelect().then((response) => {\r\n        this.deptOptions = response.data;\r\n      });\r\n    },\r\n    // 筛选节点\r\n    filterNode(value, data) {\r\n      if (!value) return true;\r\n      return data.label.indexOf(value) !== -1;\r\n    },\r\n    // 节点单击事件\r\n    handleNodeClick(data) {\r\n      this.queryParams.deptId = data.id;\r\n      this.handleQuery();\r\n    },\r\n    // 用户状态修改\r\n    handleStatusChange(row) {\r\n      let text = row.status === \"0\" ? \"启用\" : \"停用\";\r\n      this.$modal\r\n        .confirm('确认要\"' + text + '\"\"' + row.userName + '\"用户吗？')\r\n        .then(function () {\r\n          return changeUserStatus(row.userId, row.status);\r\n        })\r\n        .then(() => {\r\n          this.$modal.msgSuccess(text + \"成功\");\r\n        })\r\n        .catch(function () {\r\n          row.status = row.status === \"0\" ? \"1\" : \"0\";\r\n        });\r\n    },\r\n    // 取消按钮\r\n    cancel() {\r\n      this.open = false;\r\n      this.reset();\r\n    },\r\n    // 表单重置\r\n    reset() {\r\n      this.form = {\r\n        userId: undefined,\r\n        deptId: undefined,\r\n        userName: undefined,\r\n        nickName: undefined,\r\n        password: undefined,\r\n        phonenumber: undefined,\r\n        email: undefined,\r\n        sex: undefined,\r\n        status: \"0\",\r\n        remark: undefined,\r\n        postIds: [],\r\n        roleIds: [],\r\n      };\r\n      this.resetForm(\"form\");\r\n    },\r\n    /** 搜索按钮操作 */\r\n    handleQuery() {\r\n      this.queryParams.pageNum = 1;\r\n      this.getList();\r\n    },\r\n    /** 重置按钮操作 */\r\n    resetQuery() {\r\n      this.dateRange = [];\r\n      this.resetForm(\"queryForm\");\r\n      this.queryParams.deptId = undefined;\r\n      this.$refs.tree.setCurrentKey(null);\r\n      this.handleQuery();\r\n    },\r\n    // 多选框选中数据\r\n    handleSelectionChange(selection) {\r\n      this.ids = selection.map((item) => item.userId);\r\n      this.single = selection.length != 1;\r\n      this.multiple = !selection.length;\r\n      console.log(\"selection\", selection);\r\n      this.$emit(\"selectChange\", selection);\r\n    },\r\n    // 更多操作触发\r\n    handleCommand(command, row) {\r\n      switch (command) {\r\n        case \"handleResetPwd\":\r\n          this.handleResetPwd(row);\r\n          break;\r\n        case \"handleAuthRole\":\r\n          this.handleAuthRole(row);\r\n          break;\r\n        default:\r\n          break;\r\n      }\r\n    },\r\n    /** 新增按钮操作 */\r\n    handleAdd() {\r\n      this.reset();\r\n      getUser().then((response) => {\r\n        this.postOptions = response.posts;\r\n        this.roleOptions = response.roles;\r\n        this.open = true;\r\n        this.title = \"添加用户\";\r\n        this.form.password = this.initPassword;\r\n      });\r\n    },\r\n    /** 修改按钮操作 */\r\n    handleUpdate(row) {\r\n      this.reset();\r\n      const userId = row.userId || this.ids;\r\n      getUser(userId).then((response) => {\r\n        this.form = response.data;\r\n        this.postOptions = response.posts;\r\n        this.roleOptions = response.roles;\r\n        this.$set(this.form, \"postIds\", response.postIds);\r\n        this.$set(this.form, \"roleIds\", response.roleIds);\r\n        this.open = true;\r\n        this.title = \"修改用户\";\r\n        this.form.password = \"\";\r\n      });\r\n    },\r\n    /** 重置密码按钮操作 */\r\n    handleResetPwd(row) {\r\n      this.$prompt('请输入\"' + row.userName + '\"的新密码', \"提示\", {\r\n        confirmButtonText: \"确定\",\r\n        cancelButtonText: \"取消\",\r\n        closeOnClickModal: false,\r\n        inputPattern: /^.{5,20}$/,\r\n        inputErrorMessage: \"用户密码长度必须介于 5 和 20 之间\",\r\n      })\r\n        .then(({ value }) => {\r\n          resetUserPwd(row.userId, value).then((response) => {\r\n            this.$modal.msgSuccess(\"修改成功，新密码是：\" + value);\r\n          });\r\n        })\r\n        .catch(() => {});\r\n    },\r\n    /** 分配角色操作 */\r\n    handleAuthRole: function (row) {\r\n      const userId = row.userId;\r\n      this.$router.push(\"/system/user-auth/role/\" + userId);\r\n    },\r\n    /** 提交按钮 */\r\n    submitForm: function () {\r\n      this.$refs[\"form\"].validate((valid) => {\r\n        if (valid) {\r\n          if (this.form.userId != undefined) {\r\n            updateUser(this.form).then((response) => {\r\n              this.$modal.msgSuccess(\"修改成功\");\r\n              this.open = false;\r\n              this.getList();\r\n            });\r\n          } else {\r\n            addUser(this.form).then((response) => {\r\n              this.$modal.msgSuccess(\"新增成功\");\r\n              this.open = false;\r\n              this.getList();\r\n            });\r\n          }\r\n        }\r\n      });\r\n    },\r\n    /** 删除按钮操作 */\r\n    handleDelete(row) {\r\n      const userIds = row.userId || this.ids;\r\n      this.$modal\r\n        .confirm('是否确认删除用户编号为\"' + userIds + '\"的数据项？')\r\n        .then(function () {\r\n          return delUser(userIds);\r\n        })\r\n        .then(() => {\r\n          this.getList();\r\n          this.$modal.msgSuccess(\"删除成功\");\r\n        })\r\n        .catch(() => {});\r\n    },\r\n    /** 导出按钮操作 */\r\n    handleExport() {\r\n      this.download(\r\n        \"system/user/export\",\r\n        {\r\n          ...this.queryParams,\r\n        },\r\n        `user_${new Date().getTime()}.xlsx`\r\n      );\r\n    },\r\n    /** 导入按钮操作 */\r\n    handleImport() {\r\n      this.upload.title = \"用户导入\";\r\n      this.upload.open = true;\r\n    },\r\n    /** 下载模板操作 */\r\n    importTemplate() {\r\n      this.download(\r\n        \"system/user/importTemplate\",\r\n        {},\r\n        `user_template_${new Date().getTime()}.xlsx`\r\n      );\r\n    },\r\n    // 文件上传中处理\r\n    handleFileUploadProgress(event, file, fileList) {\r\n      this.upload.isUploading = true;\r\n    },\r\n    // 文件上传成功处理\r\n    handleFileSuccess(response, file, fileList) {\r\n      this.upload.open = false;\r\n      this.upload.isUploading = false;\r\n      this.$refs.upload.clearFiles();\r\n      this.$alert(\r\n        \"<div style='overflow: auto;overflow-x: hidden;max-height: 70vh;padding: 10px 20px 0;'>\" +\r\n          response.msg +\r\n          \"</div>\",\r\n        \"导入结果\",\r\n        { dangerouslyUseHTMLString: true }\r\n      );\r\n      this.getList();\r\n    },\r\n    // 提交上传文件\r\n    submitFileForm() {\r\n      this.$refs.upload.submit();\r\n    },\r\n  },\r\n};\r\n</script>"]}]}