{"remainingRequest": "D:\\shangchen\\casz-fat-j21\\casz-fat-ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\shangchen\\casz-fat-j21\\casz-fat-ui\\src\\components\\menu\\index.vue?vue&type=script&lang=js", "dependencies": [{"path": "D:\\shangchen\\casz-fat-j21\\casz-fat-ui\\src\\components\\menu\\index.vue", "mtime": 1752668935190}, {"path": "D:\\shangchen\\casz-fat-j21\\casz-fat-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1747273083419}, {"path": "D:\\shangchen\\casz-fat-j21\\casz-fat-ui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1747273094151}, {"path": "D:\\shangchen\\casz-fat-j21\\casz-fat-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1747273083419}, {"path": "D:\\shangchen\\casz-fat-j21\\casz-fat-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1747273098384}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:Ly8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KDQppbXBvcnQgeyBnZXRTREsgfSBmcm9tICdvcGVuLWltLXNkay13YXNtJzsNCmV4cG9ydCBkZWZhdWx0IHsNCiAgcHJvcHM6IHsNCiAgICB1c2VySW5mbzogew0KICAgICAgdHlwZTogT2JqZWN0LA0KICAgICAgZGVmYXVsdDogKCkgPT4gew0KICAgICAgICByZXR1cm4ge30NCiAgICAgIH0NCiAgICB9DQogIH0sDQogIGRhdGEoKSB7DQoNCiAgICByZXR1cm4gew0KICAgICAgbWVudURhdGE6IHsNCiAgICAgICAgLy8gaWNvbkFycjogWydlbC1pY29uLWNoYXQtcm91bmQnLCAnZWwtaWNvbi11c2VyJywgImVsLWljb24tY29sbGVjdGlvbiIsICJlbC1pY29uLWZvbGRlciIsDQogICAgICAgIC8vICAgImVsLWljb24taGVscCIsICJlbC1pY29uLWxpbmsiLCAiZWwtaWNvbi1tb2JpbGUtcGhvbmUiLCAiZWwtaWNvbi1zZXR0aW5nIl0sDQogICAgICAgIC8vIHBhdGhBcnI6IFsnL2NoYXQnLCAnL2ZyaWVuZCcsICcvJywgJy8nLA0KICAgICAgICAvLyAgICcvJywgJy8nLCAnLycsICcvbG9naW4nXSwNCiAgICAgICAgaWNvbkFycjogWydlbC1pY29uLWNoYXQtcm91bmQnLCAnZWwtaWNvbi11c2VyJ10sDQogICAgICAgIHBhdGhBcnI6IFsnL2NoYXQnLCAnL2ZyaWVuZCddLA0KICAgICAgICBJTVNESzogbnVsbCwNCg0KICAgICAgfQ0KICAgIH0NCiAgfSwNCiAgY3JlYXRlZCgpIHsNCiAgICB0aGlzLklNU0RLID0gZ2V0U0RLKCk7DQoNCiAgfSwNCiAgbWV0aG9kczogew0KDQogICAganVtcFBhZ2UocGF0aCwgY2xhc3NOYW1lKSB7DQogICAgICB0aGlzLiRlbWl0KCdqdW1wUGFnZScsIHBhdGgpDQogICAgICAvLyBBcnJheS5wcm90b3R5cGUuZm9yRWFjaC5jYWxsKGRvY3VtZW50LmdldEVsZW1lbnRzQnlDbGFzc05hbWUoJ21lbnVJY29uJyksKGVsKT0+ew0KICAgICAgLy8gICBlbC5jbGFzc0xpc3QucmVtb3ZlKCdpcy1jaGFjaycpDQogICAgICAvLyB9KQ0KICAgICAgLy8gZG9jdW1lbnQuZ2V0RWxlbWVudHNCeUNsYXNzTmFtZShjbGFzc05hbWUpWzBdLmNsYXNzTGlzdC5hZGQoJ2lzLWNoYWNrJykNCg0KDQogICAgICAvLyBpZiAocGF0aCA9PSAnL2xvZ2luJykgew0KICAgICAgLy8gICBsb2NhbFN0b3JhZ2UuY2xlYXIoKTsNCiAgICAgIC8vICAgdGhpcy4kc3RvcmUuY29tbWl0KCdzZXRDaGF0VXNlckluZm8nLCB7DQogICAgICAvLyAgICAgaXNMb2dpbjogZmFsc2UsDQogICAgICAvLyAgICAgdXNlcm5hbWU6ICcnLA0KICAgICAgLy8gICAgIGFjY291bnQ6ICcnLA0KICAgICAgLy8gICB9KQ0KICAgICAgLy8gfQ0KICAgICAgLy8gdGhpcy4kcm91dGVyLnB1c2gocGF0aCkNCiAgICB9LA0KICB9DQp9DQo="}, {"version": 3, "sources": ["index.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;AAgBA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;;AAGA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "index.vue", "sourceRoot": "src/components/menu", "sourcesContent": ["<template>\r\n  <!-- 功能区 -->\r\n  <div class=\"ribbon h flex vcenter column\">\r\n    <!-- 头像 -->\r\n    <div class=\"portrait w flex lcenter vcenter column\" style=\"margin: 70% 0 30% 0;\">\r\n      <el-avatar shape=\"square\" size=\"medium\" :src=\"userInfo.faceURL\" icon=\"el-icon-user-solid\"></el-avatar>\r\n    </div>\r\n    <!-- 常用功能 -->\r\n    <div class=\"function w flex vcenter column ribbonIconStyle\" style=\"flex:1;\">\r\n      <i v-for=\"(item, index) in menuData.iconArr\" :key=\"index\" :class=\"item\" class=\"pointer menuIcon\"\r\n        @click=\"jumpPage(menuData.pathArr[index], item)\"></i>\r\n    </div>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport { getSDK } from 'open-im-sdk-wasm';\r\nexport default {\r\n  props: {\r\n    userInfo: {\r\n      type: Object,\r\n      default: () => {\r\n        return {}\r\n      }\r\n    }\r\n  },\r\n  data() {\r\n\r\n    return {\r\n      menuData: {\r\n        // iconArr: ['el-icon-chat-round', 'el-icon-user', \"el-icon-collection\", \"el-icon-folder\",\r\n        //   \"el-icon-help\", \"el-icon-link\", \"el-icon-mobile-phone\", \"el-icon-setting\"],\r\n        // pathArr: ['/chat', '/friend', '/', '/',\r\n        //   '/', '/', '/', '/login'],\r\n        iconArr: ['el-icon-chat-round', 'el-icon-user'],\r\n        pathArr: ['/chat', '/friend'],\r\n        IMSDK: null,\r\n\r\n      }\r\n    }\r\n  },\r\n  created() {\r\n    this.IMSDK = getSDK();\r\n\r\n  },\r\n  methods: {\r\n\r\n    jumpPage(path, className) {\r\n      this.$emit('jumpPage', path)\r\n      // Array.prototype.forEach.call(document.getElementsByClassName('menuIcon'),(el)=>{\r\n      //   el.classList.remove('is-chack')\r\n      // })\r\n      // document.getElementsByClassName(className)[0].classList.add('is-chack')\r\n\r\n\r\n      // if (path == '/login') {\r\n      //   localStorage.clear();\r\n      //   this.$store.commit('setChatUserInfo', {\r\n      //     isLogin: false,\r\n      //     username: '',\r\n      //     account: '',\r\n      //   })\r\n      // }\r\n      // this.$router.push(path)\r\n    },\r\n  }\r\n}\r\n</script>\r\n\r\n<style scoped>\r\n.ribbon {\r\n  width: 6.5%;\r\n  background-color: rgb(46, 46, 46, 0.9);\r\n}\r\n\r\n.content {\r\n  width: 94%;\r\n}\r\n\r\n.ribbonIconStyle {\r\n  color: rgb(147, 147, 147, 0.8);\r\n  font-size: 23px;\r\n}\r\n\r\n.menuIcon {\r\n  margin-bottom: 40%;\r\n}\r\n\r\n.menuIcon:hover {\r\n  color: rgba(192, 192, 192, 0.9);\r\n}\r\n\r\n.is-chack {\r\n  color: rgba(7, 193, 96, 0.9);\r\n}\r\n\r\n</style>"]}]}