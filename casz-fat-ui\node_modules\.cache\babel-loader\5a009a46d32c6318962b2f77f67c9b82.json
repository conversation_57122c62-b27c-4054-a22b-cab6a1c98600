{"remainingRequest": "D:\\shangchen\\casz-fat-j21\\casz-fat-ui\\node_modules\\babel-loader\\lib\\index.js!D:\\shangchen\\casz-fat-j21\\casz-fat-ui\\node_modules\\eslint-loader\\index.js??ref--13-0!D:\\shangchen\\casz-fat-j21\\casz-fat-ui\\src\\api\\consul\\consultation.js", "dependencies": [{"path": "D:\\shangchen\\casz-fat-j21\\casz-fat-ui\\src\\api\\consul\\consultation.js", "mtime": 1752668934320}, {"path": "D:\\shangchen\\casz-fat-j21\\casz-fat-ui\\babel.config.js", "mtime": 1752668935560}, {"path": "D:\\shangchen\\casz-fat-j21\\casz-fat-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1747273083419}, {"path": "D:\\shangchen\\casz-fat-j21\\casz-fat-ui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1747273094151}, {"path": "D:\\shangchen\\casz-fat-j21\\casz-fat-ui\\node_modules\\eslint-loader\\index.js", "mtime": 1747273087917}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:InVzZSBzdHJpY3QiOwoKdmFyIF9pbnRlcm9wUmVxdWlyZURlZmF1bHQgPSByZXF1aXJlKCJEOi9zaGFuZ2NoZW4vY2Fzei1mYXQtajIxL2Nhc3otZmF0LXVpL25vZGVfbW9kdWxlcy9AYmFiZWwvcnVudGltZS9oZWxwZXJzL2ludGVyb3BSZXF1aXJlRGVmYXVsdC5qcyIpLmRlZmF1bHQ7Ck9iamVjdC5kZWZpbmVQcm9wZXJ0eShleHBvcnRzLCAiX19lc01vZHVsZSIsIHsKICB2YWx1ZTogdHJ1ZQp9KTsKZXhwb3J0cy5hZGRDb25zdWx0YXRpb24gPSBhZGRDb25zdWx0YXRpb247CmV4cG9ydHMuZGVsQ29uc3VsdGF0aW9uID0gZGVsQ29uc3VsdGF0aW9uOwpleHBvcnRzLmdldENvbnN1bHRhdGlvbiA9IGdldENvbnN1bHRhdGlvbjsKZXhwb3J0cy5saXN0Q29uc3VsdGF0aW9uID0gbGlzdENvbnN1bHRhdGlvbjsKZXhwb3J0cy51cGRhdGVDb25zdWx0YXRpb24gPSB1cGRhdGVDb25zdWx0YXRpb247CnZhciBfcmVxdWVzdCA9IF9pbnRlcm9wUmVxdWlyZURlZmF1bHQocmVxdWlyZSgiQC91dGlscy9yZXF1ZXN0IikpOwovLyDmn6Xor6LkvJror4rnrqHnkIbliJfooagKZnVuY3Rpb24gbGlzdENvbnN1bHRhdGlvbihxdWVyeSkgewogIHJldHVybiAoMCwgX3JlcXVlc3QuZGVmYXVsdCkoewogICAgdXJsOiAnL2NvbnN1bC9jb25zdWx0YXRpb24vbGlzdCcsCiAgICBtZXRob2Q6ICdnZXQnLAogICAgcGFyYW1zOiBxdWVyeQogIH0pOwp9CgovLyDmn6Xor6LkvJror4rnrqHnkIbor6bnu4YKZnVuY3Rpb24gZ2V0Q29uc3VsdGF0aW9uKGlkKSB7CiAgcmV0dXJuICgwLCBfcmVxdWVzdC5kZWZhdWx0KSh7CiAgICB1cmw6ICcvY29uc3VsL2NvbnN1bHRhdGlvbi8nICsgaWQsCiAgICBtZXRob2Q6ICdnZXQnCiAgfSk7Cn0KCi8vIOaWsOWinuS8muiviueuoeeQhgpmdW5jdGlvbiBhZGRDb25zdWx0YXRpb24oZGF0YSkgewogIHJldHVybiAoMCwgX3JlcXVlc3QuZGVmYXVsdCkoewogICAgdXJsOiAnL2NvbnN1bC9jb25zdWx0YXRpb24nLAogICAgbWV0aG9kOiAncG9zdCcsCiAgICBkYXRhOiBkYXRhCiAgfSk7Cn0KCi8vIOS/ruaUueS8muiviueuoeeQhgpmdW5jdGlvbiB1cGRhdGVDb25zdWx0YXRpb24oZGF0YSkgewogIHJldHVybiAoMCwgX3JlcXVlc3QuZGVmYXVsdCkoewogICAgdXJsOiAnL2NvbnN1bC9jb25zdWx0YXRpb24nLAogICAgbWV0aG9kOiAncHV0JywKICAgIGRhdGE6IGRhdGEKICB9KTsKfQoKLy8g5Yig6Zmk5Lya6K+K566h55CGCmZ1bmN0aW9uIGRlbENvbnN1bHRhdGlvbihpZCkgewogIHJldHVybiAoMCwgX3JlcXVlc3QuZGVmYXVsdCkoewogICAgdXJsOiAnL2NvbnN1bC9jb25zdWx0YXRpb24vJyArIGlkLAogICAgbWV0aG9kOiAnZGVsZXRlJwogIH0pOwp9"}, {"version": 3, "names": ["_request", "_interopRequireDefault", "require", "listConsultation", "query", "request", "url", "method", "params", "getConsultation", "id", "addConsultation", "data", "updateConsultation", "delConsultation"], "sources": ["D:/shangchen/casz-fat-j21/casz-fat-ui/src/api/consul/consultation.js"], "sourcesContent": ["import request from '@/utils/request'\r\n\r\n// 查询会诊管理列表\r\nexport function listConsultation(query) {\r\n  return request({\r\n    url: '/consul/consultation/list',\r\n    method: 'get',\r\n    params: query\r\n  })\r\n}\r\n\r\n// 查询会诊管理详细\r\nexport function getConsultation(id) {\r\n  return request({\r\n    url: '/consul/consultation/' + id,\r\n    method: 'get'\r\n  })\r\n}\r\n\r\n// 新增会诊管理\r\nexport function addConsultation(data) {\r\n  return request({\r\n    url: '/consul/consultation',\r\n    method: 'post',\r\n    data: data\r\n  })\r\n}\r\n\r\n// 修改会诊管理\r\nexport function updateConsultation(data) {\r\n  return request({\r\n    url: '/consul/consultation',\r\n    method: 'put',\r\n    data: data\r\n  })\r\n}\r\n\r\n// 删除会诊管理\r\nexport function delConsultation(id) {\r\n  return request({\r\n    url: '/consul/consultation/' + id,\r\n    method: 'delete'\r\n  })\r\n}\r\n"], "mappings": ";;;;;;;;;;;AAAA,IAAAA,QAAA,GAAAC,sBAAA,CAAAC,OAAA;AAEA;AACO,SAASC,gBAAgBA,CAACC,KAAK,EAAE;EACtC,OAAO,IAAAC,gBAAO,EAAC;IACbC,GAAG,EAAE,2BAA2B;IAChCC,MAAM,EAAE,KAAK;IACbC,MAAM,EAAEJ;EACV,CAAC,CAAC;AACJ;;AAEA;AACO,SAASK,eAAeA,CAACC,EAAE,EAAE;EAClC,OAAO,IAAAL,gBAAO,EAAC;IACbC,GAAG,EAAE,uBAAuB,GAAGI,EAAE;IACjCH,MAAM,EAAE;EACV,CAAC,CAAC;AACJ;;AAEA;AACO,SAASI,eAAeA,CAACC,IAAI,EAAE;EACpC,OAAO,IAAAP,gBAAO,EAAC;IACbC,GAAG,EAAE,sBAAsB;IAC3BC,MAAM,EAAE,MAAM;IACdK,IAAI,EAAEA;EACR,CAAC,CAAC;AACJ;;AAEA;AACO,SAASC,kBAAkBA,CAACD,IAAI,EAAE;EACvC,OAAO,IAAAP,gBAAO,EAAC;IACbC,GAAG,EAAE,sBAAsB;IAC3BC,MAAM,EAAE,KAAK;IACbK,IAAI,EAAEA;EACR,CAAC,CAAC;AACJ;;AAEA;AACO,SAASE,eAAeA,CAACJ,EAAE,EAAE;EAClC,OAAO,IAAAL,gBAAO,EAAC;IACbC,GAAG,EAAE,uBAAuB,GAAGI,EAAE;IACjCH,MAAM,EAAE;EACV,CAAC,CAAC;AACJ", "ignoreList": []}]}