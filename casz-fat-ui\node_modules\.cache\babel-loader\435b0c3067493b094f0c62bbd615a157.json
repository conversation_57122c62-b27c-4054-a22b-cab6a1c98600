{"remainingRequest": "D:\\shangchen\\casz-fat-j21\\casz-fat-ui\\node_modules\\babel-loader\\lib\\index.js!D:\\shangchen\\casz-fat-j21\\casz-fat-ui\\node_modules\\eslint-loader\\index.js??ref--13-0!D:\\shangchen\\casz-fat-j21\\casz-fat-ui\\src\\api\\exchange\\imUser.js", "dependencies": [{"path": "D:\\shangchen\\casz-fat-j21\\casz-fat-ui\\src\\api\\exchange\\imUser.js", "mtime": 1753693669791}, {"path": "D:\\shangchen\\casz-fat-j21\\casz-fat-ui\\babel.config.js", "mtime": 1752668935560}, {"path": "D:\\shangchen\\casz-fat-j21\\casz-fat-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1747273083419}, {"path": "D:\\shangchen\\casz-fat-j21\\casz-fat-ui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1747273094151}, {"path": "D:\\shangchen\\casz-fat-j21\\casz-fat-ui\\node_modules\\eslint-loader\\index.js", "mtime": 1747273087917}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "names": ["_request", "_interopRequireDefault", "require", "listImUser", "query", "request", "url", "method", "params", "getImUser", "userId", "getImUserBySysUserId", "addImUser", "data", "updateImUser", "delImUser", "getFriendList"], "sources": ["D:/shangchen/casz-fat-j21/casz-fat-ui/src/api/exchange/imUser.js"], "sourcesContent": ["import request from '@/utils/request'\r\n\r\n// 查询IM通讯录列表\r\nexport function listImUser(query) {\r\n  return request({\r\n    url: '/exchange/imUser/list',\r\n    method: 'get',\r\n    params: query\r\n  })\r\n}\r\n\r\n// 查询IM通讯录详细\r\nexport function getImUser(userId) {\r\n  return request({\r\n    url: '/exchange/imUser/' + userId,\r\n    method: 'get'\r\n  })\r\n}\r\n\r\nexport function getImUserBySysUserId(userId) {\r\n    return request({\r\n      url: '/exchange/imUser/getImUserBySysUserId/' + userId,\r\n      method: 'get'\r\n    })\r\n  }\r\n\r\n// 新增IM通讯录\r\nexport function addImUser(data) {\r\n  return request({\r\n    url: '/exchange/imUser',\r\n    method: 'post',\r\n    data: data\r\n  })\r\n}\r\n\r\n// 修改IM通讯录\r\nexport function updateImUser(data) {\r\n  return request({\r\n    url: '/exchange/imUser',\r\n    method: 'put',\r\n    data: data\r\n  })\r\n}\r\n\r\n// 删除IM通讯录\r\nexport function delImUser(userId) {\r\n  return request({\r\n    url: '/exchange/imUser/' + userId,\r\n    method: 'delete'\r\n  })\r\n}\r\n\r\n// 获取好友列表\r\nexport function getFriendList(query) {\r\n  return request({\r\n    url: '/exchange/imUser/friend/list',\r\n    method: 'get',\r\n    params: query\r\n  })\r\n}\r\n"], "mappings": ";;;;;;;;;;;;;AAAA,IAAAA,QAAA,GAAAC,sBAAA,CAAAC,OAAA;AAEA;AACO,SAASC,UAAUA,CAACC,KAAK,EAAE;EAChC,OAAO,IAAAC,gBAAO,EAAC;IACbC,GAAG,EAAE,uBAAuB;IAC5BC,MAAM,EAAE,KAAK;IACbC,MAAM,EAAEJ;EACV,CAAC,CAAC;AACJ;;AAEA;AACO,SAASK,SAASA,CAACC,MAAM,EAAE;EAChC,OAAO,IAAAL,gBAAO,EAAC;IACbC,GAAG,EAAE,mBAAmB,GAAGI,MAAM;IACjCH,MAAM,EAAE;EACV,CAAC,CAAC;AACJ;AAEO,SAASI,oBAAoBA,CAACD,MAAM,EAAE;EACzC,OAAO,IAAAL,gBAAO,EAAC;IACbC,GAAG,EAAE,wCAAwC,GAAGI,MAAM;IACtDH,MAAM,EAAE;EACV,CAAC,CAAC;AACJ;;AAEF;AACO,SAASK,SAASA,CAACC,IAAI,EAAE;EAC9B,OAAO,IAAAR,gBAAO,EAAC;IACbC,GAAG,EAAE,kBAAkB;IACvBC,MAAM,EAAE,MAAM;IACdM,IAAI,EAAEA;EACR,CAAC,CAAC;AACJ;;AAEA;AACO,SAASC,YAAYA,CAACD,IAAI,EAAE;EACjC,OAAO,IAAAR,gBAAO,EAAC;IACbC,GAAG,EAAE,kBAAkB;IACvBC,MAAM,EAAE,KAAK;IACbM,IAAI,EAAEA;EACR,CAAC,CAAC;AACJ;;AAEA;AACO,SAASE,SAASA,CAACL,MAAM,EAAE;EAChC,OAAO,IAAAL,gBAAO,EAAC;IACbC,GAAG,EAAE,mBAAmB,GAAGI,MAAM;IACjCH,MAAM,EAAE;EACV,CAAC,CAAC;AACJ;;AAEA;AACO,SAASS,aAAaA,CAACZ,KAAK,EAAE;EACnC,OAAO,IAAAC,gBAAO,EAAC;IACbC,GAAG,EAAE,8BAA8B;IACnCC,MAAM,EAAE,KAAK;IACbC,MAAM,EAAEJ;EACV,CAAC,CAAC;AACJ", "ignoreList": []}]}