{"remainingRequest": "D:\\shangchen\\casz-fat-j21\\casz-fat-ui\\node_modules\\babel-loader\\lib\\index.js!D:\\shangchen\\casz-fat-j21\\casz-fat-ui\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!D:\\shangchen\\casz-fat-j21\\casz-fat-ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\shangchen\\casz-fat-j21\\casz-fat-ui\\src\\components\\ImageJzUpload\\index.vue?vue&type=script&lang=js", "dependencies": [{"path": "D:\\shangchen\\casz-fat-j21\\casz-fat-ui\\src\\components\\ImageJzUpload\\index.vue", "mtime": 1752668935181}, {"path": "D:\\shangchen\\casz-fat-j21\\casz-fat-ui\\babel.config.js", "mtime": 1752668935560}, {"path": "D:\\shangchen\\casz-fat-j21\\casz-fat-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1747273083419}, {"path": "D:\\shangchen\\casz-fat-j21\\casz-fat-ui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1747273094151}, {"path": "D:\\shangchen\\casz-fat-j21\\casz-fat-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1747273083419}, {"path": "D:\\shangchen\\casz-fat-j21\\casz-fat-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1747273098384}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "names": ["_auth", "require", "props", "value", "String", "Object", "Array", "limit", "type", "Number", "default", "fileSize", "fileList", "fileType", "isShowTip", "Boolean", "uploadUrl", "data", "number", "uploadList", "dialogImageUrl", "dialogVisible", "hideUpload", "baseUrl", "process", "env", "VUE_APP_BASE_API", "headers", "Authorization", "getToken", "computed", "showTip", "uploadImgUrl", "methods", "clearFileList", "$emit", "handleBeforeUpload", "file", "isImg", "length", "fileExtension", "name", "lastIndexOf", "slice", "some", "indexOf", "$modal", "msgError", "concat", "join", "isLt", "size", "loading", "handleExceed", "handleUploadSuccess", "res", "code", "push", "fileName", "url", "uploadedSuccessfully", "closeLoading", "msg", "$refs", "imageUpload", "handleRemove", "handleDelete", "findex", "map", "f", "splice", "handleUploadError", "console", "log", "listToString", "handlePictureCardPreview", "list", "separator", "strs", "i", "replace", "substr"], "sources": ["src/components/ImageJzUpload/index.vue"], "sourcesContent": ["<template>\r\n  <div class=\"component-upload-image\">\r\n    <el-upload\r\n      multiple\r\n      :action=\"uploadImgUrl\"\r\n      list-type=\"picture-card\"\r\n      :on-success=\"handleUploadSuccess\"\r\n      :before-upload=\"handleBeforeUpload\"\r\n      :limit=\"limit\"\r\n      :on-error=\"handleUploadError\"\r\n      :on-exceed=\"handleExceed\"\r\n      ref=\"imageUpload\"\r\n      :on-remove=\"handleDelete\"\r\n      :show-file-list=\"true\"\r\n      :headers=\"headers\"\r\n      :file-list=\"fileList\"\r\n      :on-preview=\"handlePictureCardPreview\"\r\n      :class=\"{hide: this.fileList.length >= this.limit}\"\r\n    >\r\n      <i class=\"el-icon-plus\"></i>\r\n    </el-upload>\r\n\r\n    <!-- 上传提示 -->\r\n    <div class=\"el-upload__tip\" slot=\"tip\" v-if=\"showTip\">\r\n      请上传\r\n      <template v-if=\"fileSize\"> 大小不超过 <b style=\"color: #f56c6c\">{{ fileSize }}MB</b> </template>\r\n      <template v-if=\"fileType\"> 格式为 <b style=\"color: #f56c6c\">{{ fileType.join(\"/\") }}</b> </template>\r\n      的文件\r\n    </div>\r\n\r\n    <el-dialog\r\n      :visible.sync=\"dialogVisible\"\r\n      title=\"预览\"\r\n      width=\"800\"\r\n      append-to-body\r\n    >\r\n      <img\r\n        :src=\"dialogImageUrl\"\r\n        style=\"display: block; max-width: 100%; margin: 0 auto\"\r\n      />\r\n    </el-dialog>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport { getToken } from \"@/utils/auth\";\r\n\r\nexport default {\r\n  props: {\r\n    value: [String, Object, Array],\r\n    // 图片数量限制\r\n    limit: {\r\n      type: Number,\r\n      default: 5,\r\n    },\r\n  \r\n    // 大小限制(MB)\r\n    fileSize: {\r\n       type: Number,\r\n      default: 5,\r\n    },\r\n    fileList: {\r\n      type: Array,\r\n      default: () => []\r\n    },\r\n    // 文件类型, 例如['png', 'jpg', 'jpeg']\r\n    fileType: {\r\n      type: Array,\r\n      default: () => [\"png\", \"jpg\", \"jpeg\"],\r\n    },\r\n    // 是否显示提示\r\n    isShowTip: {\r\n      type: Boolean,\r\n      default: true\r\n    },\r\n    uploadUrl:{\r\n      type:String,\r\n      default:\"/common/cosUpload\"\r\n    }\r\n  },\r\n\r\n  data() {\r\n    return {\r\n      number: 0,\r\n      uploadList: [],\r\n      dialogImageUrl: \"\",\r\n      dialogVisible: false,\r\n      hideUpload: false,\r\n      baseUrl: process.env.VUE_APP_BASE_API,\r\n      // uploadImgUrl: process.env.VUE_APP_BASE_API + \"/common/upload\", // 上传的图片服务器地址\r\n      headers: {\r\n        Authorization: \"Bearer \" + getToken(),\r\n      },\r\n    \r\n    };\r\n  },\r\n\r\n\r\n  computed: {\r\n    // 是否显示提示\r\n    showTip() {\r\n      return this.isShowTip && (this.fileType || this.fileSize);\r\n    },\r\n    uploadImgUrl(){\r\n      return process.env.VUE_APP_BASE_API  + this.uploadUrl\r\n    }\r\n  },\r\n  methods: {\r\n    clearFileList() {\r\n      this.fileList = [];\r\n      this.$emit('update:fileList', this.fileList); // 如果使用了 v-model 绑定，可以触发这个事件来更新父组件的绑定值\r\n    },\r\n    // 上传前loading加载\r\n    handleBeforeUpload(file) {\r\n      let isImg = false;\r\n      if (this.fileType.length) {\r\n        let fileExtension = \"\";\r\n        if (file.name.lastIndexOf(\".\") > -1) {\r\n          fileExtension = file.name.slice(file.name.lastIndexOf(\".\") + 1);\r\n        }\r\n        isImg = this.fileType.some(type => {\r\n          if (file.type.indexOf(type) > -1) return true;\r\n          if (fileExtension && fileExtension.indexOf(type) > -1) return true;\r\n          return false;\r\n        });\r\n      } else {\r\n        isImg = file.type.indexOf(\"image\") > -1;\r\n      }\r\n\r\n      if (!isImg) {\r\n        this.$modal.msgError(`文件格式不正确, 请上传${this.fileType.join(\"/\")}图片格式文件!`);\r\n        return false;\r\n      }\r\n      if (this.fileSize) {\r\n        const isLt = file.size / 1024 / 1024 < this.fileSize;\r\n        if (!isLt) {\r\n          this.$modal.msgError(`上传头像图片大小不能超过 ${this.fileSize} MB!`);\r\n          return false;\r\n        }\r\n      }\r\n      this.$modal.loading(\"正在上传图片，请稍候.....\");\r\n      this.number++;\r\n    },\r\n    // 文件个数超出\r\n    handleExceed() {\r\n      this.$modal.msgError(`上传文件数量不能超过 ${this.limit} 个!`);\r\n    },\r\n    // 上传成功回调\r\n    handleUploadSuccess(res, file) {\r\n      if (res.code === 200) {\r\n        if(this.uploadUrl=='/common/cosUpload'){\r\n          this.uploadList.push({ name: res.fileName, url: res.url });\r\n        }else{\r\n          this.uploadList.push({ name: res.fileName, url: res.url });\r\n        }\r\n        this.uploadedSuccessfully();\r\n      } else {\r\n        this.number--;\r\n        this.$modal.closeLoading();\r\n        this.$modal.msgError(res.msg);\r\n        this.$refs.imageUpload.handleRemove(file);\r\n        this.uploadedSuccessfully();\r\n      }\r\n    },\r\n    // 删除图片\r\n    handleDelete(file) {\r\n      const findex = this.fileList.map(f => f.name).indexOf(file.name);\r\n      if (findex > -1) {\r\n        this.fileList.splice(findex, 1);\r\n        this.$emit(\"input\", this.fileList);\r\n      }\r\n    },\r\n    // 上传失败\r\n    handleUploadError() {\r\n      this.$modal.msgError(\"上传图片失败，请重试\");\r\n      this.$modal.closeLoading();\r\n    },\r\n    // 上传结束处理\r\n    uploadedSuccessfully() {\r\n      if (this.number > 0 && this.uploadList.length === this.number) {\r\n        this.fileList = this.fileList.concat(this.uploadList);\r\n        this.uploadList = [];\r\n        this.number = 0;\r\n        console.log(\"@@\",this.uploadList,this.fileList,this.listToString(this.fileList))\r\n        this.$emit(\"input\", this.fileList);\r\n        this.$modal.closeLoading();\r\n\r\n        console.log(\"子组件\",this.fileList)\r\n      }\r\n    },\r\n\r\n    // 预览\r\n    handlePictureCardPreview(file) {\r\n      console.log(\"预览\",file)\r\n      this.dialogImageUrl = file.url;\r\n      this.dialogVisible = true;\r\n    },\r\n    // 对象转成指定字符串分隔\r\n    listToString(list, separator) {\r\n      let strs = \"\";\r\n      separator = separator || \",\";\r\n      for (let i in list) {\r\n        if (list[i].url) {\r\n          strs += list[i].url.replace(this.baseUrl, \"\") + separator;\r\n        }\r\n      }\r\n      return strs != '' ? strs.substr(0, strs.length - 1) : '';\r\n    }\r\n  }\r\n};\r\n</script>\r\n<style scoped lang=\"scss\">\r\n// .el-upload--picture-card 控制加号部分\r\n::v-deep.hide .el-upload--picture-card {\r\n    display: none;\r\n}\r\n// 去掉动画效果\r\n::v-deep .el-list-enter-active,\r\n::v-deep .el-list-leave-active {\r\n    transition: all 0s;\r\n}\r\n\r\n::v-deep .el-list-enter, .el-list-leave-active {\r\n  opacity: 0;\r\n  transform: translateY(0);\r\n}\r\n</style>\r\n\r\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;AA6CA,IAAAA,KAAA,GAAAC,OAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;kCAEA;EACAC,KAAA;IACAC,KAAA,GAAAC,MAAA,EAAAC,MAAA,EAAAC,KAAA;IACA;IACAC,KAAA;MACAC,IAAA,EAAAC,MAAA;MACAC,OAAA;IACA;IAEA;IACAC,QAAA;MACAH,IAAA,EAAAC,MAAA;MACAC,OAAA;IACA;IACAE,QAAA;MACAJ,IAAA,EAAAF,KAAA;MACAI,OAAA,WAAAA,SAAA;QAAA;MAAA;IACA;IACA;IACAG,QAAA;MACAL,IAAA,EAAAF,KAAA;MACAI,OAAA,WAAAA,SAAA;QAAA;MAAA;IACA;IACA;IACAI,SAAA;MACAN,IAAA,EAAAO,OAAA;MACAL,OAAA;IACA;IACAM,SAAA;MACAR,IAAA,EAAAJ,MAAA;MACAM,OAAA;IACA;EACA;EAEAO,IAAA,WAAAA,KAAA;IACA;MACAC,MAAA;MACAC,UAAA;MACAC,cAAA;MACAC,aAAA;MACAC,UAAA;MACAC,OAAA,EAAAC,OAAA,CAAAC,GAAA,CAAAC,gBAAA;MACA;MACAC,OAAA;QACAC,aAAA,kBAAAC,cAAA;MACA;IAEA;EACA;EAGAC,QAAA;IACA;IACAC,OAAA,WAAAA,QAAA;MACA,YAAAjB,SAAA,UAAAD,QAAA,SAAAF,QAAA;IACA;IACAqB,YAAA,WAAAA,aAAA;MACA,OAAAR,OAAA,CAAAC,GAAA,CAAAC,gBAAA,QAAAV,SAAA;IACA;EACA;EACAiB,OAAA;IACAC,aAAA,WAAAA,cAAA;MACA,KAAAtB,QAAA;MACA,KAAAuB,KAAA,yBAAAvB,QAAA;IACA;IACA;IACAwB,kBAAA,WAAAA,mBAAAC,IAAA;MACA,IAAAC,KAAA;MACA,SAAAzB,QAAA,CAAA0B,MAAA;QACA,IAAAC,aAAA;QACA,IAAAH,IAAA,CAAAI,IAAA,CAAAC,WAAA;UACAF,aAAA,GAAAH,IAAA,CAAAI,IAAA,CAAAE,KAAA,CAAAN,IAAA,CAAAI,IAAA,CAAAC,WAAA;QACA;QACAJ,KAAA,QAAAzB,QAAA,CAAA+B,IAAA,WAAApC,IAAA;UACA,IAAA6B,IAAA,CAAA7B,IAAA,CAAAqC,OAAA,CAAArC,IAAA;UACA,IAAAgC,aAAA,IAAAA,aAAA,CAAAK,OAAA,CAAArC,IAAA;UACA;QACA;MACA;QACA8B,KAAA,GAAAD,IAAA,CAAA7B,IAAA,CAAAqC,OAAA;MACA;MAEA,KAAAP,KAAA;QACA,KAAAQ,MAAA,CAAAC,QAAA,kEAAAC,MAAA,MAAAnC,QAAA,CAAAoC,IAAA;QACA;MACA;MACA,SAAAtC,QAAA;QACA,IAAAuC,IAAA,GAAAb,IAAA,CAAAc,IAAA,sBAAAxC,QAAA;QACA,KAAAuC,IAAA;UACA,KAAAJ,MAAA,CAAAC,QAAA,6EAAAC,MAAA,MAAArC,QAAA;UACA;QACA;MACA;MACA,KAAAmC,MAAA,CAAAM,OAAA;MACA,KAAAlC,MAAA;IACA;IACA;IACAmC,YAAA,WAAAA,aAAA;MACA,KAAAP,MAAA,CAAAC,QAAA,iEAAAC,MAAA,MAAAzC,KAAA;IACA;IACA;IACA+C,mBAAA,WAAAA,oBAAAC,GAAA,EAAAlB,IAAA;MACA,IAAAkB,GAAA,CAAAC,IAAA;QACA,SAAAxC,SAAA;UACA,KAAAG,UAAA,CAAAsC,IAAA;YAAAhB,IAAA,EAAAc,GAAA,CAAAG,QAAA;YAAAC,GAAA,EAAAJ,GAAA,CAAAI;UAAA;QACA;UACA,KAAAxC,UAAA,CAAAsC,IAAA;YAAAhB,IAAA,EAAAc,GAAA,CAAAG,QAAA;YAAAC,GAAA,EAAAJ,GAAA,CAAAI;UAAA;QACA;QACA,KAAAC,oBAAA;MACA;QACA,KAAA1C,MAAA;QACA,KAAA4B,MAAA,CAAAe,YAAA;QACA,KAAAf,MAAA,CAAAC,QAAA,CAAAQ,GAAA,CAAAO,GAAA;QACA,KAAAC,KAAA,CAAAC,WAAA,CAAAC,YAAA,CAAA5B,IAAA;QACA,KAAAuB,oBAAA;MACA;IACA;IACA;IACAM,YAAA,WAAAA,aAAA7B,IAAA;MACA,IAAA8B,MAAA,QAAAvD,QAAA,CAAAwD,GAAA,WAAAC,CAAA;QAAA,OAAAA,CAAA,CAAA5B,IAAA;MAAA,GAAAI,OAAA,CAAAR,IAAA,CAAAI,IAAA;MACA,IAAA0B,MAAA;QACA,KAAAvD,QAAA,CAAA0D,MAAA,CAAAH,MAAA;QACA,KAAAhC,KAAA,eAAAvB,QAAA;MACA;IACA;IACA;IACA2D,iBAAA,WAAAA,kBAAA;MACA,KAAAzB,MAAA,CAAAC,QAAA;MACA,KAAAD,MAAA,CAAAe,YAAA;IACA;IACA;IACAD,oBAAA,WAAAA,qBAAA;MACA,SAAA1C,MAAA,aAAAC,UAAA,CAAAoB,MAAA,UAAArB,MAAA;QACA,KAAAN,QAAA,QAAAA,QAAA,CAAAoC,MAAA,MAAA7B,UAAA;QACA,KAAAA,UAAA;QACA,KAAAD,MAAA;QACAsD,OAAA,CAAAC,GAAA,YAAAtD,UAAA,OAAAP,QAAA,OAAA8D,YAAA,MAAA9D,QAAA;QACA,KAAAuB,KAAA,eAAAvB,QAAA;QACA,KAAAkC,MAAA,CAAAe,YAAA;QAEAW,OAAA,CAAAC,GAAA,aAAA7D,QAAA;MACA;IACA;IAEA;IACA+D,wBAAA,WAAAA,yBAAAtC,IAAA;MACAmC,OAAA,CAAAC,GAAA,OAAApC,IAAA;MACA,KAAAjB,cAAA,GAAAiB,IAAA,CAAAsB,GAAA;MACA,KAAAtC,aAAA;IACA;IACA;IACAqD,YAAA,WAAAA,aAAAE,IAAA,EAAAC,SAAA;MACA,IAAAC,IAAA;MACAD,SAAA,GAAAA,SAAA;MACA,SAAAE,CAAA,IAAAH,IAAA;QACA,IAAAA,IAAA,CAAAG,CAAA,EAAApB,GAAA;UACAmB,IAAA,IAAAF,IAAA,CAAAG,CAAA,EAAApB,GAAA,CAAAqB,OAAA,MAAAzD,OAAA,QAAAsD,SAAA;QACA;MACA;MACA,OAAAC,IAAA,SAAAA,IAAA,CAAAG,MAAA,IAAAH,IAAA,CAAAvC,MAAA;IACA;EACA;AACA", "ignoreList": []}]}