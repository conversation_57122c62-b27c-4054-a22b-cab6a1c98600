{"remainingRequest": "D:\\shangchen\\casz-fat-j21\\casz-fat-ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\shangchen\\casz-fat-j21\\casz-fat-ui\\src\\views\\cgm\\device\\index.vue?vue&type=script&lang=js", "dependencies": [{"path": "D:\\shangchen\\casz-fat-j21\\casz-fat-ui\\src\\views\\cgm\\device\\index.vue", "mtime": 1752668935351}, {"path": "D:\\shangchen\\casz-fat-j21\\casz-fat-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1747273083419}, {"path": "D:\\shangchen\\casz-fat-j21\\casz-fat-ui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1747273094151}, {"path": "D:\\shangchen\\casz-fat-j21\\casz-fat-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1747273083419}, {"path": "D:\\shangchen\\casz-fat-j21\\casz-fat-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1747273098384}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:Ly8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KDQppbXBvcnQgew0KICBsaXN0RGV2aWNlLA0KICBnZXREZXZpY2UsDQogIGRlbERldmljZSwNCiAgYWRkRGV2aWNlLA0KICB1cGRhdGVEZXZpY2UsDQogIGJpbmRJZiwNCn0gZnJvbSAiQC9hcGkvY2dtL2RldmljZSI7DQppbXBvcnQgew0KICBsaXN0RXF1aXBtZW50LA0KICBnZXRFcXVpcG1lbnQsDQogIGRlbEVxdWlwbWVudCwNCiAgYWRkRXF1aXBtZW50LA0KICB1cGRhdGVFcXVpcG1lbnQsDQp9IGZyb20gIkAvYXBpL2VxdWlwL2VxdWlwbWVudCI7DQppbXBvcnQgUGF0aWVudElucHV0U2VsZWN0IGZyb20gIkAvY29tcG9uZW50cy9QYXRpZW50SW5wdXRTZWxlY3QiOw0KaW1wb3J0IElucHV0U2VsZWN0IGZyb20gIkAvY29tcG9uZW50cy9JbnB1dFNlbGVjdCI7DQpleHBvcnQgZGVmYXVsdCB7DQogIG5hbWU6ICJEZXZpY2UiLA0KICBkaWN0czogWyJkZXZpY2Vfb3duX3R5cGUiLCAic3lzX25vcm1hbF9kaXNhYmxlIiwgImVxdWlwX3R5cGUiXSwNCiAgY29tcG9uZW50czogeyBQYXRpZW50SW5wdXRTZWxlY3QsIElucHV0U2VsZWN0IH0sDQogIGRhdGEoKSB7DQogICAgcmV0dXJuIHsNCiAgICAgIGRldmljZVR5cGU6ICIwIiwNCiAgICAgIC8vIOmBrue9qeWxgg0KICAgICAgbG9hZGluZzogdHJ1ZSwNCiAgICAgIC8vIOmAieS4reaVsOe7hA0KICAgICAgaWRzOiBbXSwNCiAgICAgIC8v5Y+v6YCJ5oup57uR5a6a55qE6K6+5aSH5YiX6KGoDQogICAgICBkZXZpY2VzOiBbXSwNCiAgICAgIC8vIOmdnuWNleS4quemgeeUqA0KICAgICAgc2luZ2xlOiB0cnVlLA0KICAgICAgLy8g6Z2e5aSa5Liq56aB55SoDQogICAgICBtdWx0aXBsZTogdHJ1ZSwNCiAgICAgIC8vIOaYvuekuuaQnOe0ouadoeS7tg0KICAgICAgc2hvd1NlYXJjaDogdHJ1ZSwNCiAgICAgIC8vIOaAu+adoeaVsA0KICAgICAgdG90YWw6IDAsDQogICAgICAvLyDooYDns5borr7lpIflhbPogZTooajmoLzmlbDmja4NCiAgICAgIGRldmljZUxpc3Q6IFtdLA0KICAgICAgLy8g5by55Ye65bGC5qCH6aKYDQogICAgICB0aXRsZTogIiIsDQogICAgICAvLyDmmK/lkKbmmL7npLrlvLnlh7rlsYINCiAgICAgIG9wZW46IGZhbHNlLA0KICAgICAgLy8g5p+l6K+i5Y+C5pWwDQogICAgICBxdWVyeVBhcmFtczogew0KICAgICAgICBwYWdlTnVtOiAxLA0KICAgICAgICBwYWdlU2l6ZTogMTAsDQogICAgICAgIHBhdGllbnRJZDogbnVsbCwNCiAgICAgICAgcGF0aWVudE5hbWU6IG51bGwsDQogICAgICAgIGNnbURldmljZU5vOiBudWxsLA0KICAgICAgICBiZ21EZXZpY2VObzogbnVsbCwNCiAgICAgICAgY2dtT3duVHlwZTogbnVsbCwNCiAgICAgICAgYmdtT3duVHlwZTogbnVsbCwNCiAgICAgICAgY2dtRW5kVGltZTogbnVsbCwNCiAgICAgICAgYmdtRW5kVGltZTogbnVsbCwNCiAgICAgICAgdGVuYW50SWQ6IG51bGwsDQogICAgICAgIHJldmlzaW9uOiBudWxsLA0KICAgICAgICBjZ21Nb2RlbDogbnVsbCwNCiAgICAgICAgYmdtTW9kZWw6IG51bGwsDQogICAgICAgIGNnbUZhY3Rvcnk6IG51bGwsDQogICAgICAgIGJnbUZhY3Rvcnk6IG51bGwsDQogICAgICAgIGNnbVN0YXR1czogbnVsbCwNCiAgICAgICAgYmdtU3RhdHVzOiBudWxsLA0KICAgICAgICB1bml0SWQ6IG51bGwsDQogICAgICAgIHVuaXROYW1lOiBudWxsLA0KICAgICAgfSwNCiAgICAgIC8vIOihqOWNleWPguaVsA0KICAgICAgZm9ybToge30sDQogICAgICAvLyDooajljZXmoKHpqowNCiAgICAgIHJ1bGVzOiB7fSwNCiAgICAgIGRhdGFMaXN0OiBbXSwNCiAgICB9Ow0KICB9LA0KICBjcmVhdGVkKCkgew0KICAgIHRoaXMuZ2V0TGlzdCgpOw0KICAgIC8v5p+l6K+i5Y+v57uR5a6a55qE6K6+5aSHDQogICAgdGhpcy5nZXRFcXVpcHMoKTsNCiAgfSwNCiAgbWV0aG9kczogew0KICAgIGdldEVxdWlwcygpIHsNCiAgICAgIGxpc3RFcXVpcG1lbnQoeyBwYWdlTnVtOiAxLCBwYWdlU2l6ZTogMTAwMDAwMCB9KS50aGVuKChyZXMpID0+IHsNCiAgICAgICAgY29uc29sZS5sb2coImxpc3RFcXVpcG1lbnQiLCByZXMpOw0KICAgICAgICBpZiAocmVzLmNvZGUgPT0gMjAwKSB7DQogICAgICAgICAgdGhpcy5kYXRhTGlzdCA9IHJlcy5yb3dzOw0KICAgICAgICAgIHRoaXMuZGF0YUxpc3QuZm9yRWFjaCgoaXRlbSkgPT4gew0KICAgICAgICAgICAgdGhpcy5kZXZpY2VzLnB1c2goew0KICAgICAgICAgICAgICB2YWx1ZTogaXRlbS5pZCwNCiAgICAgICAgICAgICAgbGFiZWw6DQogICAgICAgICAgICAgICAgaXRlbS5uYW1lICsNCiAgICAgICAgICAgICAgICAiLyIgKw0KICAgICAgICAgICAgICAgIGl0ZW0uZXF1aXBDb2RlICsNCiAgICAgICAgICAgICAgICAiLyIgKw0KICAgICAgICAgICAgICAgIHRoaXMuc2VsZWN0RGljdExhYmVsKHRoaXMuZGljdC50eXBlLmVxdWlwX3R5cGUsIGl0ZW0udHlwZSksDQogICAgICAgICAgICB9KTsNCiAgICAgICAgICB9KTsNCiAgICAgICAgfQ0KICAgICAgfSk7DQogICAgfSwNCiAgICBkZXZpY2VDaGFuZ2UoZGF0YSkgew0KICAgICAgY29uc29sZS5sb2coImRldmljZUNoYW5nZSIsIGRhdGEpOw0KICAgICAgdGhpcy5mb3JtLmVxdWlwQ29kZSA9IGRhdGEuZXF1aXBDb2RlOw0KICAgICAgdGhpcy5mb3JtLmVxdWlwSWQgPSBkYXRhLmlkOw0KICAgICAgdGhpcy5mb3JtLmlkTm8gPSBkYXRhLmlkTm87DQogICAgfSwNCiAgICBwYXRpZW50Q2hhbmdlKHBhdGllbnQpIHsNCiAgICAgIHRoaXMuZm9ybS5wYXRpZW50SWQgPSBwYXRpZW50LmlkOw0KICAgICAgdGhpcy5mb3JtLnBhdGllbnROYW1lID0gcGF0aWVudC5uYW1lOw0KICAgICAgY29uc29sZS5sb2codGhpcy5mb3JtKTsNCiAgICAgIHRoaXMuY2hlY2tCaW5kKCk7DQogICAgfSwNCiAgICBjaGVja0JpbmQoKSB7DQogICAgICBiaW5kSWYodGhpcy5mb3JtLnBhdGllbnRJZCkudGhlbigocmVzcG9uc2UpID0+IHsNCiAgICAgICAgaWYgKHJlc3BvbnNlLmRhdGEpIHsNCiAgICAgICAgICB0aGlzLiRtb2RhbC5tc2dFcnJvcigi6K+l5oKj6ICF5bey57uR5a6a6K6+5aSHIik7DQogICAgICAgIH0NCiAgICAgIH0pOw0KICAgIH0sDQogICAgLyoqIOafpeivouihgOezluiuvuWkh+WFs+iBlOWIl+ihqCAqLw0KICAgIGdldExpc3QoKSB7DQogICAgICB0aGlzLmxvYWRpbmcgPSB0cnVlOw0KICAgICAgbGlzdERldmljZSh0aGlzLnF1ZXJ5UGFyYW1zKS50aGVuKChyZXNwb25zZSkgPT4gew0KICAgICAgICB0aGlzLmRldmljZUxpc3QgPSByZXNwb25zZS5yb3dzOw0KICAgICAgICB0aGlzLnRvdGFsID0gcmVzcG9uc2UudG90YWw7DQogICAgICAgIHRoaXMubG9hZGluZyA9IGZhbHNlOw0KICAgICAgfSk7DQogICAgfSwNCiAgICAvLyDlj5bmtojmjInpkq4NCiAgICBjYW5jZWwoKSB7DQogICAgICB0aGlzLm9wZW4gPSBmYWxzZTsNCiAgICAgIHRoaXMucmVzZXQoKTsNCiAgICB9LA0KICAgIC8vIOihqOWNlemHjee9rg0KICAgIHJlc2V0KCkgew0KICAgICAgdGhpcy5mb3JtID0gew0KICAgICAgICBpZDogbnVsbCwNCiAgICAgICAgcGF0aWVudElkOiBudWxsLA0KICAgICAgICBwYXRpZW50TmFtZTogbnVsbCwNCiAgICAgICAgY3JlYXRlVGltZTogbnVsbCwNCiAgICAgICAgdXBkYXRlVGltZTogbnVsbCwNCiAgICAgICAgY3JlYXRlQnk6IG51bGwsDQogICAgICAgIHVwZGF0ZUJ5OiBudWxsLA0KICAgICAgICBjZ21EZXZpY2VObzogbnVsbCwNCiAgICAgICAgYmdtRGV2aWNlTm86IG51bGwsDQogICAgICAgIGNnbU93blR5cGU6IG51bGwsDQogICAgICAgIGJnbU93blR5cGU6IG51bGwsDQogICAgICAgIGNnbUVuZFRpbWU6IG51bGwsDQogICAgICAgIGJnbUVuZFRpbWU6IG51bGwsDQogICAgICAgIHRlbmFudElkOiBudWxsLA0KICAgICAgICByZXZpc2lvbjogbnVsbCwNCiAgICAgICAgY2dtTW9kZWw6IG51bGwsDQogICAgICAgIGJnbU1vZGVsOiBudWxsLA0KICAgICAgICBjZ21GYWN0b3J5OiBudWxsLA0KICAgICAgICBiZ21GYWN0b3J5OiBudWxsLA0KICAgICAgICBjZ21TdGF0dXM6IG51bGwsDQogICAgICAgIGJnbVN0YXR1czogbnVsbCwNCiAgICAgICAgdW5pdElkOiBudWxsLA0KICAgICAgICB1bml0TmFtZTogbnVsbCwNCiAgICAgIH07DQogICAgICB0aGlzLnJlc2V0Rm9ybSgiZm9ybSIpOw0KICAgICAgaWYgKHRoaXMuJHJlZnMucGF0aWVudElucHV0U2VsZWN0KSB0aGlzLiRyZWZzLnBhdGllbnRJbnB1dFNlbGVjdC5jbGVhcigpOw0KICAgICAgaWYgKHRoaXMuJHJlZnMuaW5wdXRTZWxlY3QpIHRoaXMuJHJlZnMuaW5wdXRTZWxlY3QuY2xlYXIoKTsNCiAgICB9LA0KICAgIC8qKiDmkJzntKLmjInpkq7mk43kvZwgKi8NCiAgICBoYW5kbGVRdWVyeSgpIHsNCiAgICAgIHRoaXMucXVlcnlQYXJhbXMucGFnZU51bSA9IDE7DQogICAgICB0aGlzLmdldExpc3QoKTsNCiAgICB9LA0KICAgIC8qKiDph43nva7mjInpkq7mk43kvZwgKi8NCiAgICByZXNldFF1ZXJ5KCkgew0KICAgICAgdGhpcy5yZXNldEZvcm0oInF1ZXJ5Rm9ybSIpOw0KICAgICAgdGhpcy5oYW5kbGVRdWVyeSgpOw0KICAgIH0sDQogICAgLy8g5aSa6YCJ5qGG6YCJ5Lit5pWw5o2uDQogICAgaGFuZGxlU2VsZWN0aW9uQ2hhbmdlKHNlbGVjdGlvbikgew0KICAgICAgdGhpcy5pZHMgPSBzZWxlY3Rpb24ubWFwKChpdGVtKSA9PiBpdGVtLmlkKTsNCiAgICAgIHRoaXMuc2luZ2xlID0gc2VsZWN0aW9uLmxlbmd0aCAhPT0gMTsNCiAgICAgIHRoaXMubXVsdGlwbGUgPSAhc2VsZWN0aW9uLmxlbmd0aDsNCiAgICB9LA0KICAgIC8qKiDmlrDlop7mjInpkq7mk43kvZwgKi8NCiAgICBoYW5kbGVBZGQoKSB7DQogICAgICB0aGlzLnJlc2V0KCk7DQogICAgICB0aGlzLm9wZW4gPSB0cnVlOw0KICAgICAgdGhpcy50aXRsZSA9ICLmt7vliqDooYDns5borr7lpIflhbPogZQiOw0KICAgIH0sDQogICAgLyoqIOS/ruaUueaMiemSruaTjeS9nCAqLw0KICAgIGhhbmRsZVVwZGF0ZShyb3cpIHsNCiAgICAgIHRoaXMucmVzZXQoKTsNCiAgICAgIGNvbnN0IGlkID0gcm93LmlkIHx8IHRoaXMuaWRzOw0KICAgICAgZ2V0RGV2aWNlKGlkKS50aGVuKChyZXNwb25zZSkgPT4gew0KICAgICAgICB0aGlzLmZvcm0gPSByZXNwb25zZS5kYXRhOw0KICAgICAgICB0aGlzLm9wZW4gPSB0cnVlOw0KICAgICAgICB0aGlzLnRpdGxlID0gIuS/ruaUueihgOezluiuvuWkh+WFs+iBlCI7DQogICAgICB9KTsNCiAgICB9LA0KICAgIC8qKiDmj5DkuqTmjInpkq4gKi8NCiAgICBzdWJtaXRGb3JtKCkgew0KICAgICAgdGhpcy4kcmVmc1siZm9ybSJdLnZhbGlkYXRlKCh2YWxpZCkgPT4gew0KICAgICAgICBpZiAodmFsaWQpIHsNCiAgICAgICAgICBpZiAodGhpcy5mb3JtLmlkICE9IG51bGwpIHsNCiAgICAgICAgICAgIHVwZGF0ZURldmljZSh0aGlzLmZvcm0pLnRoZW4oKHJlc3BvbnNlKSA9PiB7DQogICAgICAgICAgICAgIHRoaXMuJG1vZGFsLm1zZ1N1Y2Nlc3MoIuS/ruaUueaIkOWKnyIpOw0KICAgICAgICAgICAgICB0aGlzLm9wZW4gPSBmYWxzZTsNCiAgICAgICAgICAgICAgdGhpcy5nZXRMaXN0KCk7DQogICAgICAgICAgICB9KTsNCiAgICAgICAgICB9IGVsc2Ugew0KICAgICAgICAgICAgYWRkRGV2aWNlKHRoaXMuZm9ybSkudGhlbigocmVzcG9uc2UpID0+IHsNCiAgICAgICAgICAgICAgdGhpcy4kbW9kYWwubXNnU3VjY2Vzcygi5paw5aKe5oiQ5YqfIik7DQogICAgICAgICAgICAgIHRoaXMub3BlbiA9IGZhbHNlOw0KICAgICAgICAgICAgICB0aGlzLmdldExpc3QoKTsNCiAgICAgICAgICAgIH0pOw0KICAgICAgICAgIH0NCiAgICAgICAgfQ0KICAgICAgfSk7DQogICAgfSwNCiAgICAvKiog5Yig6Zmk5oyJ6ZKu5pON5L2cICovDQogICAgaGFuZGxlRGVsZXRlKHJvdykgew0KICAgICAgY29uc3QgaWRzID0gcm93LmlkIHx8IHRoaXMuaWRzOw0KICAgICAgdGhpcy4kbW9kYWwNCiAgICAgICAgLmNvbmZpcm0oJ+aYr+WQpuehruiupOWIoOmZpOihgOezluiuvuWkh+WFs+iBlOe8luWPt+S4uiInICsgaWRzICsgJyLnmoTmlbDmja7pobnvvJ8nKQ0KICAgICAgICAudGhlbihmdW5jdGlvbiAoKSB7DQogICAgICAgICAgcmV0dXJuIGRlbERldmljZShpZHMpOw0KICAgICAgICB9KQ0KICAgICAgICAudGhlbigoKSA9PiB7DQogICAgICAgICAgdGhpcy5nZXRMaXN0KCk7DQogICAgICAgICAgdGhpcy4kbW9kYWwubXNnU3VjY2Vzcygi5Yig6Zmk5oiQ5YqfIik7DQogICAgICAgIH0pDQogICAgICAgIC5jYXRjaCgoKSA9PiB7fSk7DQogICAgfSwNCiAgICAvKiog5a+85Ye65oyJ6ZKu5pON5L2cICovDQogICAgaGFuZGxlRXhwb3J0KCkgew0KICAgICAgdGhpcy5kb3dubG9hZCgNCiAgICAgICAgImNnbS9kZXZpY2UvZXhwb3J0IiwNCiAgICAgICAgew0KICAgICAgICAgIC4uLnRoaXMucXVlcnlQYXJhbXMsDQogICAgICAgIH0sDQogICAgICAgIGBkZXZpY2VfJHtuZXcgRGF0ZSgpLmdldFRpbWUoKX0ueGxzeGANCiAgICAgICk7DQogICAgfSwNCiAgfSwNCn07DQo="}, {"version": 3, "sources": ["index.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA0jBA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "index.vue", "sourceRoot": "src/views/cgm/device", "sourcesContent": ["<template>\r\n  <div class=\"app-container\">\r\n    <el-form\r\n      :model=\"queryParams\"\r\n      ref=\"queryForm\"\r\n      size=\"small\"\r\n      :inline=\"true\"\r\n      v-show=\"showSearch\"\r\n      label-width=\"180px\"\r\n    >\r\n      <!-- <el-form-item label=\"患者ID\" prop=\"patientId\">\r\n        <el-input\r\n          v-model=\"queryParams.patientId\"\r\n          placeholder=\"请输入患者ID\"\r\n          clearable\r\n          @keyup.enter.native=\"handleQuery\"\r\n        />\r\n      </el-form-item> -->\r\n      <el-form-item label=\"患者姓名\" prop=\"patientName\">\r\n        <el-input\r\n          v-model=\"queryParams.patientName\"\r\n          placeholder=\"请输入患者姓名\"\r\n          clearable\r\n          @keyup.enter.native=\"handleQuery\"\r\n        />\r\n      </el-form-item>\r\n      <el-form-item label=\"设备号\" prop=\"equipCode\">\r\n        <el-input\r\n          v-model=\"queryParams.cgmDeviceNo\"\r\n          placeholder=\"请输入设备号\"\r\n          clearable\r\n          @keyup.enter.native=\"handleQuery\"\r\n        />\r\n      </el-form-item>\r\n      <!-- <el-form-item label=\"bgm设备号\" prop=\"bgmDeviceNo\">\r\n        <el-input\r\n          v-model=\"queryParams.bgmDeviceNo\"\r\n          placeholder=\"请输入bgm设备号\"\r\n          clearable\r\n          @keyup.enter.native=\"handleQuery\"\r\n        />\r\n      </el-form-item> -->\r\n      <el-form-item label=\"绑定状态\" prop=\"status\">\r\n        <el-select\r\n          v-model=\"queryParams.status\"\r\n          placeholder=\"请选择绑定状态\"\r\n          clearable\r\n        >\r\n          <el-option\r\n            v-for=\"dict in dict.type.device_own_type\"\r\n            :key=\"dict.value\"\r\n            :label=\"dict.label\"\r\n            :value=\"dict.value\"\r\n          />\r\n        </el-select>\r\n      </el-form-item>\r\n      <!-- <el-form-item label=\"bgm设备使用方式\" prop=\"bgmOwnType\">\r\n        <el-select\r\n          v-model=\"queryParams.bgmOwnType\"\r\n          placeholder=\"请选择bgm设备使用方式\"\r\n          clearable\r\n        >\r\n          <el-option\r\n            v-for=\"dict in dict.type.device_own_type\"\r\n            :key=\"dict.value\"\r\n            :label=\"dict.label\"\r\n            :value=\"dict.value\"\r\n          />\r\n        </el-select>\r\n      </el-form-item> -->\r\n      <!-- <el-form-item label=\"cgm设备使用截止时间\" prop=\"cgmEndTime\">\r\n        <el-date-picker clearable\r\n          v-model=\"queryParams.cgmEndTime\"\r\n          type=\"date\"\r\n          value-format=\"yyyy-MM-dd\"\r\n          placeholder=\"请选择cgm设备使用截止时间\">\r\n        </el-date-picker>\r\n      </el-form-item> -->\r\n      <!-- <el-form-item label=\"bgm设备使用截止时间\" prop=\"bgmEndTime\">\r\n        <el-date-picker clearable\r\n          v-model=\"queryParams.bgmEndTime\"\r\n          type=\"date\"\r\n          value-format=\"yyyy-MM-dd\"\r\n          placeholder=\"请选择bgm设备使用截止时间\">\r\n        </el-date-picker>\r\n      </el-form-item> -->\r\n      <!-- <el-form-item label=\"${comment}\" prop=\"tenantId\">\r\n        <el-input\r\n          v-model=\"queryParams.tenantId\"\r\n          placeholder=\"请输入${comment}\"\r\n          clearable\r\n          @keyup.enter.native=\"handleQuery\"\r\n        />\r\n      </el-form-item> -->\r\n      <!-- <el-form-item label=\"${comment}\" prop=\"revision\">\r\n        <el-input\r\n          v-model=\"queryParams.revision\"\r\n          placeholder=\"请输入${comment}\"\r\n          clearable\r\n          @keyup.enter.native=\"handleQuery\"\r\n        />\r\n      </el-form-item> -->\r\n      <!-- <el-form-item label=\"cgm设备型号\" prop=\"cgmModel\">\r\n        <el-input\r\n          v-model=\"queryParams.cgmModel\"\r\n          placeholder=\"请输入cgm设备型号\"\r\n          clearable\r\n          @keyup.enter.native=\"handleQuery\"\r\n        />\r\n      </el-form-item>\r\n      <el-form-item label=\"bgm设备型号\" prop=\"bgmModel\">\r\n        <el-input\r\n          v-model=\"queryParams.bgmModel\"\r\n          placeholder=\"请输入bgm设备型号\"\r\n          clearable\r\n          @keyup.enter.native=\"handleQuery\"\r\n        />\r\n      </el-form-item>\r\n      <el-form-item label=\"CGM厂家\" prop=\"cgmFactory\">\r\n        <el-input\r\n          v-model=\"queryParams.cgmFactory\"\r\n          placeholder=\"请输入CGM厂家\"\r\n          clearable\r\n          @keyup.enter.native=\"handleQuery\"\r\n        />\r\n      </el-form-item>\r\n      <el-form-item label=\"bgm厂家\" prop=\"bgmFactory\">\r\n        <el-input\r\n          v-model=\"queryParams.bgmFactory\"\r\n          placeholder=\"请输入bgm厂家\"\r\n          clearable\r\n          @keyup.enter.native=\"handleQuery\"\r\n        />\r\n      </el-form-item> -->\r\n      <!-- <el-form-item label=\"cgm设备状态\" prop=\"cgmStatus\">\r\n        <el-select v-model=\"queryParams.cgmStatus\" placeholder=\"请选择cgm设备状态\" clearable>\r\n          <el-option\r\n            v-for=\"dict in dict.type.sys_normal_disable\"\r\n            :key=\"dict.value\"\r\n            :label=\"dict.label\"\r\n            :value=\"dict.value\"\r\n          />\r\n        </el-select>\r\n      </el-form-item>\r\n      <el-form-item label=\"bgm设备状态\" prop=\"bgmStatus\">\r\n        <el-select v-model=\"queryParams.bgmStatus\" placeholder=\"请选择bgm设备状态\" clearable>\r\n          <el-option\r\n            v-for=\"dict in dict.type.sys_normal_disable\"\r\n            :key=\"dict.value\"\r\n            :label=\"dict.label\"\r\n            :value=\"dict.value\"\r\n          />\r\n        </el-select>\r\n      </el-form-item> -->\r\n      <!-- <el-form-item label=\"机构id\" prop=\"unitId\">\r\n        <el-input\r\n          v-model=\"queryParams.unitId\"\r\n          placeholder=\"请输入机构id\"\r\n          clearable\r\n          @keyup.enter.native=\"handleQuery\"\r\n        />\r\n      </el-form-item>\r\n      <el-form-item label=\"机构名称\" prop=\"unitName\">\r\n        <el-input\r\n          v-model=\"queryParams.unitName\"\r\n          placeholder=\"请输入机构名称\"\r\n          clearable\r\n          @keyup.enter.native=\"handleQuery\"\r\n        />\r\n      </el-form-item> -->\r\n      <el-form-item>\r\n        <el-button\r\n          type=\"primary\"\r\n          icon=\"el-icon-search\"\r\n          size=\"mini\"\r\n          @click=\"handleQuery\"\r\n          >搜索</el-button\r\n        >\r\n        <el-button icon=\"el-icon-refresh\" size=\"mini\" @click=\"resetQuery\"\r\n          >重置</el-button\r\n        >\r\n      </el-form-item>\r\n    </el-form>\r\n\r\n    <el-row :gutter=\"10\" class=\"mb8\">\r\n      <el-col :span=\"1.5\">\r\n        <el-button\r\n          type=\"primary\"\r\n          plain\r\n          icon=\"el-icon-plus\"\r\n          size=\"mini\"\r\n          @click=\"handleAdd\"\r\n          v-hasPermi=\"['cgm:device:add']\"\r\n          >新增</el-button\r\n        >\r\n      </el-col>\r\n      <el-col :span=\"1.5\">\r\n        <el-button\r\n          type=\"primary\"\r\n          plain\r\n          icon=\"el-icon-edit\"\r\n          size=\"mini\"\r\n          :disabled=\"single\"\r\n          @click=\"handleUpdate\"\r\n          v-hasPermi=\"['cgm:device:edit']\"\r\n          >修改</el-button\r\n        >\r\n      </el-col>\r\n      <el-col :span=\"1.5\">\r\n        <el-button\r\n          type=\"primary\"\r\n          plain\r\n          icon=\"el-icon-delete\"\r\n          size=\"mini\"\r\n          :disabled=\"multiple\"\r\n          @click=\"handleDelete\"\r\n          v-hasPermi=\"['cgm:device:remove']\"\r\n          >删除</el-button\r\n        >\r\n      </el-col>\r\n      <!-- <el-col :span=\"1.5\">\r\n        <el-button\r\n          type=\"warning\"\r\n          plain\r\n          icon=\"el-icon-download\"\r\n          size=\"mini\"\r\n          @click=\"handleExport\"\r\n          v-hasPermi=\"['cgm:device:export']\"\r\n          >导出</el-button\r\n        >\r\n      </el-col> -->\r\n      <right-toolbar\r\n        :showSearch.sync=\"showSearch\"\r\n        @queryTable=\"getList\"\r\n      ></right-toolbar>\r\n    </el-row>\r\n\r\n    <el-table\r\n      v-loading=\"loading\"\r\n      :data=\"deviceList\"\r\n      @selection-change=\"handleSelectionChange\"\r\n    >\r\n      <el-table-column type=\"selection\" width=\"55\" align=\"center\" />\r\n      <!-- <el-table-column label=\"id\" align=\"center\" prop=\"id\" /> -->\r\n      <!-- <el-table-column label=\"患者ID\" align=\"center\" prop=\"patientId\" /> -->\r\n      <el-table-column label=\"患者姓名\" align=\"center\" prop=\"patientName\" />\r\n      <el-table-column label=\"设备号\" align=\"center\" prop=\"equipCode\" />\r\n      <el-table-column label=\"绑定时间\" align=\"center\" prop=\"createTime\" />\r\n      <el-table-column label=\"状态\" align=\"center\" prop=\"status\" />\r\n      <el-table-column label=\"解绑时间\" align=\"center\" prop=\"unbindTime\" />\r\n      <!-- <el-table-column label=\"cgm设备使用方式\" align=\"center\" prop=\"cgmOwnType\">\r\n        <template slot-scope=\"scope\">\r\n          <dict-tag\r\n            :options=\"dict.type.device_own_type\"\r\n            :value=\"scope.row.cgmOwnType\"\r\n          />\r\n        </template>\r\n      </el-table-column>\r\n      <el-table-column label=\"bgm设备号\" align=\"center\" prop=\"bgmDeviceNo\" />\r\n      <el-table-column label=\"bgm设备使用方式\" align=\"center\" prop=\"bgmOwnType\">\r\n        <template slot-scope=\"scope\">\r\n          <dict-tag\r\n            :options=\"dict.type.device_own_type\"\r\n            :value=\"scope.row.bgmOwnType\"\r\n          />\r\n        </template>\r\n      </el-table-column> -->\r\n      <!-- <el-table-column\r\n        label=\"cgm设备使用截止时间\"\r\n        align=\"center\"\r\n        prop=\"cgmEndTime\"\r\n        width=\"180\"\r\n      >\r\n        <template slot-scope=\"scope\">\r\n          <span>{{ parseTime(scope.row.cgmEndTime, \"{y}-{m}-{d}\") }}</span>\r\n        </template>\r\n      </el-table-column> -->\r\n      <!-- <el-table-column\r\n        label=\"bgm设备使用截止时间\"\r\n        align=\"center\"\r\n        prop=\"bgmEndTime\"\r\n        width=\"180\"\r\n      >\r\n        <template slot-scope=\"scope\">\r\n          <span>{{ parseTime(scope.row.bgmEndTime, \"{y}-{m}-{d}\") }}</span>\r\n        </template>\r\n      </el-table-column> -->\r\n\r\n      <!-- <el-table-column label=\"cgm设备型号\" align=\"center\" prop=\"cgmModel\" />\r\n      <el-table-column label=\"bgm设备型号\" align=\"center\" prop=\"bgmModel\" />\r\n      <el-table-column label=\"CGM厂家\" align=\"center\" prop=\"cgmFactory\" />\r\n      <el-table-column label=\"bgm厂家\" align=\"center\" prop=\"bgmFactory\" />\r\n      <el-table-column label=\"cgm设备状态\" align=\"center\" prop=\"cgmStatus\">\r\n        <template slot-scope=\"scope\">\r\n          <dict-tag\r\n            :options=\"dict.type.sys_normal_disable\"\r\n            :value=\"scope.row.cgmStatus\"\r\n          />\r\n        </template>\r\n      </el-table-column> -->\r\n      <!-- <el-table-column label=\"bgm设备状态\" align=\"center\" prop=\"bgmStatus\">\r\n        <template slot-scope=\"scope\">\r\n          <dict-tag\r\n            :options=\"dict.type.sys_normal_disable\"\r\n            :value=\"scope.row.bgmStatus\"\r\n          />\r\n        </template>\r\n      </el-table-column>\r\n      <el-table-column label=\"机构id\" align=\"center\" prop=\"unitId\" />\r\n      <el-table-column label=\"机构名称\" align=\"center\" prop=\"unitName\" /> -->\r\n      <el-table-column\r\n        label=\"操作\"\r\n        align=\"center\"\r\n        class-name=\"small-padding fixed-width\"\r\n      >\r\n        <template slot-scope=\"scope\">\r\n          <el-button\r\n            size=\"mini\"\r\n            type=\"text\"\r\n            icon=\"el-icon-edit\"\r\n            @click=\"handleUpdate(scope.row)\"\r\n            v-hasPermi=\"['cgm:device:edit']\"\r\n            >修改</el-button\r\n          >\r\n          <el-button\r\n            size=\"mini\"\r\n            type=\"text\"\r\n            icon=\"el-icon-delete\"\r\n            @click=\"handleDelete(scope.row)\"\r\n            v-hasPermi=\"['cgm:device:remove']\"\r\n            >删除</el-button\r\n          >\r\n        </template>\r\n      </el-table-column>\r\n    </el-table>\r\n\r\n    <pagination\r\n      v-show=\"total > 0\"\r\n      :total=\"total\"\r\n      :page.sync=\"queryParams.pageNum\"\r\n      :limit.sync=\"queryParams.pageSize\"\r\n      @pagination=\"getList\"\r\n    />\r\n\r\n    <!-- 添加或修改血糖设备关联对话框 -->\r\n    <el-dialog\r\n      :title=\"title\"\r\n      :visible.sync=\"open\"\r\n      width=\"500px\"\r\n      :close-on-click-modal=\"false\"\r\n      append-to-body\r\n    >\r\n      <el-form ref=\"form\" :model=\"form\" :rules=\"rules\" label-width=\"80px\">\r\n        <el-form-item label=\"患者\" prop=\"patientId\">\r\n          <!-- <el-input v-model=\"form.patientId\" placeholder=\"请选择绑定患者\" /> -->\r\n          <PatientInputSelect\r\n            @change=\"patientChange\"\r\n            ref=\"patientInputSelect\"\r\n          />\r\n        </el-form-item>\r\n\r\n        <el-form-item label=\"绑定设备\">\r\n          <InputSelect\r\n            @change=\"deviceChange\"\r\n            placeholder=\"请选择设备\"\r\n            ref=\"inputSelect\"\r\n            :options=\"devices\"\r\n            :dataList=\"dataList\"\r\n          />\r\n        </el-form-item>\r\n\r\n        <!-- <el-form-item label=\"患者姓名\" prop=\"patientName\">\r\n          <el-input v-model=\"form.patientName\" placeholder=\"请输入患者姓名\" />\r\n        </el-form-item> -->\r\n        <block v-show=\"false\">\r\n          <el-divider>CGM设备绑定</el-divider>\r\n          <el-row>\r\n            <el-col :span=\"12\"\r\n              ><el-form-item\r\n                label=\"设备号\"\r\n                prop=\"cgmDeviceNo\"\r\n                label-width=\"130px\"\r\n              >\r\n                <el-input\r\n                  v-model=\"form.cgmDeviceNo\"\r\n                  placeholder=\"请输入cgm设备号\"\r\n                /> </el-form-item\r\n            ></el-col>\r\n            <el-col :span=\"12\"\r\n              ><el-form-item\r\n                label=\"设备使用方式\"\r\n                prop=\"cgmOwnType\"\r\n                label-width=\"130px\"\r\n              >\r\n                <el-select\r\n                  style=\"width: 100%\"\r\n                  v-model=\"form.cgmOwnType\"\r\n                  placeholder=\"请选择cgm设备使用方式\"\r\n                >\r\n                  <el-option\r\n                    v-for=\"dict in dict.type.device_own_type\"\r\n                    :key=\"dict.value\"\r\n                    :label=\"dict.label\"\r\n                    :value=\"dict.value\"\r\n                  ></el-option>\r\n                </el-select> </el-form-item\r\n            ></el-col>\r\n          </el-row>\r\n          <el-row>\r\n            <el-col :span=\"12\">\r\n              <el-form-item\r\n                label=\"设备型号\"\r\n                prop=\"cgmModel\"\r\n                label-width=\"130px\"\r\n              >\r\n                <el-input\r\n                  v-model=\"form.cgmModel\"\r\n                  placeholder=\"请输入cgm设备型号\"\r\n                /> </el-form-item\r\n            ></el-col>\r\n            <el-col :span=\"12\"\r\n              ><el-form-item label=\"厂家\" prop=\"cgmFactory\" label-width=\"130px\">\r\n                <el-input\r\n                  v-model=\"form.cgmFactory\"\r\n                  placeholder=\"请输入CGM厂家\"\r\n                /> </el-form-item\r\n            ></el-col>\r\n          </el-row>\r\n          <el-row>\r\n            <el-col :span=\"12\"\r\n              ><el-form-item\r\n                label=\"设备状态\"\r\n                prop=\"cgmStatus\"\r\n                label-width=\"130px\"\r\n              >\r\n                <el-select\r\n                  style=\"width: 100%\"\r\n                  v-model=\"form.cgmStatus\"\r\n                  placeholder=\"请选择cgm设备状态\"\r\n                >\r\n                  <el-option\r\n                    v-for=\"dict in dict.type.sys_normal_disable\"\r\n                    :key=\"dict.value\"\r\n                    :label=\"dict.label\"\r\n                    :value=\"dict.value\"\r\n                  ></el-option>\r\n                </el-select> </el-form-item\r\n            ></el-col>\r\n            <el-col v-show=\"form.cgmOwnType == 1\" :span=\"12\"\r\n              ><el-form-item\r\n                label=\"设备使用截止时间\"\r\n                prop=\"cgmEndTime\"\r\n                label-width=\"160px\"\r\n              >\r\n                <el-date-picker\r\n                  style=\"width: 100%\"\r\n                  clearable\r\n                  v-model=\"form.cgmEndTime\"\r\n                  type=\"date\"\r\n                  value-format=\"yyyy-MM-dd\"\r\n                  placeholder=\"请选择cgm设备使用截止时间\"\r\n                >\r\n                </el-date-picker> </el-form-item\r\n            ></el-col>\r\n          </el-row>\r\n        </block>\r\n        <block v-show=\"false\">\r\n          <el-divider>BGM设备绑定</el-divider>\r\n          <el-row>\r\n            <el-col :span=\"12\"\r\n              ><el-form-item\r\n                label=\"设备号\"\r\n                prop=\"bgmDeviceNo\"\r\n                label-width=\"130px\"\r\n              >\r\n                <el-input\r\n                  v-model=\"form.bgmDeviceNo\"\r\n                  placeholder=\"请输入bgm设备号\"\r\n                /> </el-form-item\r\n            ></el-col>\r\n            <el-col :span=\"12\"\r\n              ><el-form-item\r\n                label=\"设备使用方式\"\r\n                prop=\"bgmOwnType\"\r\n                label-width=\"130px\"\r\n              >\r\n                <el-select\r\n                  style=\"width: 100%\"\r\n                  v-model=\"form.bgmOwnType\"\r\n                  placeholder=\"请选择bgm设备使用方式\"\r\n                >\r\n                  <el-option\r\n                    v-for=\"dict in dict.type.device_own_type\"\r\n                    :key=\"dict.value\"\r\n                    :label=\"dict.label\"\r\n                    :value=\"dict.value\"\r\n                  ></el-option>\r\n                </el-select> </el-form-item\r\n            ></el-col>\r\n          </el-row>\r\n\r\n          <el-row>\r\n            <el-col :span=\"12\"\r\n              ><el-form-item\r\n                label=\"设备型号\"\r\n                prop=\"bgmModel\"\r\n                label-width=\"130px\"\r\n              >\r\n                <el-input\r\n                  v-model=\"form.bgmModel\"\r\n                  placeholder=\"请输入bgm设备型号\"\r\n                /> </el-form-item\r\n            ></el-col>\r\n            <el-col :span=\"12\"\r\n              ><el-form-item label=\"厂家\" prop=\"bgmFactory\" label-width=\"130px\">\r\n                <el-input\r\n                  v-model=\"form.bgmFactory\"\r\n                  placeholder=\"请输入bgm厂家\"\r\n                /> </el-form-item\r\n            ></el-col>\r\n          </el-row>\r\n\r\n          <el-row>\r\n            <el-col :span=\"12\">\r\n              <el-form-item\r\n                label=\"设备状态\"\r\n                prop=\"bgmStatus\"\r\n                label-width=\"130px\"\r\n              >\r\n                <el-select\r\n                  style=\"width: 100%\"\r\n                  v-model=\"form.bgmStatus\"\r\n                  placeholder=\"请选择bgm设备状态\"\r\n                >\r\n                  <el-option\r\n                    v-for=\"dict in dict.type.sys_normal_disable\"\r\n                    :key=\"dict.value\"\r\n                    :label=\"dict.label\"\r\n                    :value=\"dict.value\"\r\n                  ></el-option>\r\n                </el-select> </el-form-item\r\n            ></el-col>\r\n            <el-col v-show=\"form.bgmOwnType == 1\" :span=\"12\"\r\n              ><el-form-item\r\n                label=\"设备使用截止时间\"\r\n                prop=\"bgmEndTime\"\r\n                label-width=\"160px\"\r\n              >\r\n                <el-date-picker\r\n                  style=\"width: 100%\"\r\n                  clearable\r\n                  v-model=\"form.bgmEndTime\"\r\n                  type=\"date\"\r\n                  value-format=\"yyyy-MM-dd\"\r\n                  placeholder=\"请选择bgm设备使用截止时间\"\r\n                >\r\n                </el-date-picker> </el-form-item\r\n            ></el-col>\r\n          </el-row>\r\n        </block>\r\n      </el-form>\r\n      <div slot=\"footer\" class=\"dialog-footer\">\r\n        <el-button type=\"primary\" @click=\"submitForm\">确 定</el-button>\r\n        <el-button @click=\"cancel\">取 消</el-button>\r\n      </div>\r\n    </el-dialog>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport {\r\n  listDevice,\r\n  getDevice,\r\n  delDevice,\r\n  addDevice,\r\n  updateDevice,\r\n  bindIf,\r\n} from \"@/api/cgm/device\";\r\nimport {\r\n  listEquipment,\r\n  getEquipment,\r\n  delEquipment,\r\n  addEquipment,\r\n  updateEquipment,\r\n} from \"@/api/equip/equipment\";\r\nimport PatientInputSelect from \"@/components/PatientInputSelect\";\r\nimport InputSelect from \"@/components/InputSelect\";\r\nexport default {\r\n  name: \"Device\",\r\n  dicts: [\"device_own_type\", \"sys_normal_disable\", \"equip_type\"],\r\n  components: { PatientInputSelect, InputSelect },\r\n  data() {\r\n    return {\r\n      deviceType: \"0\",\r\n      // 遮罩层\r\n      loading: true,\r\n      // 选中数组\r\n      ids: [],\r\n      //可选择绑定的设备列表\r\n      devices: [],\r\n      // 非单个禁用\r\n      single: true,\r\n      // 非多个禁用\r\n      multiple: true,\r\n      // 显示搜索条件\r\n      showSearch: true,\r\n      // 总条数\r\n      total: 0,\r\n      // 血糖设备关联表格数据\r\n      deviceList: [],\r\n      // 弹出层标题\r\n      title: \"\",\r\n      // 是否显示弹出层\r\n      open: false,\r\n      // 查询参数\r\n      queryParams: {\r\n        pageNum: 1,\r\n        pageSize: 10,\r\n        patientId: null,\r\n        patientName: null,\r\n        cgmDeviceNo: null,\r\n        bgmDeviceNo: null,\r\n        cgmOwnType: null,\r\n        bgmOwnType: null,\r\n        cgmEndTime: null,\r\n        bgmEndTime: null,\r\n        tenantId: null,\r\n        revision: null,\r\n        cgmModel: null,\r\n        bgmModel: null,\r\n        cgmFactory: null,\r\n        bgmFactory: null,\r\n        cgmStatus: null,\r\n        bgmStatus: null,\r\n        unitId: null,\r\n        unitName: null,\r\n      },\r\n      // 表单参数\r\n      form: {},\r\n      // 表单校验\r\n      rules: {},\r\n      dataList: [],\r\n    };\r\n  },\r\n  created() {\r\n    this.getList();\r\n    //查询可绑定的设备\r\n    this.getEquips();\r\n  },\r\n  methods: {\r\n    getEquips() {\r\n      listEquipment({ pageNum: 1, pageSize: 1000000 }).then((res) => {\r\n        console.log(\"listEquipment\", res);\r\n        if (res.code == 200) {\r\n          this.dataList = res.rows;\r\n          this.dataList.forEach((item) => {\r\n            this.devices.push({\r\n              value: item.id,\r\n              label:\r\n                item.name +\r\n                \"/\" +\r\n                item.equipCode +\r\n                \"/\" +\r\n                this.selectDictLabel(this.dict.type.equip_type, item.type),\r\n            });\r\n          });\r\n        }\r\n      });\r\n    },\r\n    deviceChange(data) {\r\n      console.log(\"deviceChange\", data);\r\n      this.form.equipCode = data.equipCode;\r\n      this.form.equipId = data.id;\r\n      this.form.idNo = data.idNo;\r\n    },\r\n    patientChange(patient) {\r\n      this.form.patientId = patient.id;\r\n      this.form.patientName = patient.name;\r\n      console.log(this.form);\r\n      this.checkBind();\r\n    },\r\n    checkBind() {\r\n      bindIf(this.form.patientId).then((response) => {\r\n        if (response.data) {\r\n          this.$modal.msgError(\"该患者已绑定设备\");\r\n        }\r\n      });\r\n    },\r\n    /** 查询血糖设备关联列表 */\r\n    getList() {\r\n      this.loading = true;\r\n      listDevice(this.queryParams).then((response) => {\r\n        this.deviceList = response.rows;\r\n        this.total = response.total;\r\n        this.loading = false;\r\n      });\r\n    },\r\n    // 取消按钮\r\n    cancel() {\r\n      this.open = false;\r\n      this.reset();\r\n    },\r\n    // 表单重置\r\n    reset() {\r\n      this.form = {\r\n        id: null,\r\n        patientId: null,\r\n        patientName: null,\r\n        createTime: null,\r\n        updateTime: null,\r\n        createBy: null,\r\n        updateBy: null,\r\n        cgmDeviceNo: null,\r\n        bgmDeviceNo: null,\r\n        cgmOwnType: null,\r\n        bgmOwnType: null,\r\n        cgmEndTime: null,\r\n        bgmEndTime: null,\r\n        tenantId: null,\r\n        revision: null,\r\n        cgmModel: null,\r\n        bgmModel: null,\r\n        cgmFactory: null,\r\n        bgmFactory: null,\r\n        cgmStatus: null,\r\n        bgmStatus: null,\r\n        unitId: null,\r\n        unitName: null,\r\n      };\r\n      this.resetForm(\"form\");\r\n      if (this.$refs.patientInputSelect) this.$refs.patientInputSelect.clear();\r\n      if (this.$refs.inputSelect) this.$refs.inputSelect.clear();\r\n    },\r\n    /** 搜索按钮操作 */\r\n    handleQuery() {\r\n      this.queryParams.pageNum = 1;\r\n      this.getList();\r\n    },\r\n    /** 重置按钮操作 */\r\n    resetQuery() {\r\n      this.resetForm(\"queryForm\");\r\n      this.handleQuery();\r\n    },\r\n    // 多选框选中数据\r\n    handleSelectionChange(selection) {\r\n      this.ids = selection.map((item) => item.id);\r\n      this.single = selection.length !== 1;\r\n      this.multiple = !selection.length;\r\n    },\r\n    /** 新增按钮操作 */\r\n    handleAdd() {\r\n      this.reset();\r\n      this.open = true;\r\n      this.title = \"添加血糖设备关联\";\r\n    },\r\n    /** 修改按钮操作 */\r\n    handleUpdate(row) {\r\n      this.reset();\r\n      const id = row.id || this.ids;\r\n      getDevice(id).then((response) => {\r\n        this.form = response.data;\r\n        this.open = true;\r\n        this.title = \"修改血糖设备关联\";\r\n      });\r\n    },\r\n    /** 提交按钮 */\r\n    submitForm() {\r\n      this.$refs[\"form\"].validate((valid) => {\r\n        if (valid) {\r\n          if (this.form.id != null) {\r\n            updateDevice(this.form).then((response) => {\r\n              this.$modal.msgSuccess(\"修改成功\");\r\n              this.open = false;\r\n              this.getList();\r\n            });\r\n          } else {\r\n            addDevice(this.form).then((response) => {\r\n              this.$modal.msgSuccess(\"新增成功\");\r\n              this.open = false;\r\n              this.getList();\r\n            });\r\n          }\r\n        }\r\n      });\r\n    },\r\n    /** 删除按钮操作 */\r\n    handleDelete(row) {\r\n      const ids = row.id || this.ids;\r\n      this.$modal\r\n        .confirm('是否确认删除血糖设备关联编号为\"' + ids + '\"的数据项？')\r\n        .then(function () {\r\n          return delDevice(ids);\r\n        })\r\n        .then(() => {\r\n          this.getList();\r\n          this.$modal.msgSuccess(\"删除成功\");\r\n        })\r\n        .catch(() => {});\r\n    },\r\n    /** 导出按钮操作 */\r\n    handleExport() {\r\n      this.download(\r\n        \"cgm/device/export\",\r\n        {\r\n          ...this.queryParams,\r\n        },\r\n        `device_${new Date().getTime()}.xlsx`\r\n      );\r\n    },\r\n  },\r\n};\r\n</script>\r\n"]}]}