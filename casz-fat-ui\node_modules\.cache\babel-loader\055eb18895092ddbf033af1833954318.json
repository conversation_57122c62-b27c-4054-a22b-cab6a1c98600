{"remainingRequest": "D:\\shangchen\\casz-fat-j21\\casz-fat-ui\\node_modules\\babel-loader\\lib\\index.js!D:\\shangchen\\casz-fat-j21\\casz-fat-ui\\node_modules\\eslint-loader\\index.js??ref--13-0!D:\\shangchen\\casz-fat-j21\\casz-fat-ui\\src\\api\\equip\\qc.js", "dependencies": [{"path": "D:\\shangchen\\casz-fat-j21\\casz-fat-ui\\src\\api\\equip\\qc.js", "mtime": 1752668934321}, {"path": "D:\\shangchen\\casz-fat-j21\\casz-fat-ui\\babel.config.js", "mtime": 1752668935560}, {"path": "D:\\shangchen\\casz-fat-j21\\casz-fat-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1747273083419}, {"path": "D:\\shangchen\\casz-fat-j21\\casz-fat-ui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1747273094151}, {"path": "D:\\shangchen\\casz-fat-j21\\casz-fat-ui\\node_modules\\eslint-loader\\index.js", "mtime": 1747273087917}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:InVzZSBzdHJpY3QiOwoKdmFyIF9pbnRlcm9wUmVxdWlyZURlZmF1bHQgPSByZXF1aXJlKCJEOi9zaGFuZ2NoZW4vY2Fzei1mYXQtajIxL2Nhc3otZmF0LXVpL25vZGVfbW9kdWxlcy9AYmFiZWwvcnVudGltZS9oZWxwZXJzL2ludGVyb3BSZXF1aXJlRGVmYXVsdC5qcyIpLmRlZmF1bHQ7Ck9iamVjdC5kZWZpbmVQcm9wZXJ0eShleHBvcnRzLCAiX19lc01vZHVsZSIsIHsKICB2YWx1ZTogdHJ1ZQp9KTsKZXhwb3J0cy5hZGRRYyA9IGFkZFFjOwpleHBvcnRzLmRlbFFjID0gZGVsUWM7CmV4cG9ydHMuZ2V0UWMgPSBnZXRRYzsKZXhwb3J0cy5saXN0UWMgPSBsaXN0UWM7CmV4cG9ydHMudXBkYXRlUWMgPSB1cGRhdGVRYzsKdmFyIF9yZXF1ZXN0ID0gX2ludGVyb3BSZXF1aXJlRGVmYXVsdChyZXF1aXJlKCJAL3V0aWxzL3JlcXVlc3QiKSk7Ci8vIOafpeivouiuvuWkh+i0qOaOp+WIl+ihqApmdW5jdGlvbiBsaXN0UWMocXVlcnkpIHsKICByZXR1cm4gKDAsIF9yZXF1ZXN0LmRlZmF1bHQpKHsKICAgIHVybDogJy9lcXVpcC9xYy9saXN0JywKICAgIG1ldGhvZDogJ2dldCcsCiAgICBwYXJhbXM6IHF1ZXJ5CiAgfSk7Cn0KCi8vIOafpeivouiuvuWkh+i0qOaOp+ivpue7hgpmdW5jdGlvbiBnZXRRYyhpZCkgewogIHJldHVybiAoMCwgX3JlcXVlc3QuZGVmYXVsdCkoewogICAgdXJsOiAnL2VxdWlwL3FjLycgKyBpZCwKICAgIG1ldGhvZDogJ2dldCcKICB9KTsKfQoKLy8g5paw5aKe6K6+5aSH6LSo5o6nCmZ1bmN0aW9uIGFkZFFjKGRhdGEpIHsKICByZXR1cm4gKDAsIF9yZXF1ZXN0LmRlZmF1bHQpKHsKICAgIHVybDogJy9lcXVpcC9xYycsCiAgICBtZXRob2Q6ICdwb3N0JywKICAgIGRhdGE6IGRhdGEKICB9KTsKfQoKLy8g5L+u5pS56K6+5aSH6LSo5o6nCmZ1bmN0aW9uIHVwZGF0ZVFjKGRhdGEpIHsKICByZXR1cm4gKDAsIF9yZXF1ZXN0LmRlZmF1bHQpKHsKICAgIHVybDogJy9lcXVpcC9xYycsCiAgICBtZXRob2Q6ICdwdXQnLAogICAgZGF0YTogZGF0YQogIH0pOwp9CgovLyDliKDpmaTorr7lpIfotKjmjqcKZnVuY3Rpb24gZGVsUWMoaWQpIHsKICByZXR1cm4gKDAsIF9yZXF1ZXN0LmRlZmF1bHQpKHsKICAgIHVybDogJy9lcXVpcC9xYy8nICsgaWQsCiAgICBtZXRob2Q6ICdkZWxldGUnCiAgfSk7Cn0="}, {"version": 3, "names": ["_request", "_interopRequireDefault", "require", "listQc", "query", "request", "url", "method", "params", "getQc", "id", "addQc", "data", "updateQc", "delQc"], "sources": ["D:/shangchen/casz-fat-j21/casz-fat-ui/src/api/equip/qc.js"], "sourcesContent": ["import request from '@/utils/request'\r\n\r\n// 查询设备质控列表\r\nexport function listQc(query) {\r\n  return request({\r\n    url: '/equip/qc/list',\r\n    method: 'get',\r\n    params: query\r\n  })\r\n}\r\n\r\n// 查询设备质控详细\r\nexport function getQc(id) {\r\n  return request({\r\n    url: '/equip/qc/' + id,\r\n    method: 'get'\r\n  })\r\n}\r\n\r\n// 新增设备质控\r\nexport function addQc(data) {\r\n  return request({\r\n    url: '/equip/qc',\r\n    method: 'post',\r\n    data: data\r\n  })\r\n}\r\n\r\n// 修改设备质控\r\nexport function updateQc(data) {\r\n  return request({\r\n    url: '/equip/qc',\r\n    method: 'put',\r\n    data: data\r\n  })\r\n}\r\n\r\n// 删除设备质控\r\nexport function delQc(id) {\r\n  return request({\r\n    url: '/equip/qc/' + id,\r\n    method: 'delete'\r\n  })\r\n}\r\n"], "mappings": ";;;;;;;;;;;AAAA,IAAAA,QAAA,GAAAC,sBAAA,CAAAC,OAAA;AAEA;AACO,SAASC,MAAMA,CAACC,KAAK,EAAE;EAC5B,OAAO,IAAAC,gBAAO,EAAC;IACbC,GAAG,EAAE,gBAAgB;IACrBC,MAAM,EAAE,KAAK;IACbC,MAAM,EAAEJ;EACV,CAAC,CAAC;AACJ;;AAEA;AACO,SAASK,KAAKA,CAACC,EAAE,EAAE;EACxB,OAAO,IAAAL,gBAAO,EAAC;IACbC,GAAG,EAAE,YAAY,GAAGI,EAAE;IACtBH,MAAM,EAAE;EACV,CAAC,CAAC;AACJ;;AAEA;AACO,SAASI,KAAKA,CAACC,IAAI,EAAE;EAC1B,OAAO,IAAAP,gBAAO,EAAC;IACbC,GAAG,EAAE,WAAW;IAChBC,MAAM,EAAE,MAAM;IACdK,IAAI,EAAEA;EACR,CAAC,CAAC;AACJ;;AAEA;AACO,SAASC,QAAQA,CAACD,IAAI,EAAE;EAC7B,OAAO,IAAAP,gBAAO,EAAC;IACbC,GAAG,EAAE,WAAW;IAChBC,MAAM,EAAE,KAAK;IACbK,IAAI,EAAEA;EACR,CAAC,CAAC;AACJ;;AAEA;AACO,SAASE,KAAKA,CAACJ,EAAE,EAAE;EACxB,OAAO,IAAAL,gBAAO,EAAC;IACbC,GAAG,EAAE,YAAY,GAAGI,EAAE;IACtBH,MAAM,EAAE;EACV,CAAC,CAAC;AACJ", "ignoreList": []}]}