{"remainingRequest": "D:\\shangchen\\casz-fat-j21\\casz-fat-ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\shangchen\\casz-fat-j21\\casz-fat-ui\\src\\components\\EquipSelect\\index.vue?vue&type=script&lang=js", "dependencies": [{"path": "D:\\shangchen\\casz-fat-j21\\casz-fat-ui\\src\\components\\EquipSelect\\index.vue", "mtime": 1752668934586}, {"path": "D:\\shangchen\\casz-fat-j21\\casz-fat-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1747273083419}, {"path": "D:\\shangchen\\casz-fat-j21\\casz-fat-ui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1747273094151}, {"path": "D:\\shangchen\\casz-fat-j21\\casz-fat-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1747273083419}, {"path": "D:\\shangchen\\casz-fat-j21\\casz-fat-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1747273098384}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:Ly8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KDQppbXBvcnQgeyBsaXN0RXF1aXBtZW50IH0gZnJvbSAiQC9hcGkvZXF1aXAvZXF1aXBtZW50IjsNCmltcG9ydCB7IHBpbnlpbiB9IGZyb20gInBpbnlpbi1wcm8iOw0KZXhwb3J0IGRlZmF1bHQgew0KICBuYW1lOiAiRXF1aXBTZWxlY3QiLA0KICBkaWN0czogWyJlcXVpcF90eXBlIl0sDQogIHByb3BzOiB7DQogICAgZWlkOiB7IHR5cGU6IFN0cmluZywgZGVmYXVsdDogbnVsbCB9LA0KICB9LA0KICBkYXRhKCkgew0KICAgIHJldHVybiB7DQogICAgICBlcXVpcElkOiAiIiwNCiAgICAgIG9wdGlvbnM6IFtdLA0KICAgICAgd2FpdE9wdGlvbnM6IFtdLA0KICAgICAgZXF1aXBMaXN0OiBbXSwNCiAgICB9Ow0KICB9LA0KICB3YXRjaDogew0KICAgIGVpZCh2YWwpIHsNCiAgICAgIGlmICh2YWwpIHsNCiAgICAgICAgdGhpcy5lcXVpcElkID0gdmFsOw0KICAgICAgfQ0KICAgIH0sDQogIH0sDQogIGNyZWF0ZWQoKSB7DQogICAgdGhpcy5nZXRFcXVpcG1lbnRMaXN0KCk7DQogIH0sDQogIG1ldGhvZHM6IHsNCiAgICAvKiog5p+l6K+i5oKj6ICF5pWw5o2u5YiX6KGoICovDQogICAgZ2V0RXF1aXBtZW50TGlzdCgpIHsNCiAgICAgIGxpc3RFcXVpcG1lbnQoeyBwYWdlTnVtOiAxLCBwYWdlU2l6ZTogMTAwMDAwMCB9KS50aGVuKChyZXNwb25zZSkgPT4gew0KICAgICAgICB0aGlzLmVxdWlwTGlzdCA9IHJlc3BvbnNlLnJvd3M7DQogICAgICAgIHRoaXMub3B0aW9ucyA9IFtdOw0KICAgICAgICB0aGlzLmVxdWlwTGlzdC5mb3JFYWNoKChpdGVtKSA9PiB7DQogICAgICAgICAgdGhpcy5vcHRpb25zLnB1c2goew0KICAgICAgICAgICAgdmFsdWU6IGl0ZW0uaWQsDQogICAgICAgICAgICBsYWJlbDoNCiAgICAgICAgICAgICAgaXRlbS5uYW1lICsNCiAgICAgICAgICAgICAgIi8iICsNCiAgICAgICAgICAgICAgaXRlbS5lcXVpcENvZGUgKw0KICAgICAgICAgICAgICAiLyIgKw0KICAgICAgICAgICAgICBpdGVtLnZlbmRlciArDQogICAgICAgICAgICAgICIvIiArDQogICAgICAgICAgICAgIHRoaXMuc2VsZWN0RGljdExhYmVsKHRoaXMuZGljdC50eXBlLmVxdWlwX3R5cGUsIGl0ZW0udHlwZSksDQogICAgICAgICAgfSk7DQogICAgICAgIH0pOw0KICAgICAgICB0aGlzLndhaXRPcHRpb25zID0gdGhpcy5vcHRpb25zOw0KICAgICAgfSk7DQogICAgfSwNCiAgICBjbGVhcigpIHsNCiAgICAgIHRoaXMuZXF1aXBJZCA9ICIiOw0KICAgIH0sDQogICAgY2hhbmdlKHZhbHVlKSB7DQogICAgICBsZXQgZXF1aXBtZW50ID0gdGhpcy5lcXVpcExpc3QuZmluZCgoaXRlbSkgPT4gew0KICAgICAgICByZXR1cm4gaXRlbS5pZCA9PT0gdmFsdWU7DQogICAgICB9KTsNCiAgICAgIHRoaXMuJGVtaXQoImNoYW5nZSIsIGVxdWlwbWVudCk7DQogICAgfSwNCiAgICBmb2N1cygpIHsNCiAgICAgIHRoaXMud2FpdE9wdGlvbnMgPSB0aGlzLm9wdGlvbnM7DQogICAgfSwNCiAgICBmaWx0ZXIodmFsKSB7DQogICAgICBpZiAodmFsID09PSBudWxsIHx8IHZhbCA9PT0gdW5kZWZpbmVkIHx8IHZhbC50cmltKCkgPT09ICIiKSB7DQogICAgICAgIHRoaXMud2FpdE9wdGlvbnMgPSB0aGlzLm9wdGlvbnM7DQogICAgICAgIHJldHVybjsNCiAgICAgIH0NCiAgICAgIHZhbCA9IHZhbC50cmltKCk7DQogICAgICBjb25zdCBhcnIgPSB0aGlzLm9wdGlvbnMuZmlsdGVyKChpdGVtKSA9PiB7DQogICAgICAgIGNvbnN0IGl0ZW1QaW55aW4gPSBwaW55aW4oaXRlbS5sYWJlbCwgew0KICAgICAgICAgIHRvbmVUeXBlOiAibm9uZSIsDQogICAgICAgICAgc2VwYXJhdG9yOiAiIiwNCiAgICAgICAgICBub25aaDogInJlbW92ZWQiLA0KICAgICAgICB9KTsgLy8gJ2hhbnl1cGlueWluJyDmsYnor63mi7zpn7MNCiAgICAgICAgY29uc3QgaXRlbUZpcnN0UGlueWluID0gcGlueWluKGl0ZW0ubGFiZWwsIHsNCiAgICAgICAgICBwYXR0ZXJuOiAiZmlyc3QiLA0KICAgICAgICAgIHNlcGFyYXRvcjogIiIsDQogICAgICAgICAgdG9uZVR5cGU6ICJub25lIiwNCiAgICAgICAgICBub25aaDogInJlbW92ZWQiLA0KICAgICAgICB9KTsgLy8gJ2h5cHknDQogICAgICAgIGNvbnNvbGUubG9nKGl0ZW1QaW55aW4sIGl0ZW1GaXJzdFBpbnlpbik7DQogICAgICAgIHJldHVybiAoDQogICAgICAgICAgaXRlbS5sYWJlbC5pbmRleE9mKHZhbCkgPiAtMSB8fA0KICAgICAgICAgIGl0ZW1QaW55aW4uaW5kZXhPZih2YWwpID4gLTEgfHwNCiAgICAgICAgICBpdGVtRmlyc3RQaW55aW4uaW5kZXhPZih2YWwpID4gLTENCiAgICAgICAgKTsNCiAgICAgIH0pOw0KICAgICAgY29uc29sZS5sb2coIkBAQCIsIGFycik7DQogICAgICB0aGlzLndhaXRPcHRpb25zID0gYXJyOw0KICAgIH0sDQogIH0sDQp9Ow0K"}, {"version": 3, "sources": ["index.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;AAqBA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "index.vue", "sourceRoot": "src/components/EquipSelect", "sourcesContent": ["<template>\r\n  <el-select\r\n    v-model=\"equipId\"\r\n    filterable\r\n    :filter-method=\"filter\"\r\n    placeholder=\"请选择设备\"\r\n    style=\"width: 100%\"\r\n    @change=\"change\"\r\n    @focus=\"focus\"\r\n  >\r\n    <el-option\r\n      v-for=\"item in waitOptions\"\r\n      :key=\"item.value\"\r\n      :label=\"item.label\"\r\n      :value=\"item.value\"\r\n    >\r\n    </el-option>\r\n  </el-select>\r\n</template>\r\n\r\n<script>\r\nimport { listEquipment } from \"@/api/equip/equipment\";\r\nimport { pinyin } from \"pinyin-pro\";\r\nexport default {\r\n  name: \"EquipSelect\",\r\n  dicts: [\"equip_type\"],\r\n  props: {\r\n    eid: { type: String, default: null },\r\n  },\r\n  data() {\r\n    return {\r\n      equipId: \"\",\r\n      options: [],\r\n      waitOptions: [],\r\n      equipList: [],\r\n    };\r\n  },\r\n  watch: {\r\n    eid(val) {\r\n      if (val) {\r\n        this.equipId = val;\r\n      }\r\n    },\r\n  },\r\n  created() {\r\n    this.getEquipmentList();\r\n  },\r\n  methods: {\r\n    /** 查询患者数据列表 */\r\n    getEquipmentList() {\r\n      listEquipment({ pageNum: 1, pageSize: 1000000 }).then((response) => {\r\n        this.equipList = response.rows;\r\n        this.options = [];\r\n        this.equipList.forEach((item) => {\r\n          this.options.push({\r\n            value: item.id,\r\n            label:\r\n              item.name +\r\n              \"/\" +\r\n              item.equipCode +\r\n              \"/\" +\r\n              item.vender +\r\n              \"/\" +\r\n              this.selectDictLabel(this.dict.type.equip_type, item.type),\r\n          });\r\n        });\r\n        this.waitOptions = this.options;\r\n      });\r\n    },\r\n    clear() {\r\n      this.equipId = \"\";\r\n    },\r\n    change(value) {\r\n      let equipment = this.equipList.find((item) => {\r\n        return item.id === value;\r\n      });\r\n      this.$emit(\"change\", equipment);\r\n    },\r\n    focus() {\r\n      this.waitOptions = this.options;\r\n    },\r\n    filter(val) {\r\n      if (val === null || val === undefined || val.trim() === \"\") {\r\n        this.waitOptions = this.options;\r\n        return;\r\n      }\r\n      val = val.trim();\r\n      const arr = this.options.filter((item) => {\r\n        const itemPinyin = pinyin(item.label, {\r\n          toneType: \"none\",\r\n          separator: \"\",\r\n          nonZh: \"removed\",\r\n        }); // 'hanyupinyin' 汉语拼音\r\n        const itemFirstPinyin = pinyin(item.label, {\r\n          pattern: \"first\",\r\n          separator: \"\",\r\n          toneType: \"none\",\r\n          nonZh: \"removed\",\r\n        }); // 'hypy'\r\n        console.log(itemPinyin, itemFirstPinyin);\r\n        return (\r\n          item.label.indexOf(val) > -1 ||\r\n          itemPinyin.indexOf(val) > -1 ||\r\n          itemFirstPinyin.indexOf(val) > -1\r\n        );\r\n      });\r\n      console.log(\"@@@\", arr);\r\n      this.waitOptions = arr;\r\n    },\r\n  },\r\n};\r\n</script>\r\n\r\n<style>\r\n</style>"]}]}