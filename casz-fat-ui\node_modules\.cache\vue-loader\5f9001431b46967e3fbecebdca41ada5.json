{"remainingRequest": "D:\\shangchen\\casz-fat-j21\\casz-fat-ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\shangchen\\casz-fat-j21\\casz-fat-ui\\src\\components\\TinyMce\\index.vue?vue&type=script&lang=js", "dependencies": [{"path": "D:\\shangchen\\casz-fat-j21\\casz-fat-ui\\src\\components\\TinyMce\\index.vue", "mtime": 1753843409411}, {"path": "D:\\shangchen\\casz-fat-j21\\casz-fat-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1747273083419}, {"path": "D:\\shangchen\\casz-fat-j21\\casz-fat-ui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1747273094151}, {"path": "D:\\shangchen\\casz-fat-j21\\casz-fat-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1747273083419}, {"path": "D:\\shangchen\\casz-fat-j21\\casz-fat-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1747273098384}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "sources": ["index.vue"], "names": [], "mappings": ";;;;;;;;;AASA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "index.vue", "sourceRoot": "src/components/TinyMce", "sourcesContent": ["<template>\r\n  <!-- 富文本 -->\r\n  <div>\r\n    <editor v-model=\"content\" :init=\"init\" :disabled=\"disabled\"></editor>\r\n  </div>\r\n</template>\r\n\r\n\r\n<script>\r\nimport axios from \"axios\";\r\nimport { config } from \"@/utils\";\r\nimport tinymce from \"tinymce/tinymce\";\r\nimport Editor from \"@tinymce/tinymce-vue\";\r\nimport \"tinymce/icons/default/icons\";\r\nimport \"tinymce/themes/silver\";\r\nimport \"tinymce/plugins/image\";\r\nimport \"tinymce/plugins/media\";\r\nimport \"tinymce/plugins/table\";\r\nimport \"tinymce/plugins/lists\";\r\nimport \"tinymce/plugins/contextmenu\";\r\nimport \"tinymce/plugins/wordcount\";\r\nimport \"tinymce/plugins/colorpicker\";\r\nimport \"tinymce/plugins/textcolor\";\r\nimport \"tinymce/plugins/preview\";\r\nimport \"tinymce/plugins/code\";\r\nimport \"tinymce/plugins/link\";\r\nimport \"tinymce/plugins/advlist\";\r\nimport \"tinymce/plugins/codesample\";\r\nimport \"tinymce/plugins/hr\";\r\nimport \"tinymce/plugins/fullscreen\";\r\nimport \"tinymce/plugins/textpattern\";\r\nimport \"tinymce/plugins/searchreplace\";\r\nimport \"tinymce/plugins/autolink\";\r\nimport \"tinymce/plugins/directionality\";\r\nimport \"tinymce/plugins/visualblocks\";\r\nimport \"tinymce/plugins/visualchars\";\r\nimport \"tinymce/plugins/template\";\r\nimport \"tinymce/plugins/charmap\";\r\nimport \"tinymce/plugins/nonbreaking\";\r\nimport \"tinymce/plugins/insertdatetime\";\r\nimport \"tinymce/plugins/imagetools\";\r\nimport \"tinymce/plugins/autosave\";\r\nimport \"tinymce/plugins/autoresize\";\r\nimport { getToken } from \"@/utils/auth\";\r\n\r\nexport default {\r\n  name: \"tinymce-editor\",\r\n  components: {\r\n    Editor,\r\n  },\r\n  props: {\r\n    value: {\r\n      type: String,\r\n      default: \"\",\r\n    },\r\n    disabled: {\r\n      type: Boolean,\r\n      default: false,\r\n    },\r\n    plugins: {\r\n      type: [String, Array],\r\n      default:\r\n        \"preview searchreplace autolink directionality visualblocks visualchars fullscreen image link media code codesample table charmap hr nonbreaking insertdatetime advlist lists wordcount imagetools textpattern autosave autoresize\",\r\n    },\r\n    toolbar: {\r\n      type: [String, Array],\r\n      default:\r\n        \"code undo redo restoredraft | cut copy paste pastetext | forecolor backcolor bold italic underline strikethrough link codesample | alignleft aligncenter alignright alignjustify outdent indent formatpainter | \\\r\n      styleselect formatselect fontselect fontsizeselect | bullist numlist | blockquote subscript superscript removeformat | \\\r\n      table image media charmap hr pagebreak insertdatetime | fullscreen preview\",\r\n      // 图片和视频  image media\r\n    },\r\n  },\r\n  data() {\r\n    return {\r\n      //初始化配置\r\n      init: {\r\n        menubar: true, // 菜单栏显隐\r\n        language_url: `${process.env.BASE_URL}tinymce/langs/zh_CN.js`,\r\n        language: \"zh_CN\",\r\n        skin_url: `${process.env.BASE_URL}tinymce/skins/ui/oxide`,\r\n        min_height: 250,\r\n        max_height: 500,\r\n        // toolbar_mode: \"floating\",\r\n        plugins: this.plugins,\r\n        toolbar: this.toolbar,\r\n        content_style: \"p {margin: 5px 0;}\",\r\n        fontsize_formats: \"12px 14px 16px 18px 24px 36px 48px 56px 72px\",\r\n        font_formats:\r\n          \"微软雅黑=Microsoft YaHei,Helvetica Neue,PingFang SC,sans-serif;苹果苹方=PingFang SC,Microsoft YaHei,sans-serif;宋体=simsun,serif;仿宋体=FangSong,serif;黑体=SimHei,sans-serif;Arial=arial,helvetica,sans-serif;Arial Black=arial black,avant garde;Book Antiqua=book antiqua,palatino;\",\r\n        branding: false,\r\n        // 图片上传\r\n        images_upload_handler: (blobInfo, success, failure) => {\r\n          const formData = new FormData();\r\n          formData.append(\"file\", blobInfo.blob());\r\n          axios.post(\r\n            process.env.VUE_APP_BASE_API + '/common/zxUpload',\r\n            formData,\r\n            {\r\n              headers: {\r\n                Accept: \"*/*\",\r\n                \"Content-Type\": \"multipart/form-data\",\r\n                Authorization: \"Bearer \" + getToken(),\r\n              },\r\n            }\r\n          )\r\n            .then((res) => {\r\n              console.log('res',res)\r\n              if (res.status === 200) {\r\n                const file = res.data;\r\n                console.log(file, 11111);\r\n                success(file.url);\r\n                return;\r\n              }\r\n              failure(\"上传成功\");\r\n            })\r\n            .catch(() => {\r\n              failure(\"上传出错\");\r\n            });\r\n        },\r\n      },\r\n      content: this.value,\r\n    };\r\n  },\r\n  mounted() {\r\n    tinymce.init({});\r\n\r\n  },\r\n  computed: {\r\n    uploadImgUrl() {\r\n      return process.env.VUE_APP_BASE_API + 'common/zxUpload'\r\n    }\r\n  },\r\n  methods: {},\r\n  watch: {\r\n    value(newValue) {\r\n      this.content = newValue;\r\n    },\r\n    content(newValue) {\r\n      this.$emit(\"input\", newValue);\r\n    },\r\n  },\r\n};\r\n</script>\r\n<style scoped lang=\"scss\"></style>"]}]}