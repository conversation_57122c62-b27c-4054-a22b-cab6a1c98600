{"remainingRequest": "D:\\shangchen\\casz-fat-j21\\casz-fat-ui\\node_modules\\babel-loader\\lib\\index.js!D:\\shangchen\\casz-fat-j21\\casz-fat-ui\\node_modules\\eslint-loader\\index.js??ref--13-0!D:\\shangchen\\casz-fat-j21\\casz-fat-ui\\src\\api\\exchange\\questions.js", "dependencies": [{"path": "D:\\shangchen\\casz-fat-j21\\casz-fat-ui\\src\\api\\exchange\\questions.js", "mtime": 1752668934322}, {"path": "D:\\shangchen\\casz-fat-j21\\casz-fat-ui\\babel.config.js", "mtime": 1752668935560}, {"path": "D:\\shangchen\\casz-fat-j21\\casz-fat-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1747273083419}, {"path": "D:\\shangchen\\casz-fat-j21\\casz-fat-ui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1747273094151}, {"path": "D:\\shangchen\\casz-fat-j21\\casz-fat-ui\\node_modules\\eslint-loader\\index.js", "mtime": 1747273087917}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:InVzZSBzdHJpY3QiOwoKdmFyIF9pbnRlcm9wUmVxdWlyZURlZmF1bHQgPSByZXF1aXJlKCJEOi9zaGFuZ2NoZW4vY2Fzei1mYXQtajIxL2Nhc3otZmF0LXVpL25vZGVfbW9kdWxlcy9AYmFiZWwvcnVudGltZS9oZWxwZXJzL2ludGVyb3BSZXF1aXJlRGVmYXVsdC5qcyIpLmRlZmF1bHQ7Ck9iamVjdC5kZWZpbmVQcm9wZXJ0eShleHBvcnRzLCAiX19lc01vZHVsZSIsIHsKICB2YWx1ZTogdHJ1ZQp9KTsKZXhwb3J0cy5hZGRRdWVzdGlvbnMgPSBhZGRRdWVzdGlvbnM7CmV4cG9ydHMuZGVsUXVlc3Rpb25zID0gZGVsUXVlc3Rpb25zOwpleHBvcnRzLmdldFF1ZXN0aW9ucyA9IGdldFF1ZXN0aW9uczsKZXhwb3J0cy5saXN0UXVlc3Rpb25zID0gbGlzdFF1ZXN0aW9uczsKZXhwb3J0cy51cGRhdGVRdWVzdGlvbnMgPSB1cGRhdGVRdWVzdGlvbnM7CnZhciBfcmVxdWVzdCA9IF9pbnRlcm9wUmVxdWlyZURlZmF1bHQocmVxdWlyZSgiQC91dGlscy9yZXF1ZXN0IikpOwovLyDmn6Xor6Lpl67popjlkqjor6LliJfooagKZnVuY3Rpb24gbGlzdFF1ZXN0aW9ucyhxdWVyeSkgewogIHJldHVybiAoMCwgX3JlcXVlc3QuZGVmYXVsdCkoewogICAgdXJsOiAnL2V4Y2hhbmdlL3F1ZXN0aW9ucy9saXN0JywKICAgIG1ldGhvZDogJ2dldCcsCiAgICBwYXJhbXM6IHF1ZXJ5CiAgfSk7Cn0KCi8vIOafpeivoumXrumimOWSqOivouivpue7hgpmdW5jdGlvbiBnZXRRdWVzdGlvbnMoaWQpIHsKICByZXR1cm4gKDAsIF9yZXF1ZXN0LmRlZmF1bHQpKHsKICAgIHVybDogJy9leGNoYW5nZS9xdWVzdGlvbnMvJyArIGlkLAogICAgbWV0aG9kOiAnZ2V0JwogIH0pOwp9CgovLyDmlrDlop7pl67popjlkqjor6IKZnVuY3Rpb24gYWRkUXVlc3Rpb25zKGRhdGEpIHsKICByZXR1cm4gKDAsIF9yZXF1ZXN0LmRlZmF1bHQpKHsKICAgIHVybDogJy9leGNoYW5nZS9xdWVzdGlvbnMnLAogICAgbWV0aG9kOiAncG9zdCcsCiAgICBkYXRhOiBkYXRhCiAgfSk7Cn0KCi8vIOS/ruaUuemXrumimOWSqOivogpmdW5jdGlvbiB1cGRhdGVRdWVzdGlvbnMoZGF0YSkgewogIHJldHVybiAoMCwgX3JlcXVlc3QuZGVmYXVsdCkoewogICAgdXJsOiAnL2V4Y2hhbmdlL3F1ZXN0aW9ucycsCiAgICBtZXRob2Q6ICdwdXQnLAogICAgZGF0YTogZGF0YQogIH0pOwp9CgovLyDliKDpmaTpl67popjlkqjor6IKZnVuY3Rpb24gZGVsUXVlc3Rpb25zKGlkKSB7CiAgcmV0dXJuICgwLCBfcmVxdWVzdC5kZWZhdWx0KSh7CiAgICB1cmw6ICcvZXhjaGFuZ2UvcXVlc3Rpb25zLycgKyBpZCwKICAgIG1ldGhvZDogJ2RlbGV0ZScKICB9KTsKfQ=="}, {"version": 3, "names": ["_request", "_interopRequireDefault", "require", "listQuestions", "query", "request", "url", "method", "params", "getQuestions", "id", "addQuestions", "data", "updateQuestions", "delQuestions"], "sources": ["D:/shangchen/casz-fat-j21/casz-fat-ui/src/api/exchange/questions.js"], "sourcesContent": ["import request from '@/utils/request'\r\n\r\n// 查询问题咨询列表\r\nexport function listQuestions(query) {\r\n    return request({\r\n        url: '/exchange/questions/list',\r\n        method: 'get',\r\n        params: query\r\n    })\r\n}\r\n\r\n// 查询问题咨询详细\r\nexport function getQuestions(id) {\r\n    return request({\r\n        url: '/exchange/questions/' + id,\r\n        method: 'get'\r\n    })\r\n}\r\n\r\n// 新增问题咨询\r\nexport function addQuestions(data) {\r\n    return request({\r\n        url: '/exchange/questions',\r\n        method: 'post',\r\n        data: data\r\n    })\r\n}\r\n\r\n// 修改问题咨询\r\nexport function updateQuestions(data) {\r\n    return request({\r\n        url: '/exchange/questions',\r\n        method: 'put',\r\n        data: data\r\n    })\r\n}\r\n\r\n// 删除问题咨询\r\nexport function delQuestions(id) {\r\n    return request({\r\n        url: '/exchange/questions/' + id,\r\n        method: 'delete'\r\n    })\r\n}\r\n"], "mappings": ";;;;;;;;;;;AAAA,IAAAA,QAAA,GAAAC,sBAAA,CAAAC,OAAA;AAEA;AACO,SAASC,aAAaA,CAACC,KAAK,EAAE;EACjC,OAAO,IAAAC,gBAAO,EAAC;IACXC,GAAG,EAAE,0BAA0B;IAC/BC,MAAM,EAAE,KAAK;IACbC,MAAM,EAAEJ;EACZ,CAAC,CAAC;AACN;;AAEA;AACO,SAASK,YAAYA,CAACC,EAAE,EAAE;EAC7B,OAAO,IAAAL,gBAAO,EAAC;IACXC,GAAG,EAAE,sBAAsB,GAAGI,EAAE;IAChCH,MAAM,EAAE;EACZ,CAAC,CAAC;AACN;;AAEA;AACO,SAASI,YAAYA,CAACC,IAAI,EAAE;EAC/B,OAAO,IAAAP,gBAAO,EAAC;IACXC,GAAG,EAAE,qBAAqB;IAC1BC,MAAM,EAAE,MAAM;IACdK,IAAI,EAAEA;EACV,CAAC,CAAC;AACN;;AAEA;AACO,SAASC,eAAeA,CAACD,IAAI,EAAE;EAClC,OAAO,IAAAP,gBAAO,EAAC;IACXC,GAAG,EAAE,qBAAqB;IAC1BC,MAAM,EAAE,KAAK;IACbK,IAAI,EAAEA;EACV,CAAC,CAAC;AACN;;AAEA;AACO,SAASE,YAAYA,CAACJ,EAAE,EAAE;EAC7B,OAAO,IAAAL,gBAAO,EAAC;IACXC,GAAG,EAAE,sBAAsB,GAAGI,EAAE;IAChCH,MAAM,EAAE;EACZ,CAAC,CAAC;AACN", "ignoreList": []}]}