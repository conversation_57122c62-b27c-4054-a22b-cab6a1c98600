{"remainingRequest": "D:\\shangchen\\casz-fat-j21\\casz-fat-ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\shangchen\\casz-fat-j21\\casz-fat-ui\\src\\views\\cgm\\config\\index.vue?vue&type=template&id=8c3623b6&scoped=true", "dependencies": [{"path": "D:\\shangchen\\casz-fat-j21\\casz-fat-ui\\src\\views\\cgm\\config\\index.vue", "mtime": 1752668935350}, {"path": "D:\\shangchen\\casz-fat-j21\\casz-fat-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1747273083419}, {"path": "D:\\shangchen\\casz-fat-j21\\casz-fat-ui\\node_modules\\vue-loader\\lib\\loaders\\templateLoader.js", "mtime": 1747273107391}, {"path": "D:\\shangchen\\casz-fat-j21\\casz-fat-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1747273083419}, {"path": "D:\\shangchen\\casz-fat-j21\\casz-fat-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1747273098384}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, null]}