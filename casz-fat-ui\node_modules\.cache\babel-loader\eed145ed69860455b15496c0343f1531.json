{"remainingRequest": "D:\\shangchen\\casz-fat-j21\\casz-fat-ui\\node_modules\\babel-loader\\lib\\index.js!D:\\shangchen\\casz-fat-j21\\casz-fat-ui\\node_modules\\eslint-loader\\index.js??ref--13-0!D:\\shangchen\\casz-fat-j21\\casz-fat-ui\\src\\api\\equip\\equipment.js", "dependencies": [{"path": "D:\\shangchen\\casz-fat-j21\\casz-fat-ui\\src\\api\\equip\\equipment.js", "mtime": 1752668934320}, {"path": "D:\\shangchen\\casz-fat-j21\\casz-fat-ui\\babel.config.js", "mtime": 1752668935560}, {"path": "D:\\shangchen\\casz-fat-j21\\casz-fat-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1747273083419}, {"path": "D:\\shangchen\\casz-fat-j21\\casz-fat-ui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1747273094151}, {"path": "D:\\shangchen\\casz-fat-j21\\casz-fat-ui\\node_modules\\eslint-loader\\index.js", "mtime": 1747273087917}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:InVzZSBzdHJpY3QiOwoKdmFyIF9pbnRlcm9wUmVxdWlyZURlZmF1bHQgPSByZXF1aXJlKCJEOi9zaGFuZ2NoZW4vY2Fzei1mYXQtajIxL2Nhc3otZmF0LXVpL25vZGVfbW9kdWxlcy9AYmFiZWwvcnVudGltZS9oZWxwZXJzL2ludGVyb3BSZXF1aXJlRGVmYXVsdC5qcyIpLmRlZmF1bHQ7Ck9iamVjdC5kZWZpbmVQcm9wZXJ0eShleHBvcnRzLCAiX19lc01vZHVsZSIsIHsKICB2YWx1ZTogdHJ1ZQp9KTsKZXhwb3J0cy5hZGRFcXVpcG1lbnQgPSBhZGRFcXVpcG1lbnQ7CmV4cG9ydHMuZGVsRXF1aXBtZW50ID0gZGVsRXF1aXBtZW50OwpleHBvcnRzLmdldEVxdWlwbWVudCA9IGdldEVxdWlwbWVudDsKZXhwb3J0cy5saXN0RXF1aXBtZW50ID0gbGlzdEVxdWlwbWVudDsKZXhwb3J0cy51cGRhdGVFcXVpcG1lbnQgPSB1cGRhdGVFcXVpcG1lbnQ7CnZhciBfcmVxdWVzdCA9IF9pbnRlcm9wUmVxdWlyZURlZmF1bHQocmVxdWlyZSgiQC91dGlscy9yZXF1ZXN0IikpOwovLyDmn6Xor6Lorr7lpIfnrqHnkIbliJfooagKZnVuY3Rpb24gbGlzdEVxdWlwbWVudChxdWVyeSkgewogIHJldHVybiAoMCwgX3JlcXVlc3QuZGVmYXVsdCkoewogICAgdXJsOiAnL2VxdWlwL2VxdWlwbWVudC9saXN0JywKICAgIG1ldGhvZDogJ2dldCcsCiAgICBwYXJhbXM6IHF1ZXJ5CiAgfSk7Cn0KCi8vIOafpeivouiuvuWkh+euoeeQhuivpue7hgpmdW5jdGlvbiBnZXRFcXVpcG1lbnQoaWQpIHsKICByZXR1cm4gKDAsIF9yZXF1ZXN0LmRlZmF1bHQpKHsKICAgIHVybDogJy9lcXVpcC9lcXVpcG1lbnQvJyArIGlkLAogICAgbWV0aG9kOiAnZ2V0JwogIH0pOwp9CgovLyDmlrDlop7orr7lpIfnrqHnkIYKZnVuY3Rpb24gYWRkRXF1aXBtZW50KGRhdGEpIHsKICByZXR1cm4gKDAsIF9yZXF1ZXN0LmRlZmF1bHQpKHsKICAgIHVybDogJy9lcXVpcC9lcXVpcG1lbnQnLAogICAgbWV0aG9kOiAncG9zdCcsCiAgICBkYXRhOiBkYXRhCiAgfSk7Cn0KCi8vIOS/ruaUueiuvuWkh+euoeeQhgpmdW5jdGlvbiB1cGRhdGVFcXVpcG1lbnQoZGF0YSkgewogIHJldHVybiAoMCwgX3JlcXVlc3QuZGVmYXVsdCkoewogICAgdXJsOiAnL2VxdWlwL2VxdWlwbWVudCcsCiAgICBtZXRob2Q6ICdwdXQnLAogICAgZGF0YTogZGF0YQogIH0pOwp9CgovLyDliKDpmaTorr7lpIfnrqHnkIYKZnVuY3Rpb24gZGVsRXF1aXBtZW50KGlkKSB7CiAgcmV0dXJuICgwLCBfcmVxdWVzdC5kZWZhdWx0KSh7CiAgICB1cmw6ICcvZXF1aXAvZXF1aXBtZW50LycgKyBpZCwKICAgIG1ldGhvZDogJ2RlbGV0ZScKICB9KTsKfQ=="}, {"version": 3, "names": ["_request", "_interopRequireDefault", "require", "listEquipment", "query", "request", "url", "method", "params", "getEquipment", "id", "addEquipment", "data", "updateEquipment", "delEquipment"], "sources": ["D:/shangchen/casz-fat-j21/casz-fat-ui/src/api/equip/equipment.js"], "sourcesContent": ["import request from '@/utils/request'\r\n\r\n// 查询设备管理列表\r\nexport function listEquipment(query) {\r\n  return request({\r\n    url: '/equip/equipment/list',\r\n    method: 'get',\r\n    params: query\r\n  })\r\n}\r\n\r\n// 查询设备管理详细\r\nexport function getEquipment(id) {\r\n  return request({\r\n    url: '/equip/equipment/' + id,\r\n    method: 'get'\r\n  })\r\n}\r\n\r\n// 新增设备管理\r\nexport function addEquipment(data) {\r\n  return request({\r\n    url: '/equip/equipment',\r\n    method: 'post',\r\n    data: data\r\n  })\r\n}\r\n\r\n// 修改设备管理\r\nexport function updateEquipment(data) {\r\n  return request({\r\n    url: '/equip/equipment',\r\n    method: 'put',\r\n    data: data\r\n  })\r\n}\r\n\r\n// 删除设备管理\r\nexport function delEquipment(id) {\r\n  return request({\r\n    url: '/equip/equipment/' + id,\r\n    method: 'delete'\r\n  })\r\n}\r\n"], "mappings": ";;;;;;;;;;;AAAA,IAAAA,QAAA,GAAAC,sBAAA,CAAAC,OAAA;AAEA;AACO,SAASC,aAAaA,CAACC,KAAK,EAAE;EACnC,OAAO,IAAAC,gBAAO,EAAC;IACbC,GAAG,EAAE,uBAAuB;IAC5BC,MAAM,EAAE,KAAK;IACbC,MAAM,EAAEJ;EACV,CAAC,CAAC;AACJ;;AAEA;AACO,SAASK,YAAYA,CAACC,EAAE,EAAE;EAC/B,OAAO,IAAAL,gBAAO,EAAC;IACbC,GAAG,EAAE,mBAAmB,GAAGI,EAAE;IAC7BH,MAAM,EAAE;EACV,CAAC,CAAC;AACJ;;AAEA;AACO,SAASI,YAAYA,CAACC,IAAI,EAAE;EACjC,OAAO,IAAAP,gBAAO,EAAC;IACbC,GAAG,EAAE,kBAAkB;IACvBC,MAAM,EAAE,MAAM;IACdK,IAAI,EAAEA;EACR,CAAC,CAAC;AACJ;;AAEA;AACO,SAASC,eAAeA,CAACD,IAAI,EAAE;EACpC,OAAO,IAAAP,gBAAO,EAAC;IACbC,GAAG,EAAE,kBAAkB;IACvBC,MAAM,EAAE,KAAK;IACbK,IAAI,EAAEA;EACR,CAAC,CAAC;AACJ;;AAEA;AACO,SAASE,YAAYA,CAACJ,EAAE,EAAE;EAC/B,OAAO,IAAAL,gBAAO,EAAC;IACbC,GAAG,EAAE,mBAAmB,GAAGI,EAAE;IAC7BH,MAAM,EAAE;EACV,CAAC,CAAC;AACJ", "ignoreList": []}]}