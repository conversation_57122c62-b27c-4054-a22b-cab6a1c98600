<template>

	<view>
		<!-- 单行文本框 -->
		<view v-if="
					  item.compType === 'text' ||
					  item.compType === 'number' ||
					  item.compType === 'code'||
					  item.compType === 'input'
					">
			<view style="text-align: center;display: inline;line-height: 32px;" v-if="item.compType==='text'">
				{{item.text}}
			</view>

			<uni-forms-item v-else :label="item.showLabel?item.label:''" label-width="auto" :required="item.required">
				<uni-easyinput v-model="elementVal" :placeholder="item.placeholder?item.placeholder:'请输入'"
					@input="inputVal(item,$event)" />
			</uni-forms-item>
		</view>
		<!-- 下拉选择-->
		<template v-else-if="item.compType === 'select'">
			<uni-forms-item :label="item.showLabel?item.label:''" label-width="auto" :required="item.required">
				<uni-data-select :placeholder="item.placeholder?item.placeholder:'请选择'" v-model="elementVal"
					:localdata="selectArr" @change="selectConfirm($event, item)"></uni-data-select>
			</uni-forms-item>
		</template>

		<block v-else-if="item.compType === 'datetime'">
			<uni-forms-item :label="item.showLabel?item.label:''" label-width="auto" :required="item.required">
				<view @click="showDateTimePicker(item)">
					<uni-easyinput v-model="elementVal" :placeholder="item.placeholder || '请选择日期时间'" readonly />
				</view>
				<u-datetime-picker
					:show="showDateTimePickerFlag"
					mode="datetime"
					:value="datePickerValue"
					:minDate="new Date('1900-01-01').getTime()"
					:maxDate="new Date('2100-12-31').getTime()"
					@confirm="selectDateTime($event, item)"
					@close="closeDateTimePicker(item)"
					@cancel="closeDateTimePicker(item)" />
			</uni-forms-item>
		</block>

		<template v-else-if="item.compType === 'calendar'||item.compType==='date'">
			<uni-forms-item :label="item.showLabel?item.label:''" label-width="auto" :required="item.required">
				<view @click="showDatePicker(item)">
					<uni-easyinput v-model="elementVal" :placeholder="item.placeholder || '请选择日期'" readonly />
				</view>
				<u-datetime-picker
					:show="showDatePickerFlag"
					mode="date"
					:value="datePickerValue"
					:minDate="new Date('1900-01-01').getTime()"
					:maxDate="new Date('2100-12-31').getTime()"
					@confirm="selectDateTime($event, item)"
					@close="closeDatePicker(item)"
					@cancel="closeDatePicker(item)" />
			</uni-forms-item>
		</template>

		<!-- 多行文本框 -->
		<view v-else-if="item.compType === 'textarea'">
			<uni-forms-item :label="item.showLabel?item.label:''" label-width="auto" :required="item.required">
				<uni-easyinput type="textarea" v-model="elementVal" :placeholder="item.placeholder"
					@input="inputVal(item,$event)" />
			</uni-forms-item>
		</view>
		<!-- 上传图片 -->
		<view class="img-box pt30 flex-col-l" v-else-if="item.compType === 'file'">
			<view class="font26" :class="item.rules.verify ? '' : 'p-l14 '">
				<text class="colorRed" v-if="item.required">*</text>
				{{item.showLabel?item.label:'' }}
			</view>
			<view class="img-upload p30">
				<u-upload :fileList="form[item.rules.name]" :disabled="item.disabled" :accept="item.accept"
					:capture="item.capture" :maxCount="item.maxCount" :sizeType="item.sizeType"
					:compressed="item.compressed" :camera="item.camera" :multiple="item.multiple"
					:maxSize="item.maxSize" :previewImage="item.previewImage" width="150rpx" height="150rpx"
					@afterRead="afterRead($event, item)" @delete="deletePic($event, item)"></u-upload>
			</view>
		</view>

		<!-- 单选框 -->
		<view v-else-if="item.compType === 'radio'">
			<uni-forms-item :label="item.showLabel?item.label:''" label-width="auto" :required="item.required">
				<uni-data-checkbox v-model="elementVal" :map="{text:'label',value:'value'}" style="line-height: 30px;" :localdata="item.options"
					@change="radioChange(item.id,$event)" />
			</uni-forms-item>
		</view>
		<!-- 多选框 -->
		<view class="line-col" v-else-if="item.compType === 'checkbox'">
			<uni-forms-item :label="item.showLabel?item.label:''" label-width="auto" :required="item.required">
				<!-- <uni-data-checkbox v-model="elementVal" :map="{text:'label',value:'value'}" multiple  :localdata="item.options"  @change="checkboxGroupChange($event, item)" /> -->
				<!-- <view>{{elementVal}}</view> -->
				<checkbox-group @change="checkboxGroupChange($event, item)"  v-if="item.vertical">
					<label style="justify-content: flex-start;display: flex;line-height: 40px;"
						class="uni-list-cell uni-list-cell-pd" v-for="item in item.options" :key="item.value">
						<view>
							<checkbox color="#0089ff" :value="item.value" :checked="checkBoxChecked(item.value)" />
						</view>
						<view>{{item.label}}</view>
					</label>
				</checkbox-group>
				<checkbox-group v-else @change="checkboxGroupChange($event, item)" style="text-align: left;line-height: 30px;">
					<label  v-for="item in item.options" :key="item.value" style="margin-right: 10px;">
						<checkbox color="#0089ff" :value="item.value" :checked="checkBoxChecked(item.value)" />{{item.label}}
					</label>
				</checkbox-group>
			</uni-forms-item>
		</view>

		<!-- 级联选择器 -->
		<view v-else-if="item.compType === 'cascader'">
			<uni-forms-item :label="item.showLabel?item.label:''" label-width="auto" :required="item.required">
				<uni-data-picker
					v-model="elementVal"
					:placeholder="item.placeholder || '请选择'"
					:localdata="cascaderOptions"
					:map="cascaderMap"
					:popup-title="item.popupTitle || '请选择'"
					:split="item.split || '/'"
					:readonly="item.disabled"
					@change="cascaderChange($event, item)"
					@nodeclick="cascaderNodeClick($event, item)">
				</uni-data-picker>
			</uni-forms-item>
		</view>

		<!-- 手机输入框 -->
		<view class="line" v-else-if="item.compType === 'mobile'">
			<view :class="item.rules.verify ? 'line-left' : 'p-l14 line-left'">
				<text class="colorRed" v-if="item.required">*</text>
				{{ item.label }}
			</view>
			<view class="line-right pr20">
				<input type="number" v-model="elementVal" :placeholder="item.placeholder"
					@input="inputVal(item,elementVal)" class="input" :disabled="item.disabled" :maxlength="11"
					placeholder-class="plaClass" />
				<view style="width: 200rpx" v-if="item.oneKeyPhone">
					<u-button size="mini" type="primary" open-type="getPhoneNumber" @getphonenumber="getphonenumber"
						:disabled="item.disabled">一键获取
					</u-button>
				</view>
			</view>
		</view>
	</view>
</template>

<script>
	import value from "@/uni_modules/uview-ui/components/u-text/value"
	import mixins from "./mixins";
	import * as DictApi from "@/api/dict"
	import checkRules from "@/pageWork/components/active-form/checkRules.js"
	export default {
		name: "activeForm",
		mixins: [mixins],
		props: {
			//是否展示序号
			item: {
				type: Object,
				default: () => {
					return {};
				}
			},
			num: {
				type: Boolean,
				default: false,
			},
			value: {
				required: false
			},
			formData: {
				type: Array,
				default: () => {
					return [];
				},
			},
			//是否编辑表单
			isEdit: {
				type: Boolean,
				default: false,
			},
			//是否能删除
			isDel: {
				type: Boolean,
				default: false,
			},
			screenFormId: {
				type: String,
				default: null
			}
		},
		data() {
			return {
				submitData: "",
				selectBox: [],
				currentSelectIndex: "",
				currentSelectValue: "",
				codeFont: "获取验证码",
				wait: 60,
				isSend: false,
				form: {}, //form//表单
				show: false,
				selectArr: [],
				allSelectLength: '',
				elementVal: null,
				elementLabel: null,
				showDatePickerFlag: false,
				showDateTimePickerFlag: false,
				datePickerValue: '',
				cascaderOptions: [], // 级联选择器数据
				cascaderMap: { // 级联选择器字段映射
					text: 'label',
					value: 'value',
					children: 'children'
				}
			};
		},
		computed: {

			checkBoxChecked() {
				return function(ava) {
					if (this.elementVal) {
						return this.elementVal.indexOf(ava + "") != -1
					} else {
						return false;
					}
				}

			}
		},

		watch: {
			value: {
				handler(newVal, oldVal) {
					this.dataFormat();
					// 日期组件特殊处理
					if (this.item.compType === 'date') {
						console.log('日期组件 value 变化:', {
							newVal,
							oldVal,
							elementVal: this.elementVal
						});
					}
				},
				deep: true,
				immediate: true
			}
		},
		mounted() {
			if (this.item.compType == 'select') {
				this.getActions(this.item)
			}
			if (this.item.compType == 'checkbox') {

			}
			if (this.item.compType == 'cascader') {
				this.getCascaderActions(this.item)
			}
			this.elementVal = this.value
			this.form[this.item.id] = this.value
			this.dataFormat();

			// 添加调试信息
			if (this.item.compType === 'date') {
				console.log('日期组件调试信息:', {
					itemId: this.item.id,
					itemLabel: this.item.label,
					value: this.value,
					elementVal: this.elementVal,
					item: this.item
				});
			}
		},
		methods: {
			async getActions(item) {
				console.log("getActions", item)
				let arr = [];
				if (item.dataType === 'dynamic') {
					const key = item.action;
					let result = await DictApi.getDictDataByDictType(key)
					console.log(result)
					if (result.code == 200 && result.data && result.data.length > 0) {
						result.data.forEach(item => {
							const {
								dictLabel,
								dictValue
							} = item;
							arr.push({
								name: dictLabel,
								value: dictValue,
								text: dictLabel
							})
						})
					}
				} else { //static
					let options = item.options;
					options.forEach(item => {
						const {
							label,
							value
						} = item;
						arr.push({
							name: label,
							value: value,
							text: label
						})
					})

				}
				item.list = arr;
				this.selectArr = arr;
				if (item.compType == 'select') {
					this.showSelectDefaultValue(item)
				}
				return arr
			},
			//初始化表单
			dataFormat() {
				let formValue = this.value;
				this.form[this.item.id] = this.value
				this.elementVal = this.value  // 添加这行来同步 elementVal
				this.$forceUpdate()
			},
			//提取表单key和value
			extractRules(formData) {
				const extractedRules = {};
				formData.forEach((field) => {
					const {
						rules
					} = field;
					if (rules && rules.name) {
						extractedRules[rules.name] = rules.value;
					}
				});
				return extractedRules;
			},
			// 删除图片
			deletePic($event, item) {
				item.rules.value.splice($event.index, 1);
				this.form[item.rules.name] = item.rules.value;
				this.$emit("input", this.form);
				uni.$emit("form-input", this.screenFormId, item.id, $event)
			},
			// 新增图片
			afterRead($event, item) {
				// 当设置 mutiple 为 true 时, file 为数组格式，否则为对象格式
				item.rules.value = item.multiple ? $event.file : [$event.file];
				this.form[item.rules.name] = item.rules.value;
				this.$emit("input", this.form);
				uni.$emit("form-input", this.screenFormId, item.id, $event)
			},
			//显示select
			showSelect(item) {
				item.show = true;
				this.$forceUpdate();
			},
			//input输入框的值传给父组件
			inputVal(item, val) {
				console.log("inputVal", item, val)
				this.$emit("input", item.id, val);
				uni.$emit("form-input", this.screenFormId, item.id, val)
			},
			// 单选 下拉框点击确定
			selectConfirm($event, item) {
				console.log("selectConfirm", $event, item)
				this.elementVal = $event;
				this.$emit("input", item.id, $event);
				uni.$emit("form-input", this.screenFormId, item.id, $event)
				this.$forceUpdate();
			},
			selectCalendar(e, item) {
				// console.log("selectCalendar", $event, item)
				this.form[item.id] = e;
				this.elementVal = e;  // 添加这行来同步 elementVal
				this.$emit("input", item.id, e);
				uni.$emit("form-input", this.screenFormId, item.id, e)
				console.log("selectCalendar", e, this.form)
				this.$forceUpdate();
			},
			// 显示日期选择器
			showDatePicker(item) {
				console.log("showDatePicker", item);
				this.showDatePickerFlag = true;
				// 设置当前值到选择器
				if (this.elementVal) {
					this.datePickerValue = new Date(this.elementVal).getTime();
				} else {
					this.datePickerValue = new Date().getTime();
				}
			},
			// 关闭日期选择器
			closeDatePicker(item) {
				console.log("closeDatePicker", item);
				this.showDatePickerFlag = false;
			},
			// 显示日期时间选择器
			showDateTimePicker(item) {
				console.log("showDateTimePicker", item);
				this.showDateTimePickerFlag = true;
				// 设置当前值到选择器
				if (this.elementVal) {
					this.datePickerValue = new Date(this.elementVal).getTime();
				} else {
					this.datePickerValue = new Date().getTime();
				}
			},
			// 关闭日期时间选择器
			closeDateTimePicker(item) {
				console.log("closeDateTimePicker", item);
				this.showDateTimePickerFlag = false;
			},
			// value:返回所选时间戳，mode:当前模式
			selectDateTime($event, item) {
				console.log("selectDateTime", $event, item);

				// u-datetime-picker 返回的是对象 {value: timestamp, mode: "date"}
				let timestamp = $event.value || $event;

				// 根据组件类型格式化显示值
				if (item.compType === 'date' || item.compType === 'calendar') {
					this.elementVal = this.formatTime(timestamp, 'date');
				} else if (item.compType === 'datetime') {
					this.elementVal = this.formatTime(timestamp, 'datetime');
				} else {
					this.elementVal = timestamp;
				}

				// 关闭选择器
				this.showDatePickerFlag = false;
				this.showDateTimePickerFlag = false;

				// 触发事件
				this.$emit("input", item.id, this.elementVal);
				uni.$emit("form-input", this.screenFormId, item.id, this.elementVal);

				console.log("selectDateTime result", {
					timestamp,
					elementVal: this.elementVal,
					itemId: item.id
				});

				this.$forceUpdate();
			},
			// 帮我更具 参数	date为日期选择，time为时间选择，year-month为年月选择 ,datetime日期时间选择 分别把时间搓转换成对应的格式
			formatTime(timestamp, formatType) {
				console.log("formatTime input:", timestamp, formatType);

				// 确保 timestamp 是数字
				const numTimestamp = Number(timestamp);
				if (isNaN(numTimestamp)) {
					console.error("Invalid timestamp:", timestamp);
					return "";
				}

				const date = new Date(numTimestamp);
				console.log("formatTime date:", date);

				if (isNaN(date.getTime())) {
					console.error("Invalid date:", date);
					return "";
				}
				let year = date.getFullYear();
				let month = String(date.getMonth() + 1).padStart(2, "0");
				let day = String(date.getDate()).padStart(2, "0");
				let hours = String(date.getHours()).padStart(2, "0");
				let minutes = String(date.getMinutes()).padStart(2, "0");
				let seconds = String(date.getSeconds()).padStart(2, "0");

				switch (formatType) {
					case "date":
						return `${year}-${month}-${day}`;
					case "time":
						return `${hours}:${minutes}`; //:${seconds}
					case "year-month":
						return `${year}-${month}`;
					case "datetime":
						return `${year}-${month}-${day} ${hours}:${minutes}:${seconds}`;
					default:
						return "Invalid Format Type";
				}
			},
			// 根据给定的时间（"11:00"）获取当前日期和指定时间的时间戳
			getTimestampFromTime(inputTime) {
				// 获取当前日期
				const currentDate = new Date();

				// 将输入时间字符串拆分成小时和分钟
				const [hours, minutes] = inputTime.split(":");

				// 创建一个新日期对象，设置时间为输入的小时和分钟
				const targetDate = new Date(
					currentDate.getFullYear(),
					currentDate.getMonth(),
					currentDate.getDate(),
					hours,
					minutes
				);

				// 获取时间戳
				const timestamp = targetDate.getTime();

				return timestamp;
			},
			selectClose(item) {
				item.show = false;
				this.$forceUpdate();
			},
			//单选 点击触发
			radioChange(key, e) {
				console.log("radioChange", JSON.stringify(e), this.form)
				this.$emit("input", key, e.detail.value);
				uni.$emit("form-input", this.screenFormId, key, e.detail.value)
			},
			//复选框 点击触发
			checkboxGroupChange(e, item) {
				console.log("checkboxGroupChange", e, item)
				this.$emit("input", item.id, e.detail.value);
				uni.$emit("form-input", this.screenFormId, item.id, e.detail.value)
			},

			// 发送验证码
			sendCode(item) {
				let setTime = 0;
				this.sendCodeCallback(item);
				if (!this.isSend) {
					this.isSend = true;
					setTime = setInterval(() => {
						this.wait--;

						this.codeFont = this.wait + "重新发送";
						if (this.wait === 0) {
							clearInterval(setTime);
							this.codeFont = "获取验证码";
							this.isSend = false;
							this.wait = 60;
						}
					}, 1000);
				}
			},
			//校验
			vervify() {
				return new Promise((resolve, reject) => {
					this.formData.forEach((item) => {
						if ((item.rules && item.rules.length > 0) || item.required) {
							switch (item.type) {
								case "checkbox":
									if (this.form[item.id].length === 0 && this.form[item.required]) {
										uni.showToast({
											title: item.rules.errMess || "请选择" + item.label,
											duration: 2000,
											icon: "none",
										});
										throw Error(); //终止函数
									}
									break;
								case "file":
									if (this.form[item.id].length === 0 && this.form[item.required]) {
										uni.showToast({
											title: item.rules.errMess || "请选择" + item.label,
											duration: 2000,
											icon: "none",
										});

										throw Error(); //终止函数
									}
									break;
								case "mobile":
									if (!this.form[item.id] && this.form[item.required]) {
										uni.showToast({
											title: "手机号不能为空",
											duration: 2000,
											icon: "none",
										});

										throw Error(); //终止函数
									}
									if (!/^\s{0}$|^1\d{10}$/.test(this.form[item.id])) {
										uni.showToast({
											title: "手机格式错误",
											duration: 2000,
											icon: "none",
										});
										throw Error(); //终止函数
									}
									break;
								default:
									if (!this.form[item.id] && this.form[item.required]) {
										uni.showToast({
											title: item.rules.errMess || item.label + "不能为空",
											duration: 2000,
											icon: "none",
										});

										throw Error(); //终止函数
									}
									if (item.rules.length > 0) {
										for (var i = 0; i < item.rules.length; i++) {
											var rule = item.rules[i]
											if (rule.rule && !new RegExp(rule.rule).test(this.form[item
													.id])) {
												uni.showToast({
													title: item.label + "格式不正确",
													duration: 2000,
													icon: "none",
												});
												throw Error(); //终止函数
											}
										}
									}

									break;
							}
						}
					});
					resolve(this.form);
				});
			},
			//重置表单
			resetForm() {
				for (let key in this.form) {
					if (this.form.hasOwnProperty(key)) {
						switch (typeof this.form[key]) {
							case "object":
								this.form[key] = [];
								break;
							case "number":
								this.form[key] = "";
								break;
							case "string":
								this.form[key] = "";
								break;
						}
					}
				}
				this.$emit("input", this.form);
			},
			showSelectDefaultValue(item) {
				let value = "";
				if (item.list && this.elementVal) {
					let aval = item.list.find(v =>
						v.value == this.elementVal
					);
					console.log("aval", aval)
					item.defaultVal = aval.name;
					this.elementLabel = aval.name
					console.log("showSelectDefaultValue", item, this.elementVal, this.elementLabel)
					return aval.name;
				}
				return value;
			},
			// 获取级联选择器数据
			async getCascaderActions(item) {
				console.log("getCascaderActions", item)
				let arr = [];
				if (item.dataType === 'dynamic') {
					const key = item.action;
					let result = await DictApi.getDictDataByDictType(key)
					console.log("cascader result", result)
					if (result.code == 200 && result.data && result.data.length > 0) {
						// 如果返回的是树形结构数据，直接使用
						if (Array.isArray(result.data) && result.data[0] && result.data[0].children) {
							arr = result.data;
						} else {
							// 如果返回的是扁平数据，需要转换为树形结构
							arr = this.buildTreeData(result.data);
						}
					}
				} else { // static
					let options = item.options || [];
					arr = options;
				}

				// 设置字段映射
				if (item.cascaderMap) {
					this.cascaderMap = {
						text: item.cascaderMap.text || 'label',
						value: item.cascaderMap.value || 'value',
						children: item.cascaderMap.children || 'children'
					};
				}

				this.cascaderOptions = arr;
				console.log("cascaderOptions", this.cascaderOptions)
				return arr;
			},
			// 构建树形数据结构
			buildTreeData(flatData) {
				const tree = [];
				const map = {};

				// 创建映射
				flatData.forEach(item => {
					map[item.value] = {
						...item,
						children: []
					};
				});

				// 构建树形结构
				flatData.forEach(item => {
					if (item.parentId && map[item.parentId]) {
						map[item.parentId].children.push(map[item.value]);
					} else {
						tree.push(map[item.value]);
					}
				});

				return tree;
			},
			// 级联选择器值改变事件
			cascaderChange(e, item) {
				console.log("cascaderChange", e, item)
				this.elementVal = e;
				this.$emit("input", item.id, e);
				uni.$emit("form-input", this.screenFormId, item.id, e)
				this.$forceUpdate();
			},
			// 级联选择器节点点击事件
			cascaderNodeClick(e, item) {
				console.log("cascaderNodeClick", e, item)
				// 可以在这里处理节点点击的逻辑
			},
		},
	};
</script>

<style lang="scss" scoped>

</style>