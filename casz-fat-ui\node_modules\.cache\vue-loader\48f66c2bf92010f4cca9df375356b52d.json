{"remainingRequest": "D:\\shangchen\\casz-fat-j21\\casz-fat-ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\shangchen\\casz-fat-j21\\casz-fat-ui\\src\\views\\cgm\\warnAlreadyRecord\\index.vue?vue&type=script&lang=js", "dependencies": [{"path": "D:\\shangchen\\casz-fat-j21\\casz-fat-ui\\src\\views\\cgm\\warnAlreadyRecord\\index.vue", "mtime": 1752668935354}, {"path": "D:\\shangchen\\casz-fat-j21\\casz-fat-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1747273083419}, {"path": "D:\\shangchen\\casz-fat-j21\\casz-fat-ui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1747273094151}, {"path": "D:\\shangchen\\casz-fat-j21\\casz-fat-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1747273083419}, {"path": "D:\\shangchen\\casz-fat-j21\\casz-fat-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1747273098384}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:Ly8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KDQppbXBvcnQgew0KICBsaXN0V2FyblJlY29yZCwNCiAgZ2V0V2FyblJlY29yZCwNCiAgZGVsV2FyblJlY29yZCwNCiAgYWRkV2FyblJlY29yZCwNCiAgdXBkYXRlV2FyblJlY29yZCwNCiAgZGVhbFdhcm5SZWNvcmQNCn0gZnJvbSAiQC9hcGkvY2dtL3dhcm5SZWNvcmQiOw0KDQpleHBvcnQgZGVmYXVsdCB7DQogIG5hbWU6ICJXYXJuUmVjb3JkIiwNCiAgZGljdHM6IFsic3lzX3llc19ubyIsICJ3YXJuX3R5cGUiLCAiZGVhbF9zdGF0dXMiXSwNCiAgZGF0YSgpIHsNCiAgICByZXR1cm4gew0KICAgICAgY3VyRGVhbFdhcm46e30sDQogICAgICBkZWFsRm9ybVZpc2libGU6IGZhbHNlLA0KICAgICAgLy8g6YGu572p5bGCDQogICAgICBsb2FkaW5nOiB0cnVlLA0KICAgICAgLy8g6YCJ5Lit5pWw57uEDQogICAgICBpZHM6IFtdLA0KICAgICAgLy8g6Z2e5Y2V5Liq56aB55SoDQogICAgICBzaW5nbGU6IHRydWUsDQogICAgICAvLyDpnZ7lpJrkuKrnpoHnlKgNCiAgICAgIG11bHRpcGxlOiB0cnVlLA0KICAgICAgLy8g5pi+56S65pCc57Si5p2h5Lu2DQogICAgICBzaG93U2VhcmNoOiB0cnVlLA0KICAgICAgLy8g5oC75p2h5pWwDQogICAgICB0b3RhbDogMCwNCiAgICAgIC8vIOmihOitpuiusOW9leihqOagvOaVsOaNrg0KICAgICAgd2FyblJlY29yZExpc3Q6IFtdLA0KICAgICAgLy8g5by55Ye65bGC5qCH6aKYDQogICAgICB0aXRsZTogIiIsDQogICAgICAvLyDmmK/lkKbmmL7npLrlvLnlh7rlsYINCiAgICAgIG9wZW46IGZhbHNlLA0KICAgICAgLy8g5p+l6K+i5Y+C5pWwDQogICAgICBxdWVyeVBhcmFtczogew0KICAgICAgICBwYWdlTnVtOiAxLA0KICAgICAgICBwYWdlU2l6ZTogMTAsDQogICAgICAgIGNvbnRlbnQ6IG51bGwsDQogICAgICAgIGRlYWxTdGF0dXM6JzEnLA0KICAgICAgICB1bml0SWQ6IG51bGwsDQogICAgICAgIGNnbVJlY29yZElkOiBudWxsLA0KICAgICAgICB3YXJuVHlwZTogbnVsbCwNCiAgICAgICAgcGF0aWVudElkOiBudWxsLA0KICAgICAgICBwYXRpZW50TmFtZTogbnVsbCwNCiAgICAgICAgdXNlcklkOiBudWxsLA0KICAgICAgICBpZlJlYWQ6IG51bGwsDQogICAgICAgIHJlYWRUaW1lOiBudWxsLA0KICAgICAgICBub3RpY2VSZWNvcmRJZHM6IG51bGwsDQogICAgICAgIHRlbmFudElkOiBudWxsLA0KICAgICAgICByZXZpc2lvbjogbnVsbCwNCiAgICAgICAgdW5pdE5hbWU6IG51bGwsDQogICAgICB9LA0KICAgICAgZGVhbEZvcm06IHt9LA0KICAgICAgLy8g6KGo5Y2V5Y+C5pWwDQogICAgICBmb3JtOiB7fSwNCiAgICAgIC8vIOihqOWNleagoemqjA0KICAgICAgcnVsZXM6IHt9LA0KICAgIH07DQogIH0sDQogIGNyZWF0ZWQoKSB7DQogICAgdGhpcy5nZXRMaXN0KCk7DQogIH0sDQogIG1ldGhvZHM6IHsNCiAgICBjb25maXJtRGVhbCgpew0KICAgICAgdGhpcy5jdXJEZWFsV2Fybi5kZWFsU3RhdHVzID0gIjEiOw0KICAgICAgdGhpcy5jdXJEZWFsV2Fybi5yZWFkVGltZSA9IHRoaXMucGFyc2VUaW1lKG5ldyBEYXRlKCkpOw0KICAgICAgdGhpcy5jdXJEZWFsV2Fybi5kZWFsUmVtYXJrID0gdGhpcy5kZWFsRm9ybS5kZWFsUmVtYXJrOw0KICAgICAgdGhpcy5kZWFsRm9ybVZpc2libGUgPSBmYWxzZTsNCiAgICAgIHRoaXMuY3VyRGVhbFdhcm4uaW1Db250ZW50ID0gdGhpcy5kZWFsRm9ybS5pbUNvbnRlbnQ7DQogICAgICB0aGlzLmN1ckRlYWxXYXJuLm9wZXIgPSB0aGlzLmRlYWxGb3JtLm9wZXI7DQogICAgICBkZWFsV2FyblJlY29yZCh0aGlzLmN1ckRlYWxXYXJuKS50aGVuKHJlc3BvbnNlID0+IHsNCiAgICAgICAgdGhpcy4kbW9kYWwubXNnU3VjY2Vzcygi5aSE55CG5oiQ5YqfIik7DQogICAgICAgIHRoaXMuZ2V0TGlzdCgpOw0KICAgICAgICB0aGlzLmN1ckRlYWxXYXJuID0ge307DQogICAgICAgIHRoaXMuZGVhbEZvcm0gPSB7fTsNCiAgICAgIH0pDQogICAgfSwNCiAgICBoYW5kbGVXYXJuKHdhcm4pIHsNCiAgICAgIHRoaXMuY3VyRGVhbFdhcm4gPSB3YXJuOw0KICAgICAgdGhpcy5kZWFsRm9ybVZpc2libGUgPSB0cnVlOw0KICAgIH0sDQogICAgLy/lv73nlaXpooToraYNCiAgICBoYW5kbGVJZ25vcmUod2Fybikgew0KICAgICAgd2Fybi5pZlJlYWQgPSAiWSI7DQogICAgICB3YXJuLmRlYWxTdGF0dXMgPSAiMiI7DQogICAgICB3YXJuLnJlYWRUaW1lID0gdGhpcy5wYXJzZVRpbWUobmV3IERhdGUoKSk7DQogICAgICB3YXJuLmRlYWxSZW1hcmsgPSAi5b+955WlIjsNCiAgICAgIHVwZGF0ZVdhcm5SZWNvcmQod2FybikudGhlbihyZXNwb25zZSA9PiB7DQogICAgICAgIHRoaXMuJG1vZGFsLm1zZ1N1Y2Nlc3MoIuW/veeVpeaIkOWKnyIpOw0KICAgICAgICB0aGlzLmdldExpc3QoKTsNCiAgICAgIH0pDQogICAgfSwNCiAgICAvKiog5p+l6K+i6aKE6K2m6K6w5b2V5YiX6KGoICovDQogICAgZ2V0TGlzdCgpIHsNCiAgICAgIHRoaXMubG9hZGluZyA9IHRydWU7DQogICAgICBsaXN0V2FyblJlY29yZCh0aGlzLnF1ZXJ5UGFyYW1zKS50aGVuKChyZXNwb25zZSkgPT4gew0KICAgICAgICB0aGlzLndhcm5SZWNvcmRMaXN0ID0gcmVzcG9uc2Uucm93czsNCiAgICAgICAgdGhpcy50b3RhbCA9IHJlc3BvbnNlLnRvdGFsOw0KICAgICAgICB0aGlzLmxvYWRpbmcgPSBmYWxzZTsNCiAgICAgIH0pOw0KICAgIH0sDQogICAgLy8g5Y+W5raI5oyJ6ZKuDQogICAgY2FuY2VsKCkgew0KICAgICAgdGhpcy5vcGVuID0gZmFsc2U7DQogICAgICB0aGlzLnJlc2V0KCk7DQogICAgfSwNCiAgICAvLyDooajljZXph43nva4NCiAgICByZXNldCgpIHsNCiAgICAgIHRoaXMuZm9ybSA9IHsNCiAgICAgICAgaWQ6IG51bGwsDQogICAgICAgIGNvbnRlbnQ6IG51bGwsDQogICAgICAgIHVuaXRJZDogbnVsbCwNCiAgICAgICAgY2dtUmVjb3JkSWQ6IG51bGwsDQogICAgICAgIGRlYWxTdGF0dXM6JzEnLA0KICAgICAgICB3YXJuVHlwZTogbnVsbCwNCiAgICAgICAgcGF0aWVudElkOiBudWxsLA0KICAgICAgICBwYXRpZW50TmFtZTogbnVsbCwNCiAgICAgICAgY3JlYXRlVGltZTogbnVsbCwNCiAgICAgICAgY3JlYXRlQnk6IG51bGwsDQogICAgICAgIHVwZGF0ZVRpbWU6IG51bGwsDQogICAgICAgIHVwZGF0ZUJ5OiBudWxsLA0KICAgICAgICB1c2VySWQ6IG51bGwsDQogICAgICAgIGlmUmVhZDogbnVsbCwNCiAgICAgICAgcmVhZFRpbWU6IG51bGwsDQogICAgICAgIG5vdGljZVJlY29yZElkczogbnVsbCwNCiAgICAgICAgdGVuYW50SWQ6IG51bGwsDQogICAgICAgIHJldmlzaW9uOiBudWxsLA0KICAgICAgICB1bml0TmFtZTogbnVsbCwNCiAgICAgIH07DQogICAgICB0aGlzLnJlc2V0Rm9ybSgiZm9ybSIpOw0KICAgIH0sDQogICAgLyoqIOaQnOe0ouaMiemSruaTjeS9nCAqLw0KICAgIGhhbmRsZVF1ZXJ5KCkgew0KICAgICAgdGhpcy5xdWVyeVBhcmFtcy5wYWdlTnVtID0gMTsNCiAgICAgIHRoaXMuZ2V0TGlzdCgpOw0KICAgIH0sDQogICAgLyoqIOmHjee9ruaMiemSruaTjeS9nCAqLw0KICAgIHJlc2V0UXVlcnkoKSB7DQogICAgICB0aGlzLnJlc2V0Rm9ybSgicXVlcnlGb3JtIik7DQogICAgICB0aGlzLmhhbmRsZVF1ZXJ5KCk7DQogICAgfSwNCiAgICAvLyDlpJrpgInmoYbpgInkuK3mlbDmja4NCiAgICBoYW5kbGVTZWxlY3Rpb25DaGFuZ2Uoc2VsZWN0aW9uKSB7DQogICAgICB0aGlzLmlkcyA9IHNlbGVjdGlvbi5tYXAoKGl0ZW0pID0+IGl0ZW0uaWQpOw0KICAgICAgdGhpcy5zaW5nbGUgPSBzZWxlY3Rpb24ubGVuZ3RoICE9PSAxOw0KICAgICAgdGhpcy5tdWx0aXBsZSA9ICFzZWxlY3Rpb24ubGVuZ3RoOw0KICAgIH0sDQogICAgLyoqIOaWsOWinuaMiemSruaTjeS9nCAqLw0KICAgIGhhbmRsZUFkZCgpIHsNCiAgICAgIHRoaXMucmVzZXQoKTsNCiAgICAgIHRoaXMub3BlbiA9IHRydWU7DQogICAgICB0aGlzLnRpdGxlID0gIua3u+WKoOmihOitpuiusOW9lSI7DQogICAgfSwNCiAgICAvKiog5L+u5pS55oyJ6ZKu5pON5L2cICovDQogICAgaGFuZGxlVXBkYXRlKHJvdykgew0KICAgICAgdGhpcy5yZXNldCgpOw0KICAgICAgY29uc3QgaWQgPSByb3cuaWQgfHwgdGhpcy5pZHM7DQogICAgICBnZXRXYXJuUmVjb3JkKGlkKS50aGVuKChyZXNwb25zZSkgPT4gew0KICAgICAgICByZXNwb25zZS5kYXRhLmRlYWxTdGF0dXMgPSBwYXJzZUludChyZXNwb25zZS5kYXRhLmRlYWxTdGF0dXMpDQogICAgICAgIHRoaXMuZm9ybSA9IHJlc3BvbnNlLmRhdGE7DQogICAgICAgIHRoaXMub3BlbiA9IHRydWU7DQogICAgICAgIHRoaXMudGl0bGUgPSAi5L+u5pS56aKE6K2m6K6w5b2VIjsNCiAgICAgIH0pOw0KICAgIH0sDQogICAgLyoqIOaPkOS6pOaMiemSriAqLw0KICAgIHN1Ym1pdEZvcm0oKSB7DQogICAgICB0aGlzLiRyZWZzWyJmb3JtIl0udmFsaWRhdGUoKHZhbGlkKSA9PiB7DQogICAgICAgIGlmICh2YWxpZCkgew0KICAgICAgICAgIGlmICh0aGlzLmZvcm0uaWQgIT0gbnVsbCkgew0KICAgICAgICAgICAgdXBkYXRlV2FyblJlY29yZCh0aGlzLmZvcm0pLnRoZW4oKHJlc3BvbnNlKSA9PiB7DQogICAgICAgICAgICAgIHRoaXMuJG1vZGFsLm1zZ1N1Y2Nlc3MoIuS/ruaUueaIkOWKnyIpOw0KICAgICAgICAgICAgICB0aGlzLm9wZW4gPSBmYWxzZTsNCiAgICAgICAgICAgICAgdGhpcy5nZXRMaXN0KCk7DQogICAgICAgICAgICB9KTsNCiAgICAgICAgICB9IGVsc2Ugew0KICAgICAgICAgICAgYWRkV2FyblJlY29yZCh0aGlzLmZvcm0pLnRoZW4oKHJlc3BvbnNlKSA9PiB7DQogICAgICAgICAgICAgIHRoaXMuJG1vZGFsLm1zZ1N1Y2Nlc3MoIuaWsOWinuaIkOWKnyIpOw0KICAgICAgICAgICAgICB0aGlzLm9wZW4gPSBmYWxzZTsNCiAgICAgICAgICAgICAgdGhpcy5nZXRMaXN0KCk7DQogICAgICAgICAgICB9KTsNCiAgICAgICAgICB9DQogICAgICAgIH0NCiAgICAgIH0pOw0KICAgIH0sDQogICAgLyoqIOWIoOmZpOaMiemSruaTjeS9nCAqLw0KICAgIGhhbmRsZURlbGV0ZShyb3cpIHsNCiAgICAgIGNvbnN0IGlkcyA9IHJvdy5pZCB8fCB0aGlzLmlkczsNCiAgICAgIHRoaXMuJG1vZGFsDQogICAgICAgIC5jb25maXJtKCfmmK/lkKbnoa7orqTliKDpmaTpooToraborrDlvZXnvJblj7fkuLoiJyArIGlkcyArICci55qE5pWw5o2u6aG577yfJykNCiAgICAgICAgLnRoZW4oZnVuY3Rpb24gKCkgew0KICAgICAgICAgIHJldHVybiBkZWxXYXJuUmVjb3JkKGlkcyk7DQogICAgICAgIH0pDQogICAgICAgIC50aGVuKCgpID0+IHsNCiAgICAgICAgICB0aGlzLmdldExpc3QoKTsNCiAgICAgICAgICB0aGlzLiRtb2RhbC5tc2dTdWNjZXNzKCLliKDpmaTmiJDlip8iKTsNCiAgICAgICAgfSkNCiAgICAgICAgLmNhdGNoKCgpID0+IHsgfSk7DQogICAgfSwNCiAgICAvKiog5a+85Ye65oyJ6ZKu5pON5L2cICovDQogICAgaGFuZGxlRXhwb3J0KCkgew0KICAgICAgdGhpcy5kb3dubG9hZCgNCiAgICAgICAgImNnbS93YXJuUmVjb3JkL2V4cG9ydCIsDQogICAgICAgIHsNCiAgICAgICAgICAuLi50aGlzLnF1ZXJ5UGFyYW1zLA0KICAgICAgICB9LA0KICAgICAgICBgd2FyblJlY29yZF8ke25ldyBEYXRlKCkuZ2V0VGltZSgpfS54bHN4YA0KICAgICAgKTsNCiAgICB9LA0KICB9LA0KfTsNCg=="}, {"version": 3, "sources": ["index.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA8SA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "index.vue", "sourceRoot": "src/views/cgm/warnAlreadyRecord", "sourcesContent": ["<template>\r\n  <div class=\"app-container\">\r\n    <el-form :model=\"queryParams\" ref=\"queryForm\" size=\"small\" :inline=\"true\" v-show=\"showSearch\" label-width=\"68px\">\r\n      <!-- <el-form-item label=\"机构ID\" prop=\"unitId\">\r\n        <el-input\r\n          v-model=\"queryParams.unitId\"\r\n          placeholder=\"请输入机构ID\"\r\n          clearable\r\n          @keyup.enter.native=\"handleQuery\"\r\n        />\r\n      </el-form-item> -->\r\n      <!-- <el-form-item label=\"预警血糖记录\" prop=\"cgmRecordId\">\r\n        <el-input\r\n          v-model=\"queryParams.cgmRecordId\"\r\n          placeholder=\"请输入预警血糖记录\"\r\n          clearable\r\n          @keyup.enter.native=\"handleQuery\"\r\n        />\r\n      </el-form-item> -->\r\n      <el-form-item label=\"预警类型\" prop=\"warnType\">\r\n        <el-select v-model=\"queryParams.warnType\" placeholder=\"请选择预警类型\" clearable>\r\n          <el-option v-for=\"dict in dict.type.warn_type\" :key=\"dict.value\" :label=\"dict.label\" :value=\"dict.value\" />\r\n        </el-select>\r\n      </el-form-item>\r\n      <!-- <el-form-item label=\"患者id\" prop=\"patientId\">\r\n        <el-input\r\n          v-model=\"queryParams.patientId\"\r\n          placeholder=\"请输入患者id\"\r\n          clearable\r\n          @keyup.enter.native=\"handleQuery\"\r\n        />\r\n      </el-form-item> -->\r\n      <el-form-item label=\"患者姓名\" prop=\"patientName\">\r\n        <el-input v-model=\"queryParams.patientName\" placeholder=\"请输入患者姓名\" clearable @keyup.enter.native=\"handleQuery\" />\r\n      </el-form-item>\r\n      <!-- <el-form-item label=\"接收者\" prop=\"userId\">\r\n        <el-input\r\n          v-model=\"queryParams.userId\"\r\n          placeholder=\"请输入接收者\"\r\n          clearable\r\n          @keyup.enter.native=\"handleQuery\"\r\n        />\r\n      </el-form-item> -->\r\n      <!-- <el-form-item label=\"是否阅读\" prop=\"ifRead\">\r\n        <el-select v-model=\"queryParams.ifRead\" placeholder=\"请选择是否阅读\" clearable>\r\n          <el-option\r\n            v-for=\"dict in dict.type.sys_yes_no\"\r\n            :key=\"dict.value\"\r\n            :label=\"dict.label\"\r\n            :value=\"dict.value\"\r\n          />\r\n        </el-select>\r\n      </el-form-item> -->\r\n      <!-- <el-form-item label=\"阅读时间\" prop=\"readTime\">\r\n        <el-date-picker clearable\r\n          v-model=\"queryParams.readTime\"\r\n          type=\"date\"\r\n          value-format=\"yyyy-MM-dd\"\r\n          placeholder=\"请选择阅读时间\">\r\n        </el-date-picker>\r\n      </el-form-item> -->\r\n      <!-- <el-form-item label=\"通知关联记录\" prop=\"noticeRecordIds\">\r\n        <el-input\r\n          v-model=\"queryParams.noticeRecordIds\"\r\n          placeholder=\"请输入通知关联记录\"\r\n          clearable\r\n          @keyup.enter.native=\"handleQuery\"\r\n        />\r\n      </el-form-item> -->\r\n\r\n      <el-form-item>\r\n        <el-button type=\"primary\" icon=\"el-icon-search\" size=\"mini\" @click=\"handleQuery\">搜索</el-button>\r\n        <el-button icon=\"el-icon-refresh\" size=\"mini\" @click=\"resetQuery\">重置</el-button>\r\n      </el-form-item>\r\n    </el-form>\r\n\r\n    <el-row :gutter=\"10\" class=\"mb8\">\r\n      <!-- <el-col :span=\"1.5\">\r\n        <el-button\r\n          type=\"primary\"\r\n          plain\r\n          icon=\"el-icon-plus\"\r\n          size=\"mini\"\r\n          @click=\"handleAdd\"\r\n          v-hasPermi=\"['cgm:warnRecord:add']\"\r\n          >新增</el-button\r\n        >\r\n      </el-col>\r\n      <el-col :span=\"1.5\">\r\n        <el-button\r\n          type=\"success\"\r\n          plain\r\n          icon=\"el-icon-edit\"\r\n          size=\"mini\"\r\n          :disabled=\"single\"\r\n          @click=\"handleUpdate\"\r\n          v-hasPermi=\"['cgm:warnRecord:edit']\"\r\n          >修改</el-button\r\n        >\r\n      </el-col> -->\r\n      <el-col :span=\"1.5\">\r\n        <el-button type=\"primary\" plain icon=\"el-icon-delete\" size=\"mini\" :disabled=\"multiple\" @click=\"handleDelete\"\r\n          v-hasPermi=\"['cgm:warnRecord:remove']\">删除</el-button>\r\n      </el-col>\r\n      <!-- <el-col :span=\"1.5\">\r\n        <el-button\r\n          type=\"warning\"\r\n          plain\r\n          icon=\"el-icon-download\"\r\n          size=\"mini\"\r\n          @click=\"handleExport\"\r\n          v-hasPermi=\"['cgm:warnRecord:export']\"\r\n          >导出</el-button\r\n        >\r\n      </el-col> -->\r\n      <right-toolbar :showSearch.sync=\"showSearch\" @queryTable=\"getList\"></right-toolbar>\r\n    </el-row>\r\n\r\n    <el-table v-loading=\"loading\" :data=\"warnRecordList\" @selection-change=\"handleSelectionChange\">\r\n      <el-table-column type=\"selection\" width=\"55\" align=\"center\" />\r\n      <el-table-column label=\"患者姓名\" align=\"center\" prop=\"patientName\" width=\"120\" />\r\n      <!-- <el-table-column label=\"id\" align=\"center\" prop=\"id\" /> -->\r\n      <el-table-column label=\"预警内容\" align=\"center\" prop=\"content\">\r\n        <template slot-scope=\"scope\">\r\n          <el-tooltip class=\"item\" effect=\"dark\" :content=\"scope.row.content\"\r\n            placement=\"top\"><span>预警内容</span></el-tooltip>\r\n        </template>\r\n      </el-table-column>\r\n      <!-- <el-table-column label=\"机构ID\" align=\"center\" prop=\"unitId\" /> -->\r\n      <!-- <el-table-column label=\"预警血糖记录\" align=\"center\" prop=\"cgmRecordId\" /> -->\r\n      <el-table-column label=\"预警类型\" align=\"center\" prop=\"warnType\" width=\"150\">\r\n        <template slot-scope=\"scope\">\r\n          <dict-tag :options=\"dict.type.warn_type\" :value=\"scope.row.warnType\" />\r\n        </template>\r\n      </el-table-column>\r\n      <!-- <el-table-column label=\"患者id\" align=\"center\" prop=\"patientId\" /> -->\r\n\r\n      <el-table-column label=\"时间\" align=\"center\" prop=\"readTime\" width=\"180\">\r\n        <template slot-scope=\"scope\">\r\n          <span>{{ parseTime(scope.row.createTime) }}</span>\r\n        </template>\r\n      </el-table-column>\r\n      <el-table-column label=\"处理状态\" align=\"center\" prop=\"dealStatus\" width=\"100\">\r\n        <template slot-scope=\"scope\">\r\n          <dict-tag :options=\"dict.type.deal_status\" :value=\"scope.row.dealStatus\" />\r\n        </template>\r\n      </el-table-column>\r\n      <el-table-column label=\"处理备注\" align=\"center\" prop=\"dealRemark\" />\r\n\r\n\r\n      <!-- <el-table-column label=\"接收者\" align=\"center\" prop=\"userId\" /> -->\r\n      <!-- <el-table-column\r\n        label=\"是否阅读\"\r\n        align=\"center\"\r\n        prop=\"ifRead\"\r\n        width=\"100\"\r\n      >\r\n        <template slot-scope=\"scope\">\r\n          <dict-tag :options=\"dict.type.sys_yes_no\" :value=\"scope.row.ifRead\" />\r\n        </template>\r\n      </el-table-column> -->\r\n      <el-table-column label=\"阅读时间\" align=\"center\" prop=\"readTime\" width=\"180\">\r\n        <template slot-scope=\"scope\">\r\n          <span>{{ parseTime(scope.row.readTime) }}</span>\r\n        </template>\r\n      </el-table-column>\r\n      <!-- <el-table-column\r\n        label=\"通知关联记录\"\r\n        align=\"center\"\r\n        prop=\"noticeRecordIds\"\r\n      /> -->\r\n      <el-table-column label=\"操作\" width=\"180\" align=\"center\" class-name=\"small-padding fixed-width\">\r\n        <template slot-scope=\"scope\">\r\n          <el-button\r\n            size=\"mini\"\r\n            type=\"text\"\r\n            icon=\"el-icon-info\"\r\n            @click=\"handleUpdate(scope.row)\"\r\n            v-hasPermi=\"['cgm:warnRecord:edit']\"\r\n            >查看</el-button\r\n          >\r\n          <el-button v-if=\"scope.row.dealStatus == 0\" size=\"mini\" type=\"text\" icon=\"el-icon-edit\"\r\n            @click=\"handleWarn(scope.row)\" v-hasPermi=\"['cgm:warnRecord:edit']\">处理</el-button>\r\n          <el-button v-if=\"scope.row.dealStatus == 0\" size=\"mini\" type=\"text\" icon=\"el-icon-circle-close\"\r\n            @click=\"handleIgnore(scope.row)\" v-hasPermi=\"['cgm:warnRecord:edit']\">忽略</el-button>\r\n          <!-- <el-button\r\n            size=\"mini\"\r\n            type=\"text\"\r\n            icon=\"el-icon-delete\"\r\n            @click=\"handleDelete(scope.row)\"\r\n            v-hasPermi=\"['cgm:warnRecord:remove']\"\r\n            >删除</el-button\r\n          > -->\r\n        </template>\r\n      </el-table-column>\r\n    </el-table>\r\n\r\n    <pagination v-show=\"total > 0\" :total=\"total\" :page.sync=\"queryParams.pageNum\" :limit.sync=\"queryParams.pageSize\"\r\n      @pagination=\"getList\" />\r\n\r\n    <!-- 添加或修改预警记录对话框 -->\r\n    <el-dialog :title=\"title\" :visible.sync=\"open\" width=\"500px\" append-to-body>\r\n      <el-form ref=\"form\" disabled :model=\"form\" :rules=\"rules\" label-width=\"80px\">\r\n        <el-form-item label=\"患者姓名\" prop=\"patientName\">\r\n          <el-input v-model=\"form.patientName\" placeholder=\"请输入患者姓名\" />\r\n        </el-form-item>\r\n        <el-form-item label=\"预警内容\" prop=\"content\">\r\n          <el-input v-model=\"form.content\" type=\"textarea\" placeholder=\"请输入内容\" />\r\n        </el-form-item>\r\n        <el-form-item label=\"是否阅读\" prop=\"ifRead\">\r\n          <el-select v-model=\"form.ifRead\" style=\"width: 100%;\" placeholder=\"请选择是否阅读\">\r\n            <el-option v-for=\"dict in dict.type.sys_yes_no\" :key=\"dict.value\" :label=\"dict.label\"\r\n              :value=\"dict.value\"></el-option>\r\n          </el-select>\r\n        </el-form-item>\r\n        <el-form-item label=\"阅读时间\" prop=\"readTime\">\r\n          <el-date-picker clearable v-model=\"form.readTime\" type=\"date\" value-format=\"yyyy-MM-dd\" placeholder=\"请选择阅读时间\">\r\n          </el-date-picker>\r\n        </el-form-item>\r\n        <el-form-item label=\"处理状态\" prop=\"dealStatus\">\r\n          <!-- <el-select v-model=\"form.dealStatus\" style=\"width: 100%;\" placeholder=\"请选择\">\r\n            <el-option v-for=\"dict in dict.type.dealStatus\" :key=\"dict.value\" :label=\"dict.label\"\r\n              :value=\"parseInt(dict.value)\"></el-option>\r\n          </el-select> -->\r\n          <dict-tag :options=\"dict.type.deal_status\" :value=\"form.dealStatus\" />\r\n        </el-form-item>\r\n\r\n        <el-form-item label=\"处理备注\" prop=\"dealRemark\">\r\n          <el-input v-model=\"form.dealRemark\" type=\"textarea\" placeholder=\"请输入内容\" />\r\n        </el-form-item>\r\n        \r\n        <!-- <el-form-item label=\"机构ID\" prop=\"unitId\">\r\n          <el-input v-model=\"form.unitId\" placeholder=\"请输入机构ID\" />\r\n        </el-form-item> -->\r\n        <!-- <el-form-item label=\"预警血糖记录\" prop=\"cgmRecordId\">\r\n          <el-input v-model=\"form.cgmRecordId\" placeholder=\"请输入预警血糖记录\" />\r\n        </el-form-item> -->\r\n        <!-- <el-form-item label=\"预警类型\" prop=\"warnType\">\r\n          <el-select v-model=\"form.warnType\" placeholder=\"请选择预警类型\">\r\n            <el-option v-for=\"dict in dict.type.warn_type\" :key=\"dict.value\" :label=\"dict.label\"\r\n              :value=\"dict.value\"></el-option>\r\n          </el-select>\r\n        </el-form-item> -->\r\n        <!-- <el-form-item label=\"患者id\" prop=\"patientId\">\r\n          <el-input v-model=\"form.patientId\" placeholder=\"请输入患者id\" />\r\n        </el-form-item> -->\r\n        \r\n        <!-- <el-form-item label=\"接收者\" prop=\"userId\">\r\n          <el-input v-model=\"form.userId\" placeholder=\"请输入接收者\" />\r\n        </el-form-item> -->\r\n        \r\n        \r\n        <!-- <el-form-item label=\"通知关联记录\" prop=\"noticeRecordIds\">\r\n          <el-input v-model=\"form.noticeRecordIds\" placeholder=\"请输入通知关联记录\" />\r\n        </el-form-item> -->\r\n        <!-- <el-form-item label=\"${comment}\" prop=\"tenantId\">\r\n          <el-input v-model=\"form.tenantId\" placeholder=\"请输入${comment}\" />\r\n        </el-form-item>\r\n        <el-form-item label=\"${comment}\" prop=\"revision\">\r\n          <el-input v-model=\"form.revision\" placeholder=\"请输入${comment}\" />\r\n        </el-form-item> -->\r\n        <!-- <el-form-item label=\"机构名称\" prop=\"unitName\">\r\n          <el-input v-model=\"form.unitName\" placeholder=\"请输入机构名称\" />\r\n        </el-form-item> -->\r\n      </el-form>\r\n      <div slot=\"footer\" class=\"dialog-footer\">\r\n        <el-button type=\"primary\" @click=\"submitForm\">确 定</el-button>\r\n        <el-button @click=\"cancel\">取 消</el-button>\r\n      </div>\r\n    </el-dialog>\r\n\r\n    <!-- 处理预警对话框 -->\r\n    <el-dialog title=\"预警处理\" :visible.sync=\"dealFormVisible\" >\r\n      <el-form :model=\"dealForm\"  label-width=\"120px\">\r\n        <el-form-item label=\"患者姓名\">\r\n          <el-input v-model=\"curDealWarn.patientName\" readonly autocomplete=\"off\"></el-input>\r\n        </el-form-item>\r\n        <el-form-item label=\"预警内容\">\r\n          <el-input v-model=\"curDealWarn.content\" type=\"textarea\" readonly autocomplete=\"off\"></el-input>\r\n        </el-form-item>\r\n        <el-form-item label=\"处理备注\">\r\n          <el-input v-model=\"dealForm.dealRemark\" type=\"textarea\" autocomplete=\"off\"></el-input>\r\n        </el-form-item>\r\n        <el-form-item label=\"其他操作\" >\r\n          <el-select v-model=\"dealForm.oper\" clearable placeholder=\"请选择其他操作\">\r\n            <el-option label=\"发送即时通信提醒\" value=\"im\"></el-option>\r\n          </el-select>\r\n        </el-form-item>\r\n        <el-form-item label=\"即时通讯内容\" v-if=\"dealForm.oper == 'im'\">\r\n          <el-input v-model=\"dealForm.imContent\" type=\"textarea\" autocomplete=\"off\"></el-input>\r\n        </el-form-item>\r\n        \r\n      </el-form>\r\n      <div slot=\"footer\" class=\"dialog-footer\">\r\n        <el-button @click=\"dealFormVisible = false\">取 消</el-button>\r\n        <el-button type=\"primary\" @click=\"confirmDeal\">确 定</el-button>\r\n      </div>\r\n    </el-dialog>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport {\r\n  listWarnRecord,\r\n  getWarnRecord,\r\n  delWarnRecord,\r\n  addWarnRecord,\r\n  updateWarnRecord,\r\n  dealWarnRecord\r\n} from \"@/api/cgm/warnRecord\";\r\n\r\nexport default {\r\n  name: \"WarnRecord\",\r\n  dicts: [\"sys_yes_no\", \"warn_type\", \"deal_status\"],\r\n  data() {\r\n    return {\r\n      curDealWarn:{},\r\n      dealFormVisible: false,\r\n      // 遮罩层\r\n      loading: true,\r\n      // 选中数组\r\n      ids: [],\r\n      // 非单个禁用\r\n      single: true,\r\n      // 非多个禁用\r\n      multiple: true,\r\n      // 显示搜索条件\r\n      showSearch: true,\r\n      // 总条数\r\n      total: 0,\r\n      // 预警记录表格数据\r\n      warnRecordList: [],\r\n      // 弹出层标题\r\n      title: \"\",\r\n      // 是否显示弹出层\r\n      open: false,\r\n      // 查询参数\r\n      queryParams: {\r\n        pageNum: 1,\r\n        pageSize: 10,\r\n        content: null,\r\n        dealStatus:'1',\r\n        unitId: null,\r\n        cgmRecordId: null,\r\n        warnType: null,\r\n        patientId: null,\r\n        patientName: null,\r\n        userId: null,\r\n        ifRead: null,\r\n        readTime: null,\r\n        noticeRecordIds: null,\r\n        tenantId: null,\r\n        revision: null,\r\n        unitName: null,\r\n      },\r\n      dealForm: {},\r\n      // 表单参数\r\n      form: {},\r\n      // 表单校验\r\n      rules: {},\r\n    };\r\n  },\r\n  created() {\r\n    this.getList();\r\n  },\r\n  methods: {\r\n    confirmDeal(){\r\n      this.curDealWarn.dealStatus = \"1\";\r\n      this.curDealWarn.readTime = this.parseTime(new Date());\r\n      this.curDealWarn.dealRemark = this.dealForm.dealRemark;\r\n      this.dealFormVisible = false;\r\n      this.curDealWarn.imContent = this.dealForm.imContent;\r\n      this.curDealWarn.oper = this.dealForm.oper;\r\n      dealWarnRecord(this.curDealWarn).then(response => {\r\n        this.$modal.msgSuccess(\"处理成功\");\r\n        this.getList();\r\n        this.curDealWarn = {};\r\n        this.dealForm = {};\r\n      })\r\n    },\r\n    handleWarn(warn) {\r\n      this.curDealWarn = warn;\r\n      this.dealFormVisible = true;\r\n    },\r\n    //忽略预警\r\n    handleIgnore(warn) {\r\n      warn.ifRead = \"Y\";\r\n      warn.dealStatus = \"2\";\r\n      warn.readTime = this.parseTime(new Date());\r\n      warn.dealRemark = \"忽略\";\r\n      updateWarnRecord(warn).then(response => {\r\n        this.$modal.msgSuccess(\"忽略成功\");\r\n        this.getList();\r\n      })\r\n    },\r\n    /** 查询预警记录列表 */\r\n    getList() {\r\n      this.loading = true;\r\n      listWarnRecord(this.queryParams).then((response) => {\r\n        this.warnRecordList = response.rows;\r\n        this.total = response.total;\r\n        this.loading = false;\r\n      });\r\n    },\r\n    // 取消按钮\r\n    cancel() {\r\n      this.open = false;\r\n      this.reset();\r\n    },\r\n    // 表单重置\r\n    reset() {\r\n      this.form = {\r\n        id: null,\r\n        content: null,\r\n        unitId: null,\r\n        cgmRecordId: null,\r\n        dealStatus:'1',\r\n        warnType: null,\r\n        patientId: null,\r\n        patientName: null,\r\n        createTime: null,\r\n        createBy: null,\r\n        updateTime: null,\r\n        updateBy: null,\r\n        userId: null,\r\n        ifRead: null,\r\n        readTime: null,\r\n        noticeRecordIds: null,\r\n        tenantId: null,\r\n        revision: null,\r\n        unitName: null,\r\n      };\r\n      this.resetForm(\"form\");\r\n    },\r\n    /** 搜索按钮操作 */\r\n    handleQuery() {\r\n      this.queryParams.pageNum = 1;\r\n      this.getList();\r\n    },\r\n    /** 重置按钮操作 */\r\n    resetQuery() {\r\n      this.resetForm(\"queryForm\");\r\n      this.handleQuery();\r\n    },\r\n    // 多选框选中数据\r\n    handleSelectionChange(selection) {\r\n      this.ids = selection.map((item) => item.id);\r\n      this.single = selection.length !== 1;\r\n      this.multiple = !selection.length;\r\n    },\r\n    /** 新增按钮操作 */\r\n    handleAdd() {\r\n      this.reset();\r\n      this.open = true;\r\n      this.title = \"添加预警记录\";\r\n    },\r\n    /** 修改按钮操作 */\r\n    handleUpdate(row) {\r\n      this.reset();\r\n      const id = row.id || this.ids;\r\n      getWarnRecord(id).then((response) => {\r\n        response.data.dealStatus = parseInt(response.data.dealStatus)\r\n        this.form = response.data;\r\n        this.open = true;\r\n        this.title = \"修改预警记录\";\r\n      });\r\n    },\r\n    /** 提交按钮 */\r\n    submitForm() {\r\n      this.$refs[\"form\"].validate((valid) => {\r\n        if (valid) {\r\n          if (this.form.id != null) {\r\n            updateWarnRecord(this.form).then((response) => {\r\n              this.$modal.msgSuccess(\"修改成功\");\r\n              this.open = false;\r\n              this.getList();\r\n            });\r\n          } else {\r\n            addWarnRecord(this.form).then((response) => {\r\n              this.$modal.msgSuccess(\"新增成功\");\r\n              this.open = false;\r\n              this.getList();\r\n            });\r\n          }\r\n        }\r\n      });\r\n    },\r\n    /** 删除按钮操作 */\r\n    handleDelete(row) {\r\n      const ids = row.id || this.ids;\r\n      this.$modal\r\n        .confirm('是否确认删除预警记录编号为\"' + ids + '\"的数据项？')\r\n        .then(function () {\r\n          return delWarnRecord(ids);\r\n        })\r\n        .then(() => {\r\n          this.getList();\r\n          this.$modal.msgSuccess(\"删除成功\");\r\n        })\r\n        .catch(() => { });\r\n    },\r\n    /** 导出按钮操作 */\r\n    handleExport() {\r\n      this.download(\r\n        \"cgm/warnRecord/export\",\r\n        {\r\n          ...this.queryParams,\r\n        },\r\n        `warnRecord_${new Date().getTime()}.xlsx`\r\n      );\r\n    },\r\n  },\r\n};\r\n</script>\r\n"]}]}