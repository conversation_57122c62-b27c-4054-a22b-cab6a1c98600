package com.ruoyi.web.controller.system;

import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;
import jakarta.servlet.http.HttpServletResponse;

import com.ruoyi.common.annotation.Anonymous;
import com.ruoyi.web.domain.CaszRegion;
import com.ruoyi.web.dto.RegionTreeDto;
import com.ruoyi.web.service.ICaszRegionService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.core.domain.entity.SysDictData;
import com.ruoyi.common.core.page.TableDataInfo;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.common.utils.StringUtils;
import com.ruoyi.common.utils.poi.ExcelUtil;
import com.ruoyi.system.service.ISysDictDataService;
import com.ruoyi.system.service.ISysDictTypeService;

/**
 * 数据字典信息
 * 
 * <AUTHOR>
 */
@RestController
@RequestMapping("/system/dict/data")
public class SysDictDataController extends BaseController
{
    @Autowired
    private ISysDictDataService dictDataService;

    @Autowired
    private ISysDictTypeService dictTypeService;

    @Autowired
    private ICaszRegionService caszRegionService;

    @PreAuthorize("@ss.hasPermi('system:dict:list')")
    @GetMapping("/list")
    public TableDataInfo list(SysDictData dictData)
    {
        startPage();
        List<SysDictData> list = dictDataService.selectDictDataList(dictData);
        return getDataTable(list);
    }

    @Log(title = "字典数据", businessType = BusinessType.EXPORT)
    @PreAuthorize("@ss.hasPermi('system:dict:export')")
    @PostMapping("/export")
    public void export(HttpServletResponse response, SysDictData dictData)
    {
        List<SysDictData> list = dictDataService.selectDictDataList(dictData);
        ExcelUtil<SysDictData> util = new ExcelUtil<SysDictData>(SysDictData.class);
        util.exportExcel(response, list, "字典数据");
    }

    /**
     * 查询字典数据详细
     */
    @PreAuthorize("@ss.hasPermi('system:dict:query')")
    @GetMapping(value = "/{dictCode}")
    public AjaxResult getInfo(@PathVariable Long dictCode)
    {
        return success(dictDataService.selectDictDataById(dictCode));
    }

    /**
     * 根据字典类型查询字典数据信息
     */
    @GetMapping(value = "/type/{dictType}")
    @Anonymous
    public AjaxResult dictType(@PathVariable String dictType)
    {
        // 如果是城市字典，返回地区树形结构
        if ("cityDict".equals(dictType))
        {
            return success(buildRegionTree());
        }

        List<SysDictData> data = dictTypeService.selectDictDataByType(dictType);
        if (StringUtils.isNull(data))
        {
            data = new ArrayList<SysDictData>();
        }
        return success(data);
    }

    /**
     * 构建地区树形结构
     */
    private List<RegionTreeDto> buildRegionTree()
    {
        // 查询所有地区数据
        List<CaszRegion> allRegions = caszRegionService.selectCaszRegionList(new CaszRegion());

        // 构建树形结构
        List<RegionTreeDto> result = new ArrayList<>();

        // 找到根节点（parentId为null或0的节点）
        List<CaszRegion> rootRegions = allRegions.stream()
            .filter(region -> region.getParentId() == null || region.getParentId() == 0)
            .collect(Collectors.toList());

        for (CaszRegion rootRegion : rootRegions)
        {
            RegionTreeDto rootDto = convertToDto(rootRegion);
            buildChildren(rootDto, allRegions);
            result.add(rootDto);
        }

        return result;
    }

    /**
     * 递归构建子节点
     */
    private void buildChildren(RegionTreeDto parentDto, List<CaszRegion> allRegions)
    {
        List<CaszRegion> children = allRegions.stream()
            .filter(region -> region.getParentId() != null &&
                    region.getParentId().toString().equals(parentDto.getValue()))
            .collect(Collectors.toList());

        if (!children.isEmpty())
        {
            List<RegionTreeDto> childrenDtos = new ArrayList<>();
            for (CaszRegion child : children)
            {
                RegionTreeDto childDto = convertToDto(child);
                buildChildren(childDto, allRegions);
                childrenDtos.add(childDto);
            }
            parentDto.setChildren(childrenDtos);
        }
    }

    /**
     * 转换为DTO对象
     */
    private RegionTreeDto convertToDto(CaszRegion region)
    {
        RegionTreeDto dto = new RegionTreeDto();
        dto.setValue(region.getId().toString());
        dto.setLabel(region.getName());
        return dto;
    }

    /**
     * 新增字典类型
     */
    @PreAuthorize("@ss.hasPermi('system:dict:add')")
    @Log(title = "字典数据", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@Validated @RequestBody SysDictData dict)
    {
        dict.setCreateBy(getUsername());
        return toAjax(dictDataService.insertDictData(dict));
    }

    /**
     * 修改保存字典类型
     */
    @PreAuthorize("@ss.hasPermi('system:dict:edit')")
    @Log(title = "字典数据", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@Validated @RequestBody SysDictData dict)
    {
        dict.setUpdateBy(getUsername());
        return toAjax(dictDataService.updateDictData(dict));
    }

    /**
     * 删除字典类型
     */
    @PreAuthorize("@ss.hasPermi('system:dict:remove')")
    @Log(title = "字典类型", businessType = BusinessType.DELETE)
    @DeleteMapping("/{dictCodes}")
    public AjaxResult remove(@PathVariable Long[] dictCodes)
    {
        dictDataService.deleteDictDataByIds(dictCodes);
        return success();
    }
}
