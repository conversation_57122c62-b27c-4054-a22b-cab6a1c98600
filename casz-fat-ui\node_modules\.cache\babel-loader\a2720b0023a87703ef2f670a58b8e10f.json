{"remainingRequest": "D:\\shangchen\\casz-fat-j21\\casz-fat-ui\\node_modules\\babel-loader\\lib\\index.js!D:\\shangchen\\casz-fat-j21\\casz-fat-ui\\node_modules\\eslint-loader\\index.js??ref--13-0!D:\\shangchen\\casz-fat-j21\\casz-fat-ui\\src\\api\\forum\\likes.js", "dependencies": [{"path": "D:\\shangchen\\casz-fat-j21\\casz-fat-ui\\src\\api\\forum\\likes.js", "mtime": 1752668934322}, {"path": "D:\\shangchen\\casz-fat-j21\\casz-fat-ui\\babel.config.js", "mtime": 1752668935560}, {"path": "D:\\shangchen\\casz-fat-j21\\casz-fat-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1747273083419}, {"path": "D:\\shangchen\\casz-fat-j21\\casz-fat-ui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1747273094151}, {"path": "D:\\shangchen\\casz-fat-j21\\casz-fat-ui\\node_modules\\eslint-loader\\index.js", "mtime": 1747273087917}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:InVzZSBzdHJpY3QiOwoKdmFyIF9pbnRlcm9wUmVxdWlyZURlZmF1bHQgPSByZXF1aXJlKCJEOi9zaGFuZ2NoZW4vY2Fzei1mYXQtajIxL2Nhc3otZmF0LXVpL25vZGVfbW9kdWxlcy9AYmFiZWwvcnVudGltZS9oZWxwZXJzL2ludGVyb3BSZXF1aXJlRGVmYXVsdC5qcyIpLmRlZmF1bHQ7Ck9iamVjdC5kZWZpbmVQcm9wZXJ0eShleHBvcnRzLCAiX19lc01vZHVsZSIsIHsKICB2YWx1ZTogdHJ1ZQp9KTsKZXhwb3J0cy5hZGRMaWtlcyA9IGFkZExpa2VzOwpleHBvcnRzLmRlbExpa2VzID0gZGVsTGlrZXM7CmV4cG9ydHMuZ2V0TGlrZXMgPSBnZXRMaWtlczsKZXhwb3J0cy5saXN0TGlrZXMgPSBsaXN0TGlrZXM7CmV4cG9ydHMudXBkYXRlTGlrZXMgPSB1cGRhdGVMaWtlczsKdmFyIF9yZXF1ZXN0ID0gX2ludGVyb3BSZXF1aXJlRGVmYXVsdChyZXF1aXJlKCJAL3V0aWxzL3JlcXVlc3QiKSk7Ci8vIOafpeivouaci+WPi+WciOeCuei1nuWIl+ihqApmdW5jdGlvbiBsaXN0TGlrZXMocXVlcnkpIHsKICByZXR1cm4gKDAsIF9yZXF1ZXN0LmRlZmF1bHQpKHsKICAgIHVybDogJy9mb3J1bS9saWtlcy9saXN0JywKICAgIG1ldGhvZDogJ2dldCcsCiAgICBwYXJhbXM6IHF1ZXJ5CiAgfSk7Cn0KCi8vIOafpeivouaci+WPi+WciOeCuei1nuivpue7hgpmdW5jdGlvbiBnZXRMaWtlcyhpZCkgewogIHJldHVybiAoMCwgX3JlcXVlc3QuZGVmYXVsdCkoewogICAgdXJsOiAnL2ZvcnVtL2xpa2VzLycgKyBpZCwKICAgIG1ldGhvZDogJ2dldCcKICB9KTsKfQoKLy8g5paw5aKe5pyL5Y+L5ZyI54K56LWeCmZ1bmN0aW9uIGFkZExpa2VzKGRhdGEpIHsKICByZXR1cm4gKDAsIF9yZXF1ZXN0LmRlZmF1bHQpKHsKICAgIHVybDogJy9mb3J1bS9saWtlcycsCiAgICBtZXRob2Q6ICdwb3N0JywKICAgIGRhdGE6IGRhdGEKICB9KTsKfQoKLy8g5L+u5pS55pyL5Y+L5ZyI54K56LWeCmZ1bmN0aW9uIHVwZGF0ZUxpa2VzKGRhdGEpIHsKICByZXR1cm4gKDAsIF9yZXF1ZXN0LmRlZmF1bHQpKHsKICAgIHVybDogJy9mb3J1bS9saWtlcycsCiAgICBtZXRob2Q6ICdwdXQnLAogICAgZGF0YTogZGF0YQogIH0pOwp9CgovLyDliKDpmaTmnIvlj4vlnIjngrnotZ4KZnVuY3Rpb24gZGVsTGlrZXMoaWQpIHsKICByZXR1cm4gKDAsIF9yZXF1ZXN0LmRlZmF1bHQpKHsKICAgIHVybDogJy9mb3J1bS9saWtlcy8nICsgaWQsCiAgICBtZXRob2Q6ICdkZWxldGUnCiAgfSk7Cn0="}, {"version": 3, "names": ["_request", "_interopRequireDefault", "require", "listLikes", "query", "request", "url", "method", "params", "getLikes", "id", "addLikes", "data", "updateLikes", "delLikes"], "sources": ["D:/shangchen/casz-fat-j21/casz-fat-ui/src/api/forum/likes.js"], "sourcesContent": ["import request from '@/utils/request'\r\n\r\n// 查询朋友圈点赞列表\r\nexport function listLikes(query) {\r\n  return request({\r\n    url: '/forum/likes/list',\r\n    method: 'get',\r\n    params: query\r\n  })\r\n}\r\n\r\n// 查询朋友圈点赞详细\r\nexport function getLikes(id) {\r\n  return request({\r\n    url: '/forum/likes/' + id,\r\n    method: 'get'\r\n  })\r\n}\r\n\r\n// 新增朋友圈点赞\r\nexport function addLikes(data) {\r\n  return request({\r\n    url: '/forum/likes',\r\n    method: 'post',\r\n    data: data\r\n  })\r\n}\r\n\r\n// 修改朋友圈点赞\r\nexport function updateLikes(data) {\r\n  return request({\r\n    url: '/forum/likes',\r\n    method: 'put',\r\n    data: data\r\n  })\r\n}\r\n\r\n// 删除朋友圈点赞\r\nexport function delLikes(id) {\r\n  return request({\r\n    url: '/forum/likes/' + id,\r\n    method: 'delete'\r\n  })\r\n}\r\n"], "mappings": ";;;;;;;;;;;AAAA,IAAAA,QAAA,GAAAC,sBAAA,CAAAC,OAAA;AAEA;AACO,SAASC,SAASA,CAACC,KAAK,EAAE;EAC/B,OAAO,IAAAC,gBAAO,EAAC;IACbC,GAAG,EAAE,mBAAmB;IACxBC,MAAM,EAAE,KAAK;IACbC,MAAM,EAAEJ;EACV,CAAC,CAAC;AACJ;;AAEA;AACO,SAASK,QAAQA,CAACC,EAAE,EAAE;EAC3B,OAAO,IAAAL,gBAAO,EAAC;IACbC,GAAG,EAAE,eAAe,GAAGI,EAAE;IACzBH,MAAM,EAAE;EACV,CAAC,CAAC;AACJ;;AAEA;AACO,SAASI,QAAQA,CAACC,IAAI,EAAE;EAC7B,OAAO,IAAAP,gBAAO,EAAC;IACbC,GAAG,EAAE,cAAc;IACnBC,MAAM,EAAE,MAAM;IACdK,IAAI,EAAEA;EACR,CAAC,CAAC;AACJ;;AAEA;AACO,SAASC,WAAWA,CAACD,IAAI,EAAE;EAChC,OAAO,IAAAP,gBAAO,EAAC;IACbC,GAAG,EAAE,cAAc;IACnBC,MAAM,EAAE,KAAK;IACbK,IAAI,EAAEA;EACR,CAAC,CAAC;AACJ;;AAEA;AACO,SAASE,QAAQA,CAACJ,EAAE,EAAE;EAC3B,OAAO,IAAAL,gBAAO,EAAC;IACbC,GAAG,EAAE,eAAe,GAAGI,EAAE;IACzBH,MAAM,EAAE;EACV,CAAC,CAAC;AACJ", "ignoreList": []}]}