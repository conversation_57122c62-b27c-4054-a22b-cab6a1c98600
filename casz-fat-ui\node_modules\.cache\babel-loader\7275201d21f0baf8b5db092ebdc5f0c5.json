{"remainingRequest": "D:\\shangchen\\casz-fat-j21\\casz-fat-ui\\node_modules\\babel-loader\\lib\\index.js!D:\\shangchen\\casz-fat-j21\\casz-fat-ui\\node_modules\\eslint-loader\\index.js??ref--13-0!D:\\shangchen\\casz-fat-j21\\casz-fat-ui\\src\\api\\check\\levelCheck.js", "dependencies": [{"path": "D:\\shangchen\\casz-fat-j21\\casz-fat-ui\\src\\api\\check\\levelCheck.js", "mtime": 1752668934317}, {"path": "D:\\shangchen\\casz-fat-j21\\casz-fat-ui\\babel.config.js", "mtime": 1752668935560}, {"path": "D:\\shangchen\\casz-fat-j21\\casz-fat-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1747273083419}, {"path": "D:\\shangchen\\casz-fat-j21\\casz-fat-ui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1747273094151}, {"path": "D:\\shangchen\\casz-fat-j21\\casz-fat-ui\\node_modules\\eslint-loader\\index.js", "mtime": 1747273087917}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:InVzZSBzdHJpY3QiOwoKdmFyIF9pbnRlcm9wUmVxdWlyZURlZmF1bHQgPSByZXF1aXJlKCJEOi9zaGFuZ2NoZW4vY2Fzei1mYXQtajIxL2Nhc3otZmF0LXVpL25vZGVfbW9kdWxlcy9AYmFiZWwvcnVudGltZS9oZWxwZXJzL2ludGVyb3BSZXF1aXJlRGVmYXVsdC5qcyIpLmRlZmF1bHQ7Ck9iamVjdC5kZWZpbmVQcm9wZXJ0eShleHBvcnRzLCAiX19lc01vZHVsZSIsIHsKICB2YWx1ZTogdHJ1ZQp9KTsKZXhwb3J0cy5hZGRMZXZlbENoZWNrID0gYWRkTGV2ZWxDaGVjazsKZXhwb3J0cy5kZWxMZXZlbENoZWNrID0gZGVsTGV2ZWxDaGVjazsKZXhwb3J0cy5nZXRMZXZlbENoZWNrID0gZ2V0TGV2ZWxDaGVjazsKZXhwb3J0cy5saXN0TGV2ZWxDaGVjayA9IGxpc3RMZXZlbENoZWNrOwpleHBvcnRzLnVwZGF0ZUxldmVsQ2hlY2sgPSB1cGRhdGVMZXZlbENoZWNrOwp2YXIgX3JlcXVlc3QgPSBfaW50ZXJvcFJlcXVpcmVEZWZhdWx0KHJlcXVpcmUoIkAvdXRpbHMvcmVxdWVzdCIpKTsKLy8g5p+l6K+i562J57qn5a6h5qC45YiX6KGoCmZ1bmN0aW9uIGxpc3RMZXZlbENoZWNrKHF1ZXJ5KSB7CiAgcmV0dXJuICgwLCBfcmVxdWVzdC5kZWZhdWx0KSh7CiAgICB1cmw6ICcvY2hlY2svbGV2ZWxDaGVjay9saXN0JywKICAgIG1ldGhvZDogJ2dldCcsCiAgICBwYXJhbXM6IHF1ZXJ5CiAgfSk7Cn0KCi8vIOafpeivouetiee6p+WuoeaguOivpue7hgpmdW5jdGlvbiBnZXRMZXZlbENoZWNrKGlkKSB7CiAgcmV0dXJuICgwLCBfcmVxdWVzdC5kZWZhdWx0KSh7CiAgICB1cmw6ICcvY2hlY2svbGV2ZWxDaGVjay8nICsgaWQsCiAgICBtZXRob2Q6ICdnZXQnCiAgfSk7Cn0KCi8vIOaWsOWinuetiee6p+WuoeaguApmdW5jdGlvbiBhZGRMZXZlbENoZWNrKGRhdGEpIHsKICByZXR1cm4gKDAsIF9yZXF1ZXN0LmRlZmF1bHQpKHsKICAgIHVybDogJy9jaGVjay9sZXZlbENoZWNrJywKICAgIG1ldGhvZDogJ3Bvc3QnLAogICAgZGF0YTogZGF0YQogIH0pOwp9CgovLyDkv67mlLnnrYnnuqflrqHmoLgKZnVuY3Rpb24gdXBkYXRlTGV2ZWxDaGVjayhkYXRhKSB7CiAgcmV0dXJuICgwLCBfcmVxdWVzdC5kZWZhdWx0KSh7CiAgICB1cmw6ICcvY2hlY2svbGV2ZWxDaGVjaycsCiAgICBtZXRob2Q6ICdwdXQnLAogICAgZGF0YTogZGF0YQogIH0pOwp9CgovLyDliKDpmaTnrYnnuqflrqHmoLgKZnVuY3Rpb24gZGVsTGV2ZWxDaGVjayhpZCkgewogIHJldHVybiAoMCwgX3JlcXVlc3QuZGVmYXVsdCkoewogICAgdXJsOiAnL2NoZWNrL2xldmVsQ2hlY2svJyArIGlkLAogICAgbWV0aG9kOiAnZGVsZXRlJwogIH0pOwp9"}, {"version": 3, "names": ["_request", "_interopRequireDefault", "require", "listLevelCheck", "query", "request", "url", "method", "params", "getLevelCheck", "id", "addLevelCheck", "data", "updateLevelCheck", "delLevelCheck"], "sources": ["D:/shangchen/casz-fat-j21/casz-fat-ui/src/api/check/levelCheck.js"], "sourcesContent": ["import request from '@/utils/request'\r\n\r\n// 查询等级审核列表\r\nexport function listLevelCheck(query) {\r\n  return request({\r\n    url: '/check/levelCheck/list',\r\n    method: 'get',\r\n    params: query\r\n  })\r\n}\r\n\r\n// 查询等级审核详细\r\nexport function getLevelCheck(id) {\r\n  return request({\r\n    url: '/check/levelCheck/' + id,\r\n    method: 'get'\r\n  })\r\n}\r\n\r\n// 新增等级审核\r\nexport function addLevelCheck(data) {\r\n  return request({\r\n    url: '/check/levelCheck',\r\n    method: 'post',\r\n    data: data\r\n  })\r\n}\r\n\r\n// 修改等级审核\r\nexport function updateLevelCheck(data) {\r\n  return request({\r\n    url: '/check/levelCheck',\r\n    method: 'put',\r\n    data: data\r\n  })\r\n}\r\n\r\n// 删除等级审核\r\nexport function delLevelCheck(id) {\r\n  return request({\r\n    url: '/check/levelCheck/' + id,\r\n    method: 'delete'\r\n  })\r\n}\r\n"], "mappings": ";;;;;;;;;;;AAAA,IAAAA,QAAA,GAAAC,sBAAA,CAAAC,OAAA;AAEA;AACO,SAASC,cAAcA,CAACC,KAAK,EAAE;EACpC,OAAO,IAAAC,gBAAO,EAAC;IACbC,GAAG,EAAE,wBAAwB;IAC7BC,MAAM,EAAE,KAAK;IACbC,MAAM,EAAEJ;EACV,CAAC,CAAC;AACJ;;AAEA;AACO,SAASK,aAAaA,CAACC,EAAE,EAAE;EAChC,OAAO,IAAAL,gBAAO,EAAC;IACbC,GAAG,EAAE,oBAAoB,GAAGI,EAAE;IAC9BH,MAAM,EAAE;EACV,CAAC,CAAC;AACJ;;AAEA;AACO,SAASI,aAAaA,CAACC,IAAI,EAAE;EAClC,OAAO,IAAAP,gBAAO,EAAC;IACbC,GAAG,EAAE,mBAAmB;IACxBC,MAAM,EAAE,MAAM;IACdK,IAAI,EAAEA;EACR,CAAC,CAAC;AACJ;;AAEA;AACO,SAASC,gBAAgBA,CAACD,IAAI,EAAE;EACrC,OAAO,IAAAP,gBAAO,EAAC;IACbC,GAAG,EAAE,mBAAmB;IACxBC,MAAM,EAAE,KAAK;IACbK,IAAI,EAAEA;EACR,CAAC,CAAC;AACJ;;AAEA;AACO,SAASE,aAAaA,CAACJ,EAAE,EAAE;EAChC,OAAO,IAAAL,gBAAO,EAAC;IACbC,GAAG,EAAE,oBAAoB,GAAGI,EAAE;IAC9BH,MAAM,EAAE;EACV,CAAC,CAAC;AACJ", "ignoreList": []}]}