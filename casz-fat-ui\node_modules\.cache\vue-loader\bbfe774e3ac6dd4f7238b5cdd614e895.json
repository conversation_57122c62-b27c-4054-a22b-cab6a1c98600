{"remainingRequest": "D:\\shangchen\\casz-fat-j21\\casz-fat-ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\shangchen\\casz-fat-j21\\casz-fat-ui\\src\\components\\ImageJzUpload\\index.vue?vue&type=script&lang=js", "dependencies": [{"path": "D:\\shangchen\\casz-fat-j21\\casz-fat-ui\\src\\components\\ImageJzUpload\\index.vue", "mtime": 1752668935181}, {"path": "D:\\shangchen\\casz-fat-j21\\casz-fat-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1747273083419}, {"path": "D:\\shangchen\\casz-fat-j21\\casz-fat-ui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1747273094151}, {"path": "D:\\shangchen\\casz-fat-j21\\casz-fat-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1747273083419}, {"path": "D:\\shangchen\\casz-fat-j21\\casz-fat-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1747273098384}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "sources": ["index.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA6CA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;;;AAGA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "index.vue", "sourceRoot": "src/components/ImageJzUpload", "sourcesContent": ["<template>\r\n  <div class=\"component-upload-image\">\r\n    <el-upload\r\n      multiple\r\n      :action=\"uploadImgUrl\"\r\n      list-type=\"picture-card\"\r\n      :on-success=\"handleUploadSuccess\"\r\n      :before-upload=\"handleBeforeUpload\"\r\n      :limit=\"limit\"\r\n      :on-error=\"handleUploadError\"\r\n      :on-exceed=\"handleExceed\"\r\n      ref=\"imageUpload\"\r\n      :on-remove=\"handleDelete\"\r\n      :show-file-list=\"true\"\r\n      :headers=\"headers\"\r\n      :file-list=\"fileList\"\r\n      :on-preview=\"handlePictureCardPreview\"\r\n      :class=\"{hide: this.fileList.length >= this.limit}\"\r\n    >\r\n      <i class=\"el-icon-plus\"></i>\r\n    </el-upload>\r\n\r\n    <!-- 上传提示 -->\r\n    <div class=\"el-upload__tip\" slot=\"tip\" v-if=\"showTip\">\r\n      请上传\r\n      <template v-if=\"fileSize\"> 大小不超过 <b style=\"color: #f56c6c\">{{ fileSize }}MB</b> </template>\r\n      <template v-if=\"fileType\"> 格式为 <b style=\"color: #f56c6c\">{{ fileType.join(\"/\") }}</b> </template>\r\n      的文件\r\n    </div>\r\n\r\n    <el-dialog\r\n      :visible.sync=\"dialogVisible\"\r\n      title=\"预览\"\r\n      width=\"800\"\r\n      append-to-body\r\n    >\r\n      <img\r\n        :src=\"dialogImageUrl\"\r\n        style=\"display: block; max-width: 100%; margin: 0 auto\"\r\n      />\r\n    </el-dialog>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport { getToken } from \"@/utils/auth\";\r\n\r\nexport default {\r\n  props: {\r\n    value: [String, Object, Array],\r\n    // 图片数量限制\r\n    limit: {\r\n      type: Number,\r\n      default: 5,\r\n    },\r\n  \r\n    // 大小限制(MB)\r\n    fileSize: {\r\n       type: Number,\r\n      default: 5,\r\n    },\r\n    fileList: {\r\n      type: Array,\r\n      default: () => []\r\n    },\r\n    // 文件类型, 例如['png', 'jpg', 'jpeg']\r\n    fileType: {\r\n      type: Array,\r\n      default: () => [\"png\", \"jpg\", \"jpeg\"],\r\n    },\r\n    // 是否显示提示\r\n    isShowTip: {\r\n      type: Boolean,\r\n      default: true\r\n    },\r\n    uploadUrl:{\r\n      type:String,\r\n      default:\"/common/cosUpload\"\r\n    }\r\n  },\r\n\r\n  data() {\r\n    return {\r\n      number: 0,\r\n      uploadList: [],\r\n      dialogImageUrl: \"\",\r\n      dialogVisible: false,\r\n      hideUpload: false,\r\n      baseUrl: process.env.VUE_APP_BASE_API,\r\n      // uploadImgUrl: process.env.VUE_APP_BASE_API + \"/common/upload\", // 上传的图片服务器地址\r\n      headers: {\r\n        Authorization: \"Bearer \" + getToken(),\r\n      },\r\n    \r\n    };\r\n  },\r\n\r\n\r\n  computed: {\r\n    // 是否显示提示\r\n    showTip() {\r\n      return this.isShowTip && (this.fileType || this.fileSize);\r\n    },\r\n    uploadImgUrl(){\r\n      return process.env.VUE_APP_BASE_API  + this.uploadUrl\r\n    }\r\n  },\r\n  methods: {\r\n    clearFileList() {\r\n      this.fileList = [];\r\n      this.$emit('update:fileList', this.fileList); // 如果使用了 v-model 绑定，可以触发这个事件来更新父组件的绑定值\r\n    },\r\n    // 上传前loading加载\r\n    handleBeforeUpload(file) {\r\n      let isImg = false;\r\n      if (this.fileType.length) {\r\n        let fileExtension = \"\";\r\n        if (file.name.lastIndexOf(\".\") > -1) {\r\n          fileExtension = file.name.slice(file.name.lastIndexOf(\".\") + 1);\r\n        }\r\n        isImg = this.fileType.some(type => {\r\n          if (file.type.indexOf(type) > -1) return true;\r\n          if (fileExtension && fileExtension.indexOf(type) > -1) return true;\r\n          return false;\r\n        });\r\n      } else {\r\n        isImg = file.type.indexOf(\"image\") > -1;\r\n      }\r\n\r\n      if (!isImg) {\r\n        this.$modal.msgError(`文件格式不正确, 请上传${this.fileType.join(\"/\")}图片格式文件!`);\r\n        return false;\r\n      }\r\n      if (this.fileSize) {\r\n        const isLt = file.size / 1024 / 1024 < this.fileSize;\r\n        if (!isLt) {\r\n          this.$modal.msgError(`上传头像图片大小不能超过 ${this.fileSize} MB!`);\r\n          return false;\r\n        }\r\n      }\r\n      this.$modal.loading(\"正在上传图片，请稍候.....\");\r\n      this.number++;\r\n    },\r\n    // 文件个数超出\r\n    handleExceed() {\r\n      this.$modal.msgError(`上传文件数量不能超过 ${this.limit} 个!`);\r\n    },\r\n    // 上传成功回调\r\n    handleUploadSuccess(res, file) {\r\n      if (res.code === 200) {\r\n        if(this.uploadUrl=='/common/cosUpload'){\r\n          this.uploadList.push({ name: res.fileName, url: res.url });\r\n        }else{\r\n          this.uploadList.push({ name: res.fileName, url: res.url });\r\n        }\r\n        this.uploadedSuccessfully();\r\n      } else {\r\n        this.number--;\r\n        this.$modal.closeLoading();\r\n        this.$modal.msgError(res.msg);\r\n        this.$refs.imageUpload.handleRemove(file);\r\n        this.uploadedSuccessfully();\r\n      }\r\n    },\r\n    // 删除图片\r\n    handleDelete(file) {\r\n      const findex = this.fileList.map(f => f.name).indexOf(file.name);\r\n      if (findex > -1) {\r\n        this.fileList.splice(findex, 1);\r\n        this.$emit(\"input\", this.fileList);\r\n      }\r\n    },\r\n    // 上传失败\r\n    handleUploadError() {\r\n      this.$modal.msgError(\"上传图片失败，请重试\");\r\n      this.$modal.closeLoading();\r\n    },\r\n    // 上传结束处理\r\n    uploadedSuccessfully() {\r\n      if (this.number > 0 && this.uploadList.length === this.number) {\r\n        this.fileList = this.fileList.concat(this.uploadList);\r\n        this.uploadList = [];\r\n        this.number = 0;\r\n        console.log(\"@@\",this.uploadList,this.fileList,this.listToString(this.fileList))\r\n        this.$emit(\"input\", this.fileList);\r\n        this.$modal.closeLoading();\r\n\r\n        console.log(\"子组件\",this.fileList)\r\n      }\r\n    },\r\n\r\n    // 预览\r\n    handlePictureCardPreview(file) {\r\n      console.log(\"预览\",file)\r\n      this.dialogImageUrl = file.url;\r\n      this.dialogVisible = true;\r\n    },\r\n    // 对象转成指定字符串分隔\r\n    listToString(list, separator) {\r\n      let strs = \"\";\r\n      separator = separator || \",\";\r\n      for (let i in list) {\r\n        if (list[i].url) {\r\n          strs += list[i].url.replace(this.baseUrl, \"\") + separator;\r\n        }\r\n      }\r\n      return strs != '' ? strs.substr(0, strs.length - 1) : '';\r\n    }\r\n  }\r\n};\r\n</script>\r\n<style scoped lang=\"scss\">\r\n// .el-upload--picture-card 控制加号部分\r\n::v-deep.hide .el-upload--picture-card {\r\n    display: none;\r\n}\r\n// 去掉动画效果\r\n::v-deep .el-list-enter-active,\r\n::v-deep .el-list-leave-active {\r\n    transition: all 0s;\r\n}\r\n\r\n::v-deep .el-list-enter, .el-list-leave-active {\r\n  opacity: 0;\r\n  transform: translateY(0);\r\n}\r\n</style>\r\n\r\n"]}]}