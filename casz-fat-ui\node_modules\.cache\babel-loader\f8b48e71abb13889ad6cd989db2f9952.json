{"remainingRequest": "D:\\shangchen\\casz-fat-j21\\casz-fat-ui\\node_modules\\babel-loader\\lib\\index.js!D:\\shangchen\\casz-fat-j21\\casz-fat-ui\\node_modules\\eslint-loader\\index.js??ref--13-0!D:\\shangchen\\casz-fat-j21\\casz-fat-ui\\src\\api\\openIM\\user.js", "dependencies": [{"path": "D:\\shangchen\\casz-fat-j21\\casz-fat-ui\\src\\api\\openIM\\user.js", "mtime": 1752668934333}, {"path": "D:\\shangchen\\casz-fat-j21\\casz-fat-ui\\babel.config.js", "mtime": 1752668935560}, {"path": "D:\\shangchen\\casz-fat-j21\\casz-fat-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1747273083419}, {"path": "D:\\shangchen\\casz-fat-j21\\casz-fat-ui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1747273094151}, {"path": "D:\\shangchen\\casz-fat-j21\\casz-fat-ui\\node_modules\\eslint-loader\\index.js", "mtime": 1747273087917}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:InVzZSBzdHJpY3QiOwoKdmFyIF9pbnRlcm9wUmVxdWlyZURlZmF1bHQgPSByZXF1aXJlKCJEOi9zaGFuZ2NoZW4vY2Fzei1mYXQtajIxL2Nhc3otZmF0LXVpL25vZGVfbW9kdWxlcy9AYmFiZWwvcnVudGltZS9oZWxwZXJzL2ludGVyb3BSZXF1aXJlRGVmYXVsdC5qcyIpLmRlZmF1bHQ7Ck9iamVjdC5kZWZpbmVQcm9wZXJ0eShleHBvcnRzLCAiX19lc01vZHVsZSIsIHsKICB2YWx1ZTogdHJ1ZQp9KTsKZXhwb3J0cy5nZXRGcmllbmRMaXN0ID0gZ2V0RnJpZW5kTGlzdDsKdmFyIF9pbVJlcXVlc3QgPSBfaW50ZXJvcFJlcXVpcmVEZWZhdWx0KHJlcXVpcmUoIkAvdXRpbHMvaW1SZXF1ZXN0IikpOwovL+iOt+WPlueUqOaIt+WlveWPi+WIl+ihqApmdW5jdGlvbiBnZXRGcmllbmRMaXN0KHBhcmFtcykgewogIHJldHVybiAoMCwgX2ltUmVxdWVzdC5kZWZhdWx0KSh7CiAgICB1cmw6ICcvZnJpZW5kL2dldF9mcmllbmRfbGlzdCcsCiAgICBtZXRob2Q6ICdwb3N0JywKICAgIGRhdGE6IHBhcmFtcwogIH0pOwp9"}, {"version": 3, "names": ["_imRequest", "_interopRequireDefault", "require", "getFriendList", "params", "request", "url", "method", "data"], "sources": ["D:/shangchen/casz-fat-j21/casz-fat-ui/src/api/openIM/user.js"], "sourcesContent": ["import request  from '@/utils/imRequest'\r\n\r\n//获取用户好友列表\r\nexport function getFriendList(params){\r\n    return request({\r\n        url: '/friend/get_friend_list',\r\n        method: 'post',\r\n        data: params\r\n    })\r\n}\r\n"], "mappings": ";;;;;;;AAAA,IAAAA,UAAA,GAAAC,sBAAA,CAAAC,OAAA;AAEA;AACO,SAASC,aAAaA,CAACC,MAAM,EAAC;EACjC,OAAO,IAAAC,kBAAO,EAAC;IACXC,GAAG,EAAE,yBAAyB;IAC9BC,MAAM,EAAE,MAAM;IACdC,IAAI,EAAEJ;EACV,CAAC,CAAC;AACN", "ignoreList": []}]}