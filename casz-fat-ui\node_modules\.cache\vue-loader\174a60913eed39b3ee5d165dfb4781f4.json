{"remainingRequest": "D:\\shangchen\\casz-fat-j21\\casz-fat-ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\shangchen\\casz-fat-j21\\casz-fat-ui\\src\\components\\menu\\index.vue?vue&type=style&index=0&id=3a39ad32&scoped=true&lang=css", "dependencies": [{"path": "D:\\shangchen\\casz-fat-j21\\casz-fat-ui\\src\\components\\menu\\index.vue", "mtime": 1752668935190}, {"path": "D:\\shangchen\\casz-fat-j21\\casz-fat-ui\\node_modules\\css-loader\\dist\\cjs.js", "mtime": 1747273089646}, {"path": "D:\\shangchen\\casz-fat-j21\\casz-fat-ui\\node_modules\\vue-loader\\lib\\loaders\\stylePostLoader.js", "mtime": 1747273105021}, {"path": "D:\\shangchen\\casz-fat-j21\\casz-fat-ui\\node_modules\\postcss-loader\\src\\index.js", "mtime": 1747273094093}, {"path": "D:\\shangchen\\casz-fat-j21\\casz-fat-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1747273083419}, {"path": "D:\\shangchen\\casz-fat-j21\\casz-fat-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1747273098384}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:CgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKDQoucmliYm9uIHsNCiAgd2lkdGg6IDYuNSU7DQogIGJhY2tncm91bmQtY29sb3I6IHJnYig0NiwgNDYsIDQ2LCAwLjkpOw0KfQ0KDQouY29udGVudCB7DQogIHdpZHRoOiA5NCU7DQp9DQoNCi5yaWJib25JY29uU3R5bGUgew0KICBjb2xvcjogcmdiKDE0NywgMTQ3LCAxNDcsIDAuOCk7DQogIGZvbnQtc2l6ZTogMjNweDsNCn0NCg0KLm1lbnVJY29uIHsNCiAgbWFyZ2luLWJvdHRvbTogNDAlOw0KfQ0KDQoubWVudUljb246aG92ZXIgew0KICBjb2xvcjogcmdiYSgxOTIsIDE5MiwgMTkyLCAwLjkpOw0KfQ0KDQouaXMtY2hhY2sgew0KICBjb2xvcjogcmdiYSg3LCAxOTMsIDk2LCAwLjkpOw0KfQ0KDQo="}, {"version": 3, "sources": ["index.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAsEA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA", "file": "index.vue", "sourceRoot": "src/components/menu", "sourcesContent": ["<template>\r\n  <!-- 功能区 -->\r\n  <div class=\"ribbon h flex vcenter column\">\r\n    <!-- 头像 -->\r\n    <div class=\"portrait w flex lcenter vcenter column\" style=\"margin: 70% 0 30% 0;\">\r\n      <el-avatar shape=\"square\" size=\"medium\" :src=\"userInfo.faceURL\" icon=\"el-icon-user-solid\"></el-avatar>\r\n    </div>\r\n    <!-- 常用功能 -->\r\n    <div class=\"function w flex vcenter column ribbonIconStyle\" style=\"flex:1;\">\r\n      <i v-for=\"(item, index) in menuData.iconArr\" :key=\"index\" :class=\"item\" class=\"pointer menuIcon\"\r\n        @click=\"jumpPage(menuData.pathArr[index], item)\"></i>\r\n    </div>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport { getSDK } from 'open-im-sdk-wasm';\r\nexport default {\r\n  props: {\r\n    userInfo: {\r\n      type: Object,\r\n      default: () => {\r\n        return {}\r\n      }\r\n    }\r\n  },\r\n  data() {\r\n\r\n    return {\r\n      menuData: {\r\n        // iconArr: ['el-icon-chat-round', 'el-icon-user', \"el-icon-collection\", \"el-icon-folder\",\r\n        //   \"el-icon-help\", \"el-icon-link\", \"el-icon-mobile-phone\", \"el-icon-setting\"],\r\n        // pathArr: ['/chat', '/friend', '/', '/',\r\n        //   '/', '/', '/', '/login'],\r\n        iconArr: ['el-icon-chat-round', 'el-icon-user'],\r\n        pathArr: ['/chat', '/friend'],\r\n        IMSDK: null,\r\n\r\n      }\r\n    }\r\n  },\r\n  created() {\r\n    this.IMSDK = getSDK();\r\n\r\n  },\r\n  methods: {\r\n\r\n    jumpPage(path, className) {\r\n      this.$emit('jumpPage', path)\r\n      // Array.prototype.forEach.call(document.getElementsByClassName('menuIcon'),(el)=>{\r\n      //   el.classList.remove('is-chack')\r\n      // })\r\n      // document.getElementsByClassName(className)[0].classList.add('is-chack')\r\n\r\n\r\n      // if (path == '/login') {\r\n      //   localStorage.clear();\r\n      //   this.$store.commit('setChatUserInfo', {\r\n      //     isLogin: false,\r\n      //     username: '',\r\n      //     account: '',\r\n      //   })\r\n      // }\r\n      // this.$router.push(path)\r\n    },\r\n  }\r\n}\r\n</script>\r\n\r\n<style scoped>\r\n.ribbon {\r\n  width: 6.5%;\r\n  background-color: rgb(46, 46, 46, 0.9);\r\n}\r\n\r\n.content {\r\n  width: 94%;\r\n}\r\n\r\n.ribbonIconStyle {\r\n  color: rgb(147, 147, 147, 0.8);\r\n  font-size: 23px;\r\n}\r\n\r\n.menuIcon {\r\n  margin-bottom: 40%;\r\n}\r\n\r\n.menuIcon:hover {\r\n  color: rgba(192, 192, 192, 0.9);\r\n}\r\n\r\n.is-chack {\r\n  color: rgba(7, 193, 96, 0.9);\r\n}\r\n\r\n</style>"]}]}