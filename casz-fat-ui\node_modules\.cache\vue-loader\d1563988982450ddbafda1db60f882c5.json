{"remainingRequest": "D:\\shangchen\\casz-fat-j21\\casz-fat-ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\shangchen\\casz-fat-j21\\casz-fat-ui\\src\\views\\cgm\\control\\index.vue?vue&type=script&lang=js", "dependencies": [{"path": "D:\\shangchen\\casz-fat-j21\\casz-fat-ui\\src\\views\\cgm\\control\\index.vue", "mtime": 1752668935350}, {"path": "D:\\shangchen\\casz-fat-j21\\casz-fat-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1747273083419}, {"path": "D:\\shangchen\\casz-fat-j21\\casz-fat-ui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1747273094151}, {"path": "D:\\shangchen\\casz-fat-j21\\casz-fat-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1747273083419}, {"path": "D:\\shangchen\\casz-fat-j21\\casz-fat-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1747273098384}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:Ly8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KDQppbXBvcnQgew0KICBsaXN0RGV2aWNlLA0KICBnZXREZXZpY2UsDQogIGRlbERldmljZSwNCiAgYWRkRGV2aWNlLA0KICB1cGRhdGVEZXZpY2UsDQogIGJpbmRJZiwNCiAgbGlzdENvbnRyb2wsDQp9IGZyb20gIkAvYXBpL2NnbS9kZXZpY2UiOw0KaW1wb3J0IFBhdGllbnRJbnB1dFNlbGVjdCBmcm9tICJAL2NvbXBvbmVudHMvUGF0aWVudElucHV0U2VsZWN0IjsNCmV4cG9ydCBkZWZhdWx0IHsNCiAgbmFtZTogIkRldmljZSIsDQogIGRpY3RzOiBbImRldmljZV9vd25fdHlwZSIsICJzeXNfbm9ybWFsX2Rpc2FibGUiXSwNCiAgY29tcG9uZW50czogeyBQYXRpZW50SW5wdXRTZWxlY3QgfSwNCiAgZGF0YSgpIHsNCiAgICByZXR1cm4gew0KICAgICAgLy8g6YGu572p5bGCDQogICAgICBsb2FkaW5nOiB0cnVlLA0KICAgICAgLy8g6YCJ5Lit5pWw57uEDQogICAgICBpZHM6IFtdLA0KICAgICAgLy8g6Z2e5Y2V5Liq56aB55SoDQogICAgICBzaW5nbGU6IHRydWUsDQogICAgICAvLyDpnZ7lpJrkuKrnpoHnlKgNCiAgICAgIG11bHRpcGxlOiB0cnVlLA0KICAgICAgLy8g5pi+56S65pCc57Si5p2h5Lu2DQogICAgICBzaG93U2VhcmNoOiB0cnVlLA0KICAgICAgLy8g5oC75p2h5pWwDQogICAgICB0b3RhbDogMCwNCiAgICAgIC8vIOihgOezluiuvuWkh+WFs+iBlOihqOagvOaVsOaNrg0KICAgICAgZGV2aWNlTGlzdDogW10sDQogICAgICAvLyDlvLnlh7rlsYLmoIfpopgNCiAgICAgIHRpdGxlOiAiIiwNCiAgICAgIC8vIOaYr+WQpuaYvuekuuW8ueWHuuWxgg0KICAgICAgb3BlbjogZmFsc2UsDQogICAgICAvLyDmn6Xor6Llj4LmlbANCiAgICAgIHF1ZXJ5UGFyYW1zOiB7DQogICAgICAgIHBhZ2VOdW06IDEsDQogICAgICAgIHBhZ2VTaXplOiAxMCwNCiAgICAgICAgcGF0aWVudElkOiBudWxsLA0KICAgICAgICBwYXRpZW50TmFtZTogbnVsbCwNCiAgICAgICAgY2dtRGV2aWNlTm86IG51bGwsDQogICAgICAgIGJnbURldmljZU5vOiBudWxsLA0KICAgICAgICBjZ21Pd25UeXBlOiBudWxsLA0KICAgICAgICBiZ21Pd25UeXBlOiBudWxsLA0KICAgICAgICBjZ21FbmRUaW1lOiBudWxsLA0KICAgICAgICBiZ21FbmRUaW1lOiBudWxsLA0KICAgICAgICB0ZW5hbnRJZDogbnVsbCwNCiAgICAgICAgcmV2aXNpb246IG51bGwsDQogICAgICAgIGNnbU1vZGVsOiBudWxsLA0KICAgICAgICBiZ21Nb2RlbDogbnVsbCwNCiAgICAgICAgY2dtRmFjdG9yeTogbnVsbCwNCiAgICAgICAgYmdtRmFjdG9yeTogbnVsbCwNCiAgICAgICAgY2dtU3RhdHVzOiBudWxsLA0KICAgICAgICBiZ21TdGF0dXM6IG51bGwsDQogICAgICAgIHVuaXRJZDogbnVsbCwNCiAgICAgICAgdW5pdE5hbWU6IG51bGwsDQogICAgICB9LA0KICAgICAgLy8g6KGo5Y2V5Y+C5pWwDQogICAgICBmb3JtOiB7fSwNCiAgICAgIC8vIOihqOWNleagoemqjA0KICAgICAgcnVsZXM6IHt9LA0KICAgIH07DQogIH0sDQogIGNyZWF0ZWQoKSB7DQogICAgdGhpcy5nZXRMaXN0KCk7DQogIH0sDQogIG1ldGhvZHM6IHsNCiAgICBoYW5kbGVDZ21SZXBvcnQocm93KSB7DQogICAgICB0aGlzLiRyb3V0ZXIucHVzaCh7DQogICAgICAgIG5hbWU6ICJCc1JlcG9ydCIsDQogICAgICAgIHBhdGg6ICIvY2dtL2NvbnRyb2wvUmVwb3J0LyIgKyByb3cucGF0aWVudElkLA0KICAgICAgICBwYXJhbXM6IHsgaWQ6IHJvdy5wYXRpZW50SWQgfSwNCiAgICAgIH0pOw0KICAgIH0sDQogICAgcGF0aWVudENoYW5nZShwYXRpZW50KSB7DQogICAgICB0aGlzLmZvcm0ucGF0aWVudElkID0gcGF0aWVudC5pZDsNCiAgICAgIHRoaXMuZm9ybS5wYXRpZW50TmFtZSA9IHBhdGllbnQubmFtZTsNCiAgICAgIGNvbnNvbGUubG9nKHRoaXMuZm9ybSk7DQogICAgICB0aGlzLmNoZWNrQmluZCgpOw0KICAgIH0sDQogICAgY2hlY2tCaW5kKCkgew0KICAgICAgYmluZElmKHRoaXMuZm9ybS5wYXRpZW50SWQpLnRoZW4oKHJlc3BvbnNlKSA9PiB7DQogICAgICAgIGlmIChyZXNwb25zZS5kYXRhKSB7DQogICAgICAgICAgdGhpcy4kbW9kYWwubXNnRXJyb3IoIuivpeaCo+iAheW3sue7keWumuiuvuWkhyIpOw0KICAgICAgICB9DQogICAgICB9KTsNCiAgICB9LA0KICAgIC8qKiDmn6Xor6LooYDns5borr7lpIflhbPogZTliJfooaggKi8NCiAgICBnZXRMaXN0KCkgew0KICAgICAgdGhpcy5sb2FkaW5nID0gdHJ1ZTsNCiAgICAgIGxpc3RDb250cm9sKHRoaXMucXVlcnlQYXJhbXMpLnRoZW4oKHJlc3BvbnNlKSA9PiB7DQogICAgICAgIHRoaXMuZGV2aWNlTGlzdCA9IHJlc3BvbnNlLnJvd3M7DQogICAgICAgIHRoaXMudG90YWwgPSByZXNwb25zZS50b3RhbDsNCiAgICAgICAgdGhpcy5sb2FkaW5nID0gZmFsc2U7DQogICAgICB9KTsNCiAgICB9LA0KICAgIC8vIOWPlua2iOaMiemSrg0KICAgIGNhbmNlbCgpIHsNCiAgICAgIHRoaXMub3BlbiA9IGZhbHNlOw0KICAgICAgdGhpcy5yZXNldCgpOw0KICAgIH0sDQogICAgLy8g6KGo5Y2V6YeN572uDQogICAgcmVzZXQoKSB7DQogICAgICB0aGlzLmZvcm0gPSB7DQogICAgICAgIGlkOiBudWxsLA0KICAgICAgICBwYXRpZW50SWQ6IG51bGwsDQogICAgICAgIHBhdGllbnROYW1lOiBudWxsLA0KICAgICAgICBjcmVhdGVUaW1lOiBudWxsLA0KICAgICAgICB1cGRhdGVUaW1lOiBudWxsLA0KICAgICAgICBjcmVhdGVCeTogbnVsbCwNCiAgICAgICAgdXBkYXRlQnk6IG51bGwsDQogICAgICAgIGNnbURldmljZU5vOiBudWxsLA0KICAgICAgICBiZ21EZXZpY2VObzogbnVsbCwNCiAgICAgICAgY2dtT3duVHlwZTogbnVsbCwNCiAgICAgICAgYmdtT3duVHlwZTogbnVsbCwNCiAgICAgICAgY2dtRW5kVGltZTogbnVsbCwNCiAgICAgICAgYmdtRW5kVGltZTogbnVsbCwNCiAgICAgICAgdGVuYW50SWQ6IG51bGwsDQogICAgICAgIHJldmlzaW9uOiBudWxsLA0KICAgICAgICBjZ21Nb2RlbDogbnVsbCwNCiAgICAgICAgYmdtTW9kZWw6IG51bGwsDQogICAgICAgIGNnbUZhY3Rvcnk6IG51bGwsDQogICAgICAgIGJnbUZhY3Rvcnk6IG51bGwsDQogICAgICAgIGNnbVN0YXR1czogbnVsbCwNCiAgICAgICAgYmdtU3RhdHVzOiBudWxsLA0KICAgICAgICB1bml0SWQ6IG51bGwsDQogICAgICAgIHVuaXROYW1lOiBudWxsLA0KICAgICAgfTsNCiAgICAgIHRoaXMucmVzZXRGb3JtKCJmb3JtIik7DQogICAgICBpZiAodGhpcy4kcmVmcy5wYXRpZW50SW5wdXRTZWxlY3QpIHRoaXMuJHJlZnMucGF0aWVudElucHV0U2VsZWN0LmNsZWFyKCk7DQogICAgfSwNCiAgICAvKiog5pCc57Si5oyJ6ZKu5pON5L2cICovDQogICAgaGFuZGxlUXVlcnkoKSB7DQogICAgICB0aGlzLnF1ZXJ5UGFyYW1zLnBhZ2VOdW0gPSAxOw0KICAgICAgdGhpcy5nZXRMaXN0KCk7DQogICAgfSwNCiAgICAvKiog6YeN572u5oyJ6ZKu5pON5L2cICovDQogICAgcmVzZXRRdWVyeSgpIHsNCiAgICAgIHRoaXMucmVzZXRGb3JtKCJxdWVyeUZvcm0iKTsNCiAgICAgIHRoaXMuaGFuZGxlUXVlcnkoKTsNCiAgICB9LA0KICAgIC8vIOWkmumAieahhumAieS4reaVsOaNrg0KICAgIGhhbmRsZVNlbGVjdGlvbkNoYW5nZShzZWxlY3Rpb24pIHsNCiAgICAgIHRoaXMuaWRzID0gc2VsZWN0aW9uLm1hcCgoaXRlbSkgPT4gaXRlbS5pZCk7DQogICAgICB0aGlzLnNpbmdsZSA9IHNlbGVjdGlvbi5sZW5ndGggIT09IDE7DQogICAgICB0aGlzLm11bHRpcGxlID0gIXNlbGVjdGlvbi5sZW5ndGg7DQogICAgfSwNCiAgICAvKiog5paw5aKe5oyJ6ZKu5pON5L2cICovDQogICAgaGFuZGxlQWRkKCkgew0KICAgICAgdGhpcy5yZXNldCgpOw0KICAgICAgdGhpcy5vcGVuID0gdHJ1ZTsNCiAgICAgIHRoaXMudGl0bGUgPSAi5re75Yqg6KGA57OW6K6+5aSH5YWz6IGUIjsNCiAgICB9LA0KICAgIC8qKiDkv67mlLnmjInpkq7mk43kvZwgKi8NCiAgICBoYW5kbGVVcGRhdGUocm93KSB7DQogICAgICB0aGlzLnJlc2V0KCk7DQogICAgICBjb25zdCBpZCA9IHJvdy5pZCB8fCB0aGlzLmlkczsNCiAgICAgIGdldERldmljZShpZCkudGhlbigocmVzcG9uc2UpID0+IHsNCiAgICAgICAgdGhpcy5mb3JtID0gcmVzcG9uc2UuZGF0YTsNCiAgICAgICAgdGhpcy5vcGVuID0gdHJ1ZTsNCiAgICAgICAgdGhpcy50aXRsZSA9ICLkv67mlLnooYDns5borr7lpIflhbPogZQiOw0KICAgICAgfSk7DQogICAgfSwNCiAgICAvKiog5o+Q5Lqk5oyJ6ZKuICovDQogICAgc3VibWl0Rm9ybSgpIHsNCiAgICAgIHRoaXMuJHJlZnNbImZvcm0iXS52YWxpZGF0ZSgodmFsaWQpID0+IHsNCiAgICAgICAgaWYgKHZhbGlkKSB7DQogICAgICAgICAgaWYgKHRoaXMuZm9ybS5pZCAhPSBudWxsKSB7DQogICAgICAgICAgICB1cGRhdGVEZXZpY2UodGhpcy5mb3JtKS50aGVuKChyZXNwb25zZSkgPT4gew0KICAgICAgICAgICAgICB0aGlzLiRtb2RhbC5tc2dTdWNjZXNzKCLkv67mlLnmiJDlip8iKTsNCiAgICAgICAgICAgICAgdGhpcy5vcGVuID0gZmFsc2U7DQogICAgICAgICAgICAgIHRoaXMuZ2V0TGlzdCgpOw0KICAgICAgICAgICAgfSk7DQogICAgICAgICAgfSBlbHNlIHsNCiAgICAgICAgICAgIGFkZERldmljZSh0aGlzLmZvcm0pLnRoZW4oKHJlc3BvbnNlKSA9PiB7DQogICAgICAgICAgICAgIHRoaXMuJG1vZGFsLm1zZ1N1Y2Nlc3MoIuaWsOWinuaIkOWKnyIpOw0KICAgICAgICAgICAgICB0aGlzLm9wZW4gPSBmYWxzZTsNCiAgICAgICAgICAgICAgdGhpcy5nZXRMaXN0KCk7DQogICAgICAgICAgICB9KTsNCiAgICAgICAgICB9DQogICAgICAgIH0NCiAgICAgIH0pOw0KICAgIH0sDQogICAgLyoqIOWIoOmZpOaMiemSruaTjeS9nCAqLw0KICAgIGhhbmRsZURlbGV0ZShyb3cpIHsNCiAgICAgIGNvbnN0IGlkcyA9IHJvdy5pZCB8fCB0aGlzLmlkczsNCiAgICAgIHRoaXMuJG1vZGFsDQogICAgICAgIC5jb25maXJtKCfmmK/lkKbnoa7orqTliKDpmaTooYDns5borr7lpIflhbPogZTnvJblj7fkuLoiJyArIGlkcyArICci55qE5pWw5o2u6aG577yfJykNCiAgICAgICAgLnRoZW4oZnVuY3Rpb24gKCkgew0KICAgICAgICAgIHJldHVybiBkZWxEZXZpY2UoaWRzKTsNCiAgICAgICAgfSkNCiAgICAgICAgLnRoZW4oKCkgPT4gew0KICAgICAgICAgIHRoaXMuZ2V0TGlzdCgpOw0KICAgICAgICAgIHRoaXMuJG1vZGFsLm1zZ1N1Y2Nlc3MoIuWIoOmZpOaIkOWKnyIpOw0KICAgICAgICB9KQ0KICAgICAgICAuY2F0Y2goKCkgPT4ge30pOw0KICAgIH0sDQogICAgLyoqIOWvvOWHuuaMiemSruaTjeS9nCAqLw0KICAgIGhhbmRsZUV4cG9ydCgpIHsNCiAgICAgIHRoaXMuZG93bmxvYWQoDQogICAgICAgICJjZ20vZGV2aWNlL2V4cG9ydCIsDQogICAgICAgIHsNCiAgICAgICAgICAuLi50aGlzLnF1ZXJ5UGFyYW1zLA0KICAgICAgICB9LA0KICAgICAgICBgZGV2aWNlXyR7bmV3IERhdGUoKS5nZXRUaW1lKCl9Lnhsc3hgDQogICAgICApOw0KICAgIH0sDQogIH0sDQp9Ow0K"}, {"version": 3, "sources": ["index.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAokBA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "index.vue", "sourceRoot": "src/views/cgm/control", "sourcesContent": ["<template>\r\n  <div class=\"app-container\">\r\n    <el-form\r\n      :model=\"queryParams\"\r\n      ref=\"queryForm\"\r\n      size=\"small\"\r\n      :inline=\"true\"\r\n      v-show=\"showSearch\"\r\n      label-width=\"90px\"\r\n    >\r\n      <!-- <el-form-item label=\"患者ID\" prop=\"patientId\">\r\n        <el-input\r\n          v-model=\"queryParams.patientId\"\r\n          placeholder=\"请输入患者ID\"\r\n          clearable\r\n          @keyup.enter.native=\"handleQuery\"\r\n        />\r\n      </el-form-item> -->\r\n      <el-form-item label=\"患者姓名\" prop=\"patientName\">\r\n        <el-input\r\n          v-model=\"queryParams.patientName\"\r\n          placeholder=\"请输入患者姓名\"\r\n          clearable\r\n          @keyup.enter.native=\"handleQuery\"\r\n        />\r\n      </el-form-item>\r\n      <el-form-item label=\"cgm设备号\" prop=\"cgmDeviceNo\">\r\n        <el-input\r\n          v-model=\"queryParams.cgmDeviceNo\"\r\n          placeholder=\"请输入cgm设备号\"\r\n          clearable\r\n          @keyup.enter.native=\"handleQuery\"\r\n        />\r\n      </el-form-item>\r\n      <el-form-item label=\"bgm设备号\" prop=\"bgmDeviceNo\">\r\n        <el-input\r\n          v-model=\"queryParams.bgmDeviceNo\"\r\n          placeholder=\"请输入bgm设备号\"\r\n          clearable\r\n          @keyup.enter.native=\"handleQuery\"\r\n        />\r\n      </el-form-item>\r\n      <!-- <el-form-item label=\"cgm设备使用方式\" prop=\"cgmOwnType\">\r\n        <el-select\r\n          v-model=\"queryParams.cgmOwnType\"\r\n          placeholder=\"请选择cgm设备使用方式\"\r\n          clearable\r\n        >\r\n          <el-option\r\n            v-for=\"dict in dict.type.device_own_type\"\r\n            :key=\"dict.value\"\r\n            :label=\"dict.label\"\r\n            :value=\"dict.value\"\r\n          />\r\n        </el-select>\r\n      </el-form-item> -->\r\n      <!-- <el-form-item label=\"bgm设备使用方式\" prop=\"bgmOwnType\">\r\n        <el-select\r\n          v-model=\"queryParams.bgmOwnType\"\r\n          placeholder=\"请选择bgm设备使用方式\"\r\n          clearable\r\n        >\r\n          <el-option\r\n            v-for=\"dict in dict.type.device_own_type\"\r\n            :key=\"dict.value\"\r\n            :label=\"dict.label\"\r\n            :value=\"dict.value\"\r\n          />\r\n        </el-select>\r\n      </el-form-item> -->\r\n      <!-- <el-form-item label=\"cgm设备使用截止时间\" prop=\"cgmEndTime\">\r\n        <el-date-picker clearable\r\n          v-model=\"queryParams.cgmEndTime\"\r\n          type=\"date\"\r\n          value-format=\"yyyy-MM-dd\"\r\n          placeholder=\"请选择cgm设备使用截止时间\">\r\n        </el-date-picker>\r\n      </el-form-item> -->\r\n      <!-- <el-form-item label=\"bgm设备使用截止时间\" prop=\"bgmEndTime\">\r\n        <el-date-picker clearable\r\n          v-model=\"queryParams.bgmEndTime\"\r\n          type=\"date\"\r\n          value-format=\"yyyy-MM-dd\"\r\n          placeholder=\"请选择bgm设备使用截止时间\">\r\n        </el-date-picker>\r\n      </el-form-item> -->\r\n      <!-- <el-form-item label=\"${comment}\" prop=\"tenantId\">\r\n        <el-input\r\n          v-model=\"queryParams.tenantId\"\r\n          placeholder=\"请输入${comment}\"\r\n          clearable\r\n          @keyup.enter.native=\"handleQuery\"\r\n        />\r\n      </el-form-item> -->\r\n      <!-- <el-form-item label=\"${comment}\" prop=\"revision\">\r\n        <el-input\r\n          v-model=\"queryParams.revision\"\r\n          placeholder=\"请输入${comment}\"\r\n          clearable\r\n          @keyup.enter.native=\"handleQuery\"\r\n        />\r\n      </el-form-item> -->\r\n      <!-- <el-form-item label=\"cgm设备型号\" prop=\"cgmModel\">\r\n        <el-input\r\n          v-model=\"queryParams.cgmModel\"\r\n          placeholder=\"请输入cgm设备型号\"\r\n          clearable\r\n          @keyup.enter.native=\"handleQuery\"\r\n        />\r\n      </el-form-item>\r\n      <el-form-item label=\"bgm设备型号\" prop=\"bgmModel\">\r\n        <el-input\r\n          v-model=\"queryParams.bgmModel\"\r\n          placeholder=\"请输入bgm设备型号\"\r\n          clearable\r\n          @keyup.enter.native=\"handleQuery\"\r\n        />\r\n      </el-form-item>\r\n      <el-form-item label=\"CGM厂家\" prop=\"cgmFactory\">\r\n        <el-input\r\n          v-model=\"queryParams.cgmFactory\"\r\n          placeholder=\"请输入CGM厂家\"\r\n          clearable\r\n          @keyup.enter.native=\"handleQuery\"\r\n        />\r\n      </el-form-item>\r\n      <el-form-item label=\"bgm厂家\" prop=\"bgmFactory\">\r\n        <el-input\r\n          v-model=\"queryParams.bgmFactory\"\r\n          placeholder=\"请输入bgm厂家\"\r\n          clearable\r\n          @keyup.enter.native=\"handleQuery\"\r\n        />\r\n      </el-form-item> -->\r\n      <!-- <el-form-item label=\"cgm设备状态\" prop=\"cgmStatus\">\r\n        <el-select v-model=\"queryParams.cgmStatus\" placeholder=\"请选择cgm设备状态\" clearable>\r\n          <el-option\r\n            v-for=\"dict in dict.type.sys_normal_disable\"\r\n            :key=\"dict.value\"\r\n            :label=\"dict.label\"\r\n            :value=\"dict.value\"\r\n          />\r\n        </el-select>\r\n      </el-form-item>\r\n      <el-form-item label=\"bgm设备状态\" prop=\"bgmStatus\">\r\n        <el-select v-model=\"queryParams.bgmStatus\" placeholder=\"请选择bgm设备状态\" clearable>\r\n          <el-option\r\n            v-for=\"dict in dict.type.sys_normal_disable\"\r\n            :key=\"dict.value\"\r\n            :label=\"dict.label\"\r\n            :value=\"dict.value\"\r\n          />\r\n        </el-select>\r\n      </el-form-item> -->\r\n      <!-- <el-form-item label=\"机构id\" prop=\"unitId\">\r\n        <el-input\r\n          v-model=\"queryParams.unitId\"\r\n          placeholder=\"请输入机构id\"\r\n          clearable\r\n          @keyup.enter.native=\"handleQuery\"\r\n        />\r\n      </el-form-item>\r\n      <el-form-item label=\"机构名称\" prop=\"unitName\">\r\n        <el-input\r\n          v-model=\"queryParams.unitName\"\r\n          placeholder=\"请输入机构名称\"\r\n          clearable\r\n          @keyup.enter.native=\"handleQuery\"\r\n        />\r\n      </el-form-item> -->\r\n      <el-form-item>\r\n        <el-button\r\n          type=\"primary\"\r\n          icon=\"el-icon-search\"\r\n          size=\"mini\"\r\n          @click=\"handleQuery\"\r\n          >搜索</el-button\r\n        >\r\n        <el-button icon=\"el-icon-refresh\" size=\"mini\" @click=\"resetQuery\"\r\n          >重置</el-button\r\n        >\r\n      </el-form-item>\r\n    </el-form>\r\n\r\n    <el-row :gutter=\"10\" class=\"mb8\">\r\n      <!-- <el-col :span=\"1.5\">\r\n        <el-button\r\n          type=\"primary\"\r\n          plain\r\n          icon=\"el-icon-plus\"\r\n          size=\"mini\"\r\n          @click=\"handleAdd\"\r\n          v-hasPermi=\"['cgm:device:add']\"\r\n          >新增</el-button\r\n        >\r\n      </el-col> -->\r\n      <!-- <el-col :span=\"1.5\">\r\n        <el-button\r\n          type=\"success\"\r\n          plain\r\n          icon=\"el-icon-edit\"\r\n          size=\"mini\"\r\n          :disabled=\"single\"\r\n          @click=\"handleUpdate\"\r\n          v-hasPermi=\"['cgm:device:edit']\"\r\n          >修改</el-button\r\n        >\r\n      </el-col> -->\r\n      <!-- <el-col :span=\"1.5\">\r\n        <el-button\r\n          type=\"danger\"\r\n          plain\r\n          icon=\"el-icon-delete\"\r\n          size=\"mini\"\r\n          :disabled=\"multiple\"\r\n          @click=\"handleDelete\"\r\n          v-hasPermi=\"['cgm:device:remove']\"\r\n          >删除</el-button\r\n        >\r\n      </el-col> -->\r\n      <!-- <el-col :span=\"1.5\">\r\n        <el-button\r\n          type=\"warning\"\r\n          plain\r\n          icon=\"el-icon-download\"\r\n          size=\"mini\"\r\n          @click=\"handleExport\"\r\n          v-hasPermi=\"['cgm:device:export']\"\r\n          >导出</el-button\r\n        >\r\n      </el-col> -->\r\n      <right-toolbar\r\n        :showSearch.sync=\"showSearch\"\r\n        @queryTable=\"getList\"\r\n      ></right-toolbar>\r\n    </el-row>\r\n\r\n    <el-table\r\n      v-loading=\"loading\"\r\n      :data=\"deviceList\"\r\n      @selection-change=\"handleSelectionChange\"\r\n    >\r\n      <el-table-column type=\"selection\" width=\"55\" align=\"center\" />\r\n      <!-- <el-table-column label=\"id\" align=\"center\" prop=\"id\" /> -->\r\n      <!-- <el-table-column label=\"患者ID\" align=\"center\" prop=\"patientId\" /> -->\r\n      <el-table-column label=\"患者姓名\" align=\"center\" prop=\"patientName\" />\r\n      <el-table-column label=\"cgm设备号\" align=\"center\" prop=\"cgmDeviceNo\" />\r\n      <!-- <el-table-column label=\"cgm设备使用方式\" align=\"center\" prop=\"cgmOwnType\">\r\n        <template slot-scope=\"scope\">\r\n          <dict-tag\r\n            :options=\"dict.type.device_own_type\"\r\n            :value=\"scope.row.cgmOwnType\"\r\n          />\r\n        </template>\r\n      </el-table-column> -->\r\n      <el-table-column label=\"bgm设备号\" align=\"center\" prop=\"bgmDeviceNo\" />\r\n      <!-- <el-table-column label=\"bgm设备使用方式\" align=\"center\" prop=\"bgmOwnType\">\r\n        <template slot-scope=\"scope\">\r\n          <dict-tag\r\n            :options=\"dict.type.device_own_type\"\r\n            :value=\"scope.row.bgmOwnType\"\r\n          />\r\n        </template>\r\n      </el-table-column> -->\r\n      <!-- <el-table-column\r\n        label=\"cgm设备使用截止时间\"\r\n        align=\"center\"\r\n        prop=\"cgmEndTime\"\r\n        width=\"180\"\r\n      >\r\n        <template slot-scope=\"scope\">\r\n          <span>{{ parseTime(scope.row.cgmEndTime, \"{y}-{m}-{d}\") }}</span>\r\n        </template>\r\n      </el-table-column> -->\r\n      <!-- <el-table-column\r\n        label=\"bgm设备使用截止时间\"\r\n        align=\"center\"\r\n        prop=\"bgmEndTime\"\r\n        width=\"180\"\r\n      >\r\n        <template slot-scope=\"scope\">\r\n          <span>{{ parseTime(scope.row.bgmEndTime, \"{y}-{m}-{d}\") }}</span>\r\n        </template>\r\n      </el-table-column> -->\r\n\r\n      <el-table-column\r\n        label=\"活跃状态\"\r\n        align=\"center\"\r\n        prop=\"params.actStatus\"\r\n      />\r\n      <el-table-column\r\n        label=\"最近一次血糖\"\r\n        align=\"center\"\r\n        prop=\"params.lastValue\"\r\n      />\r\n      <el-table-column\r\n        label=\"最近一次异常\"\r\n        align=\"center\"\r\n        prop=\"params.lastWarn\"\r\n      />\r\n      <!--<el-table-column label=\"bgm厂家\" align=\"center\" prop=\"bgmFactory\" />\r\n      <el-table-column label=\"cgm设备状态\" align=\"center\" prop=\"cgmStatus\">\r\n        <template slot-scope=\"scope\">\r\n          <dict-tag\r\n            :options=\"dict.type.sys_normal_disable\"\r\n            :value=\"scope.row.cgmStatus\"\r\n          />\r\n        </template>\r\n      </el-table-column> -->\r\n      <!-- <el-table-column label=\"bgm设备状态\" align=\"center\" prop=\"bgmStatus\">\r\n        <template slot-scope=\"scope\">\r\n          <dict-tag\r\n            :options=\"dict.type.sys_normal_disable\"\r\n            :value=\"scope.row.bgmStatus\"\r\n          />\r\n        </template>\r\n      </el-table-column>\r\n      <el-table-column label=\"机构id\" align=\"center\" prop=\"unitId\" />\r\n      <el-table-column label=\"机构名称\" align=\"center\" prop=\"unitName\" /> -->\r\n      <el-table-column\r\n        label=\"操作\"\r\n        align=\"center\"\r\n        class-name=\"small-padding fixed-width\"\r\n      >\r\n        <template slot-scope=\"scope\">\r\n          <el-button\r\n            size=\"mini\"\r\n            type=\"text\"\r\n            icon=\"el-icon-info\"\r\n            @click=\"handleCgmReport(scope.row)\"\r\n            v-hasPermi=\"['cgm:device:edit']\"\r\n            >血糖报告</el-button\r\n          >\r\n          <!-- <el-button\r\n            size=\"mini\"\r\n            type=\"text\"\r\n            icon=\"el-icon-edit\"\r\n            @click=\"handleUpdate(scope.row)\"\r\n            v-hasPermi=\"['cgm:device:edit']\"\r\n            >修改</el-button\r\n          >\r\n          <el-button\r\n            size=\"mini\"\r\n            type=\"text\"\r\n            icon=\"el-icon-delete\"\r\n            @click=\"handleDelete(scope.row)\"\r\n            v-hasPermi=\"['cgm:device:remove']\"\r\n            >删除</el-button\r\n          > -->\r\n        </template>\r\n      </el-table-column>\r\n    </el-table>\r\n\r\n    <pagination\r\n      v-show=\"total > 0\"\r\n      :total=\"total\"\r\n      :page.sync=\"queryParams.pageNum\"\r\n      :limit.sync=\"queryParams.pageSize\"\r\n      @pagination=\"getList\"\r\n    />\r\n\r\n    <!-- 添加或修改血糖设备关联对话框 -->\r\n    <el-dialog\r\n      :title=\"title\"\r\n      :visible.sync=\"open\"\r\n      width=\"1000px\"\r\n      :close-on-click-modal=\"false\"\r\n      append-to-body\r\n    >\r\n      <el-form ref=\"form\" :model=\"form\" :rules=\"rules\" label-width=\"80px\">\r\n        <el-form-item label=\"患者\" prop=\"patientId\">\r\n          <!-- <el-input v-model=\"form.patientId\" placeholder=\"请选择绑定患者\" /> -->\r\n          <PatientInputSelect\r\n            @change=\"patientChange\"\r\n            ref=\"patientInputSelect\"\r\n          />\r\n        </el-form-item>\r\n        <!-- <el-form-item label=\"患者姓名\" prop=\"patientName\">\r\n          <el-input v-model=\"form.patientName\" placeholder=\"请输入患者姓名\" />\r\n        </el-form-item> -->\r\n        <el-divider>cgm设备绑定</el-divider>\r\n        <el-row>\r\n          <el-col :span=\"12\"\r\n            ><el-form-item\r\n              label=\"cgm设备号\"\r\n              prop=\"cgmDeviceNo\"\r\n              label-width=\"130px\"\r\n            >\r\n              <el-input\r\n                v-model=\"form.cgmDeviceNo\"\r\n                placeholder=\"请输入cgm设备号\"\r\n              /> </el-form-item\r\n          ></el-col>\r\n          <el-col :span=\"12\"\r\n            ><el-form-item\r\n              label=\"cgm设备使用方式\"\r\n              prop=\"cgmOwnType\"\r\n              label-width=\"130px\"\r\n            >\r\n              <el-select\r\n                style=\"width: 100%\"\r\n                v-model=\"form.cgmOwnType\"\r\n                placeholder=\"请选择cgm设备使用方式\"\r\n              >\r\n                <el-option\r\n                  v-for=\"dict in dict.type.device_own_type\"\r\n                  :key=\"dict.value\"\r\n                  :label=\"dict.label\"\r\n                  :value=\"dict.value\"\r\n                ></el-option>\r\n              </el-select> </el-form-item\r\n          ></el-col>\r\n        </el-row>\r\n        <el-row>\r\n          <el-col :span=\"12\">\r\n            <el-form-item\r\n              label=\"cgm设备型号\"\r\n              prop=\"cgmModel\"\r\n              label-width=\"130px\"\r\n            >\r\n              <el-input\r\n                v-model=\"form.cgmModel\"\r\n                placeholder=\"请输入cgm设备型号\"\r\n              /> </el-form-item\r\n          ></el-col>\r\n          <el-col :span=\"12\"\r\n            ><el-form-item\r\n              label=\"CGM厂家\"\r\n              prop=\"cgmFactory\"\r\n              label-width=\"130px\"\r\n            >\r\n              <el-input\r\n                v-model=\"form.cgmFactory\"\r\n                placeholder=\"请输入CGM厂家\"\r\n              /> </el-form-item\r\n          ></el-col>\r\n        </el-row>\r\n        <el-row>\r\n          <el-col :span=\"12\"\r\n            ><el-form-item\r\n              label=\"cgm设备状态\"\r\n              prop=\"cgmStatus\"\r\n              label-width=\"130px\"\r\n            >\r\n              <el-select\r\n                style=\"width: 100%\"\r\n                v-model=\"form.cgmStatus\"\r\n                placeholder=\"请选择cgm设备状态\"\r\n              >\r\n                <el-option\r\n                  v-for=\"dict in dict.type.sys_normal_disable\"\r\n                  :key=\"dict.value\"\r\n                  :label=\"dict.label\"\r\n                  :value=\"dict.value\"\r\n                ></el-option>\r\n              </el-select> </el-form-item\r\n          ></el-col>\r\n          <el-col v-show=\"form.cgmOwnType == 1\" :span=\"12\"\r\n            ><el-form-item\r\n              label=\"cgm设备使用截止时间\"\r\n              prop=\"cgmEndTime\"\r\n              label-width=\"160px\"\r\n            >\r\n              <el-date-picker\r\n                style=\"width: 100%\"\r\n                clearable\r\n                v-model=\"form.cgmEndTime\"\r\n                type=\"date\"\r\n                value-format=\"yyyy-MM-dd\"\r\n                placeholder=\"请选择cgm设备使用截止时间\"\r\n              >\r\n              </el-date-picker> </el-form-item\r\n          ></el-col>\r\n        </el-row>\r\n        <el-divider>bgm设备绑定</el-divider>\r\n        <el-row>\r\n          <el-col :span=\"12\"\r\n            ><el-form-item\r\n              label=\"bgm设备号\"\r\n              prop=\"bgmDeviceNo\"\r\n              label-width=\"130px\"\r\n            >\r\n              <el-input\r\n                v-model=\"form.bgmDeviceNo\"\r\n                placeholder=\"请输入bgm设备号\"\r\n              /> </el-form-item\r\n          ></el-col>\r\n          <el-col :span=\"12\"\r\n            ><el-form-item\r\n              label=\"bgm设备使用方式\"\r\n              prop=\"bgmOwnType\"\r\n              label-width=\"130px\"\r\n            >\r\n              <el-select\r\n                style=\"width: 100%\"\r\n                v-model=\"form.bgmOwnType\"\r\n                placeholder=\"请选择bgm设备使用方式\"\r\n              >\r\n                <el-option\r\n                  v-for=\"dict in dict.type.device_own_type\"\r\n                  :key=\"dict.value\"\r\n                  :label=\"dict.label\"\r\n                  :value=\"dict.value\"\r\n                ></el-option>\r\n              </el-select> </el-form-item\r\n          ></el-col>\r\n        </el-row>\r\n\r\n        <el-row>\r\n          <el-col :span=\"12\"\r\n            ><el-form-item\r\n              label=\"bgm设备型号\"\r\n              prop=\"bgmModel\"\r\n              label-width=\"130px\"\r\n            >\r\n              <el-input\r\n                v-model=\"form.bgmModel\"\r\n                placeholder=\"请输入bgm设备型号\"\r\n              /> </el-form-item\r\n          ></el-col>\r\n          <el-col :span=\"12\"\r\n            ><el-form-item\r\n              label=\"bgm厂家\"\r\n              prop=\"bgmFactory\"\r\n              label-width=\"130px\"\r\n            >\r\n              <el-input\r\n                v-model=\"form.bgmFactory\"\r\n                placeholder=\"请输入bgm厂家\"\r\n              /> </el-form-item\r\n          ></el-col>\r\n        </el-row>\r\n\r\n        <el-row>\r\n          <el-col :span=\"12\">\r\n            <el-form-item\r\n              label=\"bgm设备状态\"\r\n              prop=\"bgmStatus\"\r\n              label-width=\"130px\"\r\n            >\r\n              <el-select\r\n                style=\"width: 100%\"\r\n                v-model=\"form.bgmStatus\"\r\n                placeholder=\"请选择bgm设备状态\"\r\n              >\r\n                <el-option\r\n                  v-for=\"dict in dict.type.sys_normal_disable\"\r\n                  :key=\"dict.value\"\r\n                  :label=\"dict.label\"\r\n                  :value=\"dict.value\"\r\n                ></el-option>\r\n              </el-select> </el-form-item\r\n          ></el-col>\r\n          <el-col v-show=\"form.bgmOwnType == 1\" :span=\"12\"\r\n            ><el-form-item\r\n              label=\"bgm设备使用截止时间\"\r\n              prop=\"bgmEndTime\"\r\n              label-width=\"160px\"\r\n            >\r\n              <el-date-picker\r\n                style=\"width: 100%\"\r\n                clearable\r\n                v-model=\"form.bgmEndTime\"\r\n                type=\"date\"\r\n                value-format=\"yyyy-MM-dd\"\r\n                placeholder=\"请选择bgm设备使用截止时间\"\r\n              >\r\n              </el-date-picker> </el-form-item\r\n          ></el-col>\r\n        </el-row>\r\n      </el-form>\r\n      <div slot=\"footer\" class=\"dialog-footer\">\r\n        <el-button type=\"primary\" @click=\"submitForm\">确 定</el-button>\r\n        <el-button @click=\"cancel\">取 消</el-button>\r\n      </div>\r\n    </el-dialog>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport {\r\n  listDevice,\r\n  getDevice,\r\n  delDevice,\r\n  addDevice,\r\n  updateDevice,\r\n  bindIf,\r\n  listControl,\r\n} from \"@/api/cgm/device\";\r\nimport PatientInputSelect from \"@/components/PatientInputSelect\";\r\nexport default {\r\n  name: \"Device\",\r\n  dicts: [\"device_own_type\", \"sys_normal_disable\"],\r\n  components: { PatientInputSelect },\r\n  data() {\r\n    return {\r\n      // 遮罩层\r\n      loading: true,\r\n      // 选中数组\r\n      ids: [],\r\n      // 非单个禁用\r\n      single: true,\r\n      // 非多个禁用\r\n      multiple: true,\r\n      // 显示搜索条件\r\n      showSearch: true,\r\n      // 总条数\r\n      total: 0,\r\n      // 血糖设备关联表格数据\r\n      deviceList: [],\r\n      // 弹出层标题\r\n      title: \"\",\r\n      // 是否显示弹出层\r\n      open: false,\r\n      // 查询参数\r\n      queryParams: {\r\n        pageNum: 1,\r\n        pageSize: 10,\r\n        patientId: null,\r\n        patientName: null,\r\n        cgmDeviceNo: null,\r\n        bgmDeviceNo: null,\r\n        cgmOwnType: null,\r\n        bgmOwnType: null,\r\n        cgmEndTime: null,\r\n        bgmEndTime: null,\r\n        tenantId: null,\r\n        revision: null,\r\n        cgmModel: null,\r\n        bgmModel: null,\r\n        cgmFactory: null,\r\n        bgmFactory: null,\r\n        cgmStatus: null,\r\n        bgmStatus: null,\r\n        unitId: null,\r\n        unitName: null,\r\n      },\r\n      // 表单参数\r\n      form: {},\r\n      // 表单校验\r\n      rules: {},\r\n    };\r\n  },\r\n  created() {\r\n    this.getList();\r\n  },\r\n  methods: {\r\n    handleCgmReport(row) {\r\n      this.$router.push({\r\n        name: \"BsReport\",\r\n        path: \"/cgm/control/Report/\" + row.patientId,\r\n        params: { id: row.patientId },\r\n      });\r\n    },\r\n    patientChange(patient) {\r\n      this.form.patientId = patient.id;\r\n      this.form.patientName = patient.name;\r\n      console.log(this.form);\r\n      this.checkBind();\r\n    },\r\n    checkBind() {\r\n      bindIf(this.form.patientId).then((response) => {\r\n        if (response.data) {\r\n          this.$modal.msgError(\"该患者已绑定设备\");\r\n        }\r\n      });\r\n    },\r\n    /** 查询血糖设备关联列表 */\r\n    getList() {\r\n      this.loading = true;\r\n      listControl(this.queryParams).then((response) => {\r\n        this.deviceList = response.rows;\r\n        this.total = response.total;\r\n        this.loading = false;\r\n      });\r\n    },\r\n    // 取消按钮\r\n    cancel() {\r\n      this.open = false;\r\n      this.reset();\r\n    },\r\n    // 表单重置\r\n    reset() {\r\n      this.form = {\r\n        id: null,\r\n        patientId: null,\r\n        patientName: null,\r\n        createTime: null,\r\n        updateTime: null,\r\n        createBy: null,\r\n        updateBy: null,\r\n        cgmDeviceNo: null,\r\n        bgmDeviceNo: null,\r\n        cgmOwnType: null,\r\n        bgmOwnType: null,\r\n        cgmEndTime: null,\r\n        bgmEndTime: null,\r\n        tenantId: null,\r\n        revision: null,\r\n        cgmModel: null,\r\n        bgmModel: null,\r\n        cgmFactory: null,\r\n        bgmFactory: null,\r\n        cgmStatus: null,\r\n        bgmStatus: null,\r\n        unitId: null,\r\n        unitName: null,\r\n      };\r\n      this.resetForm(\"form\");\r\n      if (this.$refs.patientInputSelect) this.$refs.patientInputSelect.clear();\r\n    },\r\n    /** 搜索按钮操作 */\r\n    handleQuery() {\r\n      this.queryParams.pageNum = 1;\r\n      this.getList();\r\n    },\r\n    /** 重置按钮操作 */\r\n    resetQuery() {\r\n      this.resetForm(\"queryForm\");\r\n      this.handleQuery();\r\n    },\r\n    // 多选框选中数据\r\n    handleSelectionChange(selection) {\r\n      this.ids = selection.map((item) => item.id);\r\n      this.single = selection.length !== 1;\r\n      this.multiple = !selection.length;\r\n    },\r\n    /** 新增按钮操作 */\r\n    handleAdd() {\r\n      this.reset();\r\n      this.open = true;\r\n      this.title = \"添加血糖设备关联\";\r\n    },\r\n    /** 修改按钮操作 */\r\n    handleUpdate(row) {\r\n      this.reset();\r\n      const id = row.id || this.ids;\r\n      getDevice(id).then((response) => {\r\n        this.form = response.data;\r\n        this.open = true;\r\n        this.title = \"修改血糖设备关联\";\r\n      });\r\n    },\r\n    /** 提交按钮 */\r\n    submitForm() {\r\n      this.$refs[\"form\"].validate((valid) => {\r\n        if (valid) {\r\n          if (this.form.id != null) {\r\n            updateDevice(this.form).then((response) => {\r\n              this.$modal.msgSuccess(\"修改成功\");\r\n              this.open = false;\r\n              this.getList();\r\n            });\r\n          } else {\r\n            addDevice(this.form).then((response) => {\r\n              this.$modal.msgSuccess(\"新增成功\");\r\n              this.open = false;\r\n              this.getList();\r\n            });\r\n          }\r\n        }\r\n      });\r\n    },\r\n    /** 删除按钮操作 */\r\n    handleDelete(row) {\r\n      const ids = row.id || this.ids;\r\n      this.$modal\r\n        .confirm('是否确认删除血糖设备关联编号为\"' + ids + '\"的数据项？')\r\n        .then(function () {\r\n          return delDevice(ids);\r\n        })\r\n        .then(() => {\r\n          this.getList();\r\n          this.$modal.msgSuccess(\"删除成功\");\r\n        })\r\n        .catch(() => {});\r\n    },\r\n    /** 导出按钮操作 */\r\n    handleExport() {\r\n      this.download(\r\n        \"cgm/device/export\",\r\n        {\r\n          ...this.queryParams,\r\n        },\r\n        `device_${new Date().getTime()}.xlsx`\r\n      );\r\n    },\r\n  },\r\n};\r\n</script>\r\n"]}]}