{"remainingRequest": "D:\\shangchen\\casz-fat-j21\\casz-fat-ui\\node_modules\\babel-loader\\lib\\index.js!D:\\shangchen\\casz-fat-j21\\casz-fat-ui\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!D:\\shangchen\\casz-fat-j21\\casz-fat-ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\shangchen\\casz-fat-j21\\casz-fat-ui\\src\\views\\care\\follow\\selectUser.vue?vue&type=script&lang=js", "dependencies": [{"path": "D:\\shangchen\\casz-fat-j21\\casz-fat-ui\\src\\views\\care\\follow\\selectUser.vue", "mtime": 1752668935346}, {"path": "D:\\shangchen\\casz-fat-j21\\casz-fat-ui\\babel.config.js", "mtime": 1752668935560}, {"path": "D:\\shangchen\\casz-fat-j21\\casz-fat-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1747273083419}, {"path": "D:\\shangchen\\casz-fat-j21\\casz-fat-ui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1747273094151}, {"path": "D:\\shangchen\\casz-fat-j21\\casz-fat-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1747273083419}, {"path": "D:\\shangchen\\casz-fat-j21\\casz-fat-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1747273098384}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "names": ["_role", "require", "_user", "_follow", "dicts", "props", "businessId", "type", "Number", "String", "data", "visible", "userIds", "total", "userList", "queryParams", "pageNum", "pageSize", "roleId", "undefined", "userName", "phonenumber", "methods", "show", "console", "log", "getList", "clickRow", "row", "$refs", "table", "toggleRowSelection", "handleSelectionChange", "selection", "map", "item", "userId", "_this", "listUser", "then", "res", "rows", "handleQuery", "reset<PERSON><PERSON>y", "resetForm", "handleSelectUser", "_this2", "join", "$modal", "msgError", "assignedUsersFollow", "id", "msgSuccess", "msg", "code", "$emit"], "sources": ["src/views/care/follow/selectUser.vue"], "sourcesContent": ["<template>\r\n  <!-- 授权用户 -->\r\n  <el-dialog\r\n    title=\"选择用户\"\r\n    :visible.sync=\"visible\"\r\n    width=\"800px\"\r\n    top=\"5vh\"\r\n    append-to-body\r\n  >\r\n    <el-form :model=\"queryParams\" ref=\"queryForm\" size=\"small\" :inline=\"true\">\r\n      <el-form-item label=\"用户名称\" prop=\"userName\">\r\n        <el-input\r\n          v-model=\"queryParams.userName\"\r\n          placeholder=\"请输入用户名称\"\r\n          clearable\r\n          @keyup.enter.native=\"handleQuery\"\r\n        />\r\n      </el-form-item>\r\n      <el-form-item label=\"手机号码\" prop=\"phonenumber\">\r\n        <el-input\r\n          v-model=\"queryParams.phonenumber\"\r\n          placeholder=\"请输入手机号码\"\r\n          clearable\r\n          @keyup.enter.native=\"handleQuery\"\r\n        />\r\n      </el-form-item>\r\n      <el-form-item>\r\n        <el-button\r\n          type=\"primary\"\r\n          icon=\"el-icon-search\"\r\n          size=\"mini\"\r\n          @click=\"handleQuery\"\r\n          >搜索</el-button\r\n        >\r\n        <el-button icon=\"el-icon-refresh\" size=\"mini\" @click=\"resetQuery\"\r\n          >重置</el-button\r\n        >\r\n      </el-form-item>\r\n    </el-form>\r\n    <el-row>\r\n      <el-table\r\n        @row-click=\"clickRow\"\r\n        ref=\"table\"\r\n        :data=\"userList\"\r\n        @selection-change=\"handleSelectionChange\"\r\n        height=\"260px\"\r\n      >\r\n        <el-table-column type=\"selection\" width=\"55\"></el-table-column>\r\n        <el-table-column\r\n          label=\"用户名称\"\r\n          prop=\"userName\"\r\n          :show-overflow-tooltip=\"true\"\r\n        />\r\n        <el-table-column\r\n          label=\"用户昵称\"\r\n          prop=\"nickName\"\r\n          :show-overflow-tooltip=\"true\"\r\n        />\r\n        <el-table-column\r\n          label=\"邮箱\"\r\n          prop=\"email\"\r\n          :show-overflow-tooltip=\"true\"\r\n        />\r\n        <el-table-column\r\n          label=\"手机\"\r\n          prop=\"phonenumber\"\r\n          :show-overflow-tooltip=\"true\"\r\n        />\r\n        <el-table-column label=\"状态\" align=\"center\" prop=\"status\">\r\n          <template slot-scope=\"scope\">\r\n            <dict-tag\r\n              :options=\"dict.type.sys_normal_disable\"\r\n              :value=\"scope.row.status\"\r\n            />\r\n          </template>\r\n        </el-table-column>\r\n        <el-table-column\r\n          label=\"创建时间\"\r\n          align=\"center\"\r\n          prop=\"createTime\"\r\n          width=\"180\"\r\n        >\r\n          <template slot-scope=\"scope\">\r\n            <span>{{ parseTime(scope.row.createTime) }}</span>\r\n          </template>\r\n        </el-table-column>\r\n      </el-table>\r\n      <pagination\r\n        v-show=\"total > 0\"\r\n        :total=\"total\"\r\n        :page.sync=\"queryParams.pageNum\"\r\n        :limit.sync=\"queryParams.pageSize\"\r\n        @pagination=\"getList\"\r\n      />\r\n    </el-row>\r\n    <div slot=\"footer\" class=\"dialog-footer\">\r\n      <el-button type=\"primary\" @click=\"handleSelectUser\">确 定</el-button>\r\n      <el-button @click=\"visible = false\">取 消</el-button>\r\n    </div>\r\n  </el-dialog>\r\n</template>\r\n\r\n<script>\r\nimport { unallocatedUserList, authUserSelectAll } from \"@/api/system/role\";\r\nimport {\r\n  listUser,\r\n  getUser,\r\n  delUser,\r\n  addUser,\r\n  updateUser,\r\n  resetUserPwd,\r\n  changeUserStatus,\r\n  deptTreeSelect,\r\n} from \"@/api/system/user\";\r\nimport {\r\n  listFollow,\r\n  getFollow,\r\n  delFollow,\r\n  addFollow,\r\n  updateFollow,\r\n  assignedUsersFollow,\r\n} from \"@/api/care/follow\";\r\nexport default {\r\n  dicts: [\"sys_normal_disable\"],\r\n  props: {\r\n    // 角色编号\r\n    businessId: {\r\n      type: [Number, String],\r\n    },\r\n  },\r\n  data() {\r\n    return {\r\n      // 遮罩层\r\n      visible: false,\r\n      // 选中数组值\r\n      userIds: [],\r\n      // 总条数\r\n      total: 0,\r\n      // 未授权用户数据\r\n      userList: [],\r\n      // 查询参数\r\n      queryParams: {\r\n        pageNum: 1,\r\n        pageSize: 10,\r\n        roleId: undefined,\r\n        userName: undefined,\r\n        phonenumber: undefined,\r\n      },\r\n    };\r\n  },\r\n  methods: {\r\n    // 显示弹框\r\n    show() {\r\n      // this.queryParams.roleId = this.roleId;\r\n      console.log(\"businessId\", this.businessId);\r\n      this.getList();\r\n      this.visible = true;\r\n    },\r\n    clickRow(row) {\r\n      this.$refs.table.toggleRowSelection(row);\r\n    },\r\n    // 多选框选中数据\r\n    handleSelectionChange(selection) {\r\n      this.userIds = selection.map((item) => item.userId);\r\n    },\r\n    // 查询表数据\r\n    getList() {\r\n      listUser(this.queryParams).then((res) => {\r\n        this.userList = res.rows;\r\n        this.total = res.total;\r\n      });\r\n    },\r\n    /** 搜索按钮操作 */\r\n    handleQuery() {\r\n      this.queryParams.pageNum = 1;\r\n      this.getList();\r\n    },\r\n    /** 重置按钮操作 */\r\n    resetQuery() {\r\n      this.resetForm(\"queryForm\");\r\n      this.handleQuery();\r\n    },\r\n    /** 选择分配用户操作 */\r\n    handleSelectUser() {\r\n      const businessId = this.businessId;\r\n      const userIds = this.userIds.join(\",\");\r\n      if (userIds == \"\") {\r\n        this.$modal.msgError(\"请选择要分配的用户\");\r\n        return;\r\n      }\r\n      assignedUsersFollow({ id: businessId, userIds: userIds }).then((res) => {\r\n        this.$modal.msgSuccess(res.msg);\r\n        if (res.code === 200) {\r\n          this.visible = false;\r\n          this.$emit(\"ok\");\r\n        }\r\n      });\r\n    },\r\n  },\r\n};\r\n</script>\r\n"], "mappings": ";;;;;;;;;;;;AAuGA,IAAAA,KAAA,GAAAC,OAAA;AACA,IAAAC,KAAA,GAAAD,OAAA;AAUA,IAAAE,OAAA,GAAAF,OAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;iCAQA;EACAG,KAAA;EACAC,KAAA;IACA;IACAC,UAAA;MACAC,IAAA,GAAAC,MAAA,EAAAC,MAAA;IACA;EACA;EACAC,IAAA,WAAAA,KAAA;IACA;MACA;MACAC,OAAA;MACA;MACAC,OAAA;MACA;MACAC,KAAA;MACA;MACAC,QAAA;MACA;MACAC,WAAA;QACAC,OAAA;QACAC,QAAA;QACAC,MAAA,EAAAC,SAAA;QACAC,QAAA,EAAAD,SAAA;QACAE,WAAA,EAAAF;MACA;IACA;EACA;EACAG,OAAA;IACA;IACAC,IAAA,WAAAA,KAAA;MACA;MACAC,OAAA,CAAAC,GAAA,oBAAAnB,UAAA;MACA,KAAAoB,OAAA;MACA,KAAAf,OAAA;IACA;IACAgB,QAAA,WAAAA,SAAAC,GAAA;MACA,KAAAC,KAAA,CAAAC,KAAA,CAAAC,kBAAA,CAAAH,GAAA;IACA;IACA;IACAI,qBAAA,WAAAA,sBAAAC,SAAA;MACA,KAAArB,OAAA,GAAAqB,SAAA,CAAAC,GAAA,WAAAC,IAAA;QAAA,OAAAA,IAAA,CAAAC,MAAA;MAAA;IACA;IACA;IACAV,OAAA,WAAAA,QAAA;MAAA,IAAAW,KAAA;MACA,IAAAC,cAAA,OAAAvB,WAAA,EAAAwB,IAAA,WAAAC,GAAA;QACAH,KAAA,CAAAvB,QAAA,GAAA0B,GAAA,CAAAC,IAAA;QACAJ,KAAA,CAAAxB,KAAA,GAAA2B,GAAA,CAAA3B,KAAA;MACA;IACA;IACA,aACA6B,WAAA,WAAAA,YAAA;MACA,KAAA3B,WAAA,CAAAC,OAAA;MACA,KAAAU,OAAA;IACA;IACA,aACAiB,UAAA,WAAAA,WAAA;MACA,KAAAC,SAAA;MACA,KAAAF,WAAA;IACA;IACA,eACAG,gBAAA,WAAAA,iBAAA;MAAA,IAAAC,MAAA;MACA,IAAAxC,UAAA,QAAAA,UAAA;MACA,IAAAM,OAAA,QAAAA,OAAA,CAAAmC,IAAA;MACA,IAAAnC,OAAA;QACA,KAAAoC,MAAA,CAAAC,QAAA;QACA;MACA;MACA,IAAAC,2BAAA;QAAAC,EAAA,EAAA7C,UAAA;QAAAM,OAAA,EAAAA;MAAA,GAAA2B,IAAA,WAAAC,GAAA;QACAM,MAAA,CAAAE,MAAA,CAAAI,UAAA,CAAAZ,GAAA,CAAAa,GAAA;QACA,IAAAb,GAAA,CAAAc,IAAA;UACAR,MAAA,CAAAnC,OAAA;UACAmC,MAAA,CAAAS,KAAA;QACA;MACA;IACA;EACA;AACA", "ignoreList": []}]}