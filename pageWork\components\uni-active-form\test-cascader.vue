<template>
    <view class="container">
        <view class="title">级联选择器测试</view>
        
        <!-- 静态数据示例 -->
        <view class="section">
            <view class="section-title">静态数据示例</view>
            <form-item 
                :item="staticCascaderItem" 
                :value="form.staticRegion"
                @input="handleInput"
            />
            <view class="result">
                选中值：{{ JSON.stringify(form.staticRegion) }}
            </view>
        </view>
        
        <!-- 动态数据示例 -->
        <view class="section">
            <view class="section-title">动态数据示例</view>
            <form-item 
                :item="dynamicCascaderItem" 
                :value="form.dynamicRegion"
                @input="handleInput"
            />
            <view class="result">
                选中值：{{ JSON.stringify(form.dynamicRegion) }}
            </view>
        </view>
        
        <!-- 表单数据展示 -->
        <view class="section">
            <view class="section-title">完整表单数据</view>
            <view class="form-data">
                {{ JSON.stringify(form, null, 2) }}
            </view>
        </view>
    </view>
</template>

<script>
import FormItem from './form-item.vue';

export default {
    name: 'TestCascader',
    components: {
        FormItem
    },
    data() {
        return {
            form: {
                staticRegion: [],
                dynamicRegion: []
            },
            // 静态数据级联选择器配置
            staticCascaderItem: {
                id: "staticRegion",
                compType: "cascader",
                label: "地区选择（静态）",
                placeholder: "请选择地区",
                showLabel: true,
                required: true,
                disabled: false,
                dataType: 'static',
                popupTitle: '请选择地区',
                split: '/',
                options: [
                    {
                        label: '福建省',
                        value: 'fujian',
                        children: [
                            {
                                label: '泉州市',
                                value: 'quanzhou',
                                children: [
                                    { label: '鲤城区', value: 'licheng' },
                                    { label: '丰泽区', value: 'fengze' },
                                    { label: '洛江区', value: 'luojiang' },
                                    { label: '泉港区', value: 'quangang' },
                                    { label: '惠安县', value: 'huian' },
                                    { label: '安溪县', value: 'anxi' },
                                    { label: '永春县', value: 'yongchun' },
                                    { label: '德化县', value: 'dehua' },
                                    { label: '晋江市', value: 'jinjiang' },
                                    { label: '石狮市', value: 'shishi' },
                                    { label: '南安市', value: 'nanan' }
                                ]
                            },
                            {
                                label: '福州市',
                                value: 'fuzhou',
                                children: [
                                    { label: '鼓楼区', value: 'gulou' },
                                    { label: '台江区', value: 'taijiang' },
                                    { label: '仓山区', value: 'cangshan' },
                                    { label: '马尾区', value: 'mawei' },
                                    { label: '晋安区', value: 'jinan' },
                                    { label: '长乐区', value: 'changle' }
                                ]
                            },
                            {
                                label: '厦门市',
                                value: 'xiamen',
                                children: [
                                    { label: '思明区', value: 'siming' },
                                    { label: '海沧区', value: 'haicang' },
                                    { label: '湖里区', value: 'huli' },
                                    { label: '集美区', value: 'jimei' },
                                    { label: '同安区', value: 'tongan' },
                                    { label: '翔安区', value: 'xiangan' }
                                ]
                            }
                        ]
                    }
                ]
            },
            // 动态数据级联选择器配置
            dynamicCascaderItem: {
                id: "dynamicRegion",
                compType: "cascader",
                label: "地区选择（动态）",
                placeholder: "请选择地区",
                showLabel: true,
                required: true,
                disabled: false,
                dataType: 'dynamic',
                action: 'cityDict', // 对应后端的字典类型
                popupTitle: '请选择地区',
                split: '/',
                cascaderMap: {
                    text: 'label',
                    value: 'value',
                    children: 'children'
                }
            }
        };
    },
    methods: {
        handleInput(id, value) {
            console.log('级联选择器值变化:', id, value);
            this.form[id] = value;
        }
    }
};
</script>

<style lang="scss" scoped>
.container {
    padding: 20rpx;
}

.title {
    font-size: 32rpx;
    font-weight: bold;
    text-align: center;
    margin-bottom: 40rpx;
    color: #333;
}

.section {
    margin-bottom: 40rpx;
    padding: 20rpx;
    background: #f8f8f8;
    border-radius: 10rpx;
}

.section-title {
    font-size: 28rpx;
    font-weight: bold;
    margin-bottom: 20rpx;
    color: #666;
}

.result {
    margin-top: 20rpx;
    padding: 10rpx;
    background: #fff;
    border-radius: 5rpx;
    font-size: 24rpx;
    color: #333;
    word-break: break-all;
}

.form-data {
    background: #fff;
    padding: 20rpx;
    border-radius: 5rpx;
    font-size: 22rpx;
    color: #333;
    white-space: pre-wrap;
    word-break: break-all;
}
</style>
