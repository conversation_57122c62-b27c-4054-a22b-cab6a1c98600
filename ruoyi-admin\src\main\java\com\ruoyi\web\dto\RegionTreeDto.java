package com.ruoyi.web.dto;

import com.fasterxml.jackson.annotation.JsonInclude;
import java.util.List;

/**
 * 地区树形结构DTO
 * 
 * <AUTHOR>
 */
public class RegionTreeDto
{
    /** 节点值 */
    private String value;

    /** 节点标签 */
    private String label;

    /** 子节点 */
    @JsonInclude(JsonInclude.Include.NON_EMPTY)
    private List<RegionTreeDto> children;

    public RegionTreeDto()
    {
    }

    public RegionTreeDto(String value, String label)
    {
        this.value = value;
        this.label = label;
    }

    public String getValue()
    {
        return value;
    }

    public void setValue(String value)
    {
        this.value = value;
    }

    public String getLabel()
    {
        return label;
    }

    public void setLabel(String label)
    {
        this.label = label;
    }

    public List<RegionTreeDto> getChildren()
    {
        return children;
    }

    public void setChildren(List<RegionTreeDto> children)
    {
        this.children = children;
    }
}
