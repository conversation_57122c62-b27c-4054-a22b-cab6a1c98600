{"remainingRequest": "D:\\shangchen\\casz-fat-j21\\casz-fat-ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\shangchen\\casz-fat-j21\\casz-fat-ui\\src\\views\\cgm\\sugar\\index.vue?vue&type=script&lang=js", "dependencies": [{"path": "D:\\shangchen\\casz-fat-j21\\casz-fat-ui\\src\\views\\cgm\\sugar\\index.vue", "mtime": 1752668935353}, {"path": "D:\\shangchen\\casz-fat-j21\\casz-fat-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1747273083419}, {"path": "D:\\shangchen\\casz-fat-j21\\casz-fat-ui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1747273094151}, {"path": "D:\\shangchen\\casz-fat-j21\\casz-fat-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1747273083419}, {"path": "D:\\shangchen\\casz-fat-j21\\casz-fat-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1747273098384}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:Ly8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KDQppbXBvcnQgew0KICBsaXN0U3VnYXIsDQogIGdldFN1Z2FyLA0KICBkZWxTdWdhciwNCiAgYWRkU3VnYXIsDQogIHVwZGF0ZVN1Z2FyLA0KfSBmcm9tICJAL2FwaS9jZ20vc3VnYXIiOw0KaW1wb3J0IFBhdGllbnRJbnB1dFNlbGVjdCBmcm9tICJAL2NvbXBvbmVudHMvUGF0aWVudElucHV0U2VsZWN0IjsNCg0KZXhwb3J0IGRlZmF1bHQgew0KICBuYW1lOiAiU3VnYXIiLA0KICBkaWN0czogWw0KICAgICJzdWdhcl92YWx1ZV90eXBlIiwNCiAgICAic3lzX3llc19ubyIsDQogICAgInN1Z2FyX2RhdGFfdHlwZSIsDQogICAgImVxdWlwX3VzZV90eXBlIiwNCiAgXSwNCiAgY29tcG9uZW50czogeyBQYXRpZW50SW5wdXRTZWxlY3QgfSwNCiAgZGF0YSgpIHsNCiAgICByZXR1cm4gew0KICAgICAgLy8g6YGu572p5bGCDQogICAgICBsb2FkaW5nOiB0cnVlLA0KICAgICAgLy8g6YCJ5Lit5pWw57uEDQogICAgICBpZHM6IFtdLA0KICAgICAgLy8g6Z2e5Y2V5Liq56aB55SoDQogICAgICBzaW5nbGU6IHRydWUsDQogICAgICAvLyDpnZ7lpJrkuKrnpoHnlKgNCiAgICAgIG11bHRpcGxlOiB0cnVlLA0KICAgICAgLy8g5pi+56S65pCc57Si5p2h5Lu2DQogICAgICBzaG93U2VhcmNoOiB0cnVlLA0KICAgICAgLy8g5oC75p2h5pWwDQogICAgICB0b3RhbDogMCwNCiAgICAgIC8vIOihgOezluiusOW9leihqOagvOaVsOaNrg0KICAgICAgc3VnYXJMaXN0OiBbXSwNCiAgICAgIC8vIOW8ueWHuuWxguagh+mimA0KICAgICAgdGl0bGU6ICIiLA0KICAgICAgLy8g5piv5ZCm5pi+56S65by55Ye65bGCDQogICAgICBvcGVuOiBmYWxzZSwNCiAgICAgIC8vIOaYr+WQpumihOitpuaXtumXtOiMg+WbtA0KICAgICAgZGF0ZXJhbmdlQ2hlY2tUaW1lOiBbXSwNCiAgICAgIC8vIOaYr+WQpumihOitpuaXtumXtOiMg+WbtA0KICAgICAgZGF0ZXJhbmdlQ3JlYXRlVGltZTogW10sDQogICAgICAvLyDmn6Xor6Llj4LmlbANCiAgICAgIHF1ZXJ5UGFyYW1zOiB7DQogICAgICAgIHBhZ2VOdW06IDEsDQogICAgICAgIHBhZ2VTaXplOiAxMCwNCiAgICAgICAgdmFsdWU6IG51bGwsDQogICAgICAgIHBhdGllbnRJZDogbnVsbCwNCiAgICAgICAgaWRObzogbnVsbCwNCiAgICAgICAgZXF1aXBJZDogbnVsbCwNCiAgICAgICAgZXF1aXBDb2RlOiBudWxsLA0KICAgICAgICB2YWx1ZVR5cGU6IG51bGwsDQogICAgICAgIGRhdGFTb3VyY2U6IG51bGwsDQogICAgICAgIGRhdGFUeXBlOiBudWxsLA0KICAgICAgICBjaGVja1RpbWU6IG51bGwsDQogICAgICAgIHVuaXRJZDogbnVsbCwNCiAgICAgICAgd2FybklmOiBudWxsLA0KICAgICAgfSwNCiAgICAgIC8vIOihqOWNleWPguaVsA0KICAgICAgZm9ybToge30sDQogICAgICAvLyDooajljZXmoKHpqowNCiAgICAgIHJ1bGVzOiB7fSwNCiAgICB9Ow0KICB9LA0KICBjcmVhdGVkKCkgew0KICAgIHRoaXMuZ2V0TGlzdCgpOw0KICB9LA0KICBtZXRob2RzOiB7DQogICAgcGF0aWVudFNlbGVjdChwYXRpZW50KSB7DQogICAgICB0aGlzLmZvcm0ucGF0aWVudElkID0gcGF0aWVudC5pZDsNCiAgICAgIHRoaXMuZm9ybS5pZE5vID0gcGF0aWVudC5pZE5vOw0KICAgICAgdGhpcy5mb3JtLnBhdGllbnROYW1lID0gcGF0aWVudC5uYW1lOw0KICAgIH0sDQogICAgLyoqIOafpeivouihgOezluiusOW9leWIl+ihqCAqLw0KICAgIGdldExpc3QoKSB7DQogICAgICB0aGlzLmxvYWRpbmcgPSB0cnVlOw0KICAgICAgbGlzdFN1Z2FyKHRoaXMucXVlcnlQYXJhbXMpLnRoZW4oKHJlc3BvbnNlKSA9PiB7DQogICAgICAgIHRoaXMuc3VnYXJMaXN0ID0gcmVzcG9uc2Uucm93czsNCiAgICAgICAgdGhpcy50b3RhbCA9IHJlc3BvbnNlLnRvdGFsOw0KICAgICAgICB0aGlzLmxvYWRpbmcgPSBmYWxzZTsNCiAgICAgIH0pOw0KICAgIH0sDQogICAgLyoqIOafpeivouihgOezluiusOW9leWIl+ihqCAqLw0KICAgIGdldExpc3QoKSB7DQogICAgICB0aGlzLmxvYWRpbmcgPSB0cnVlOw0KICAgICAgdGhpcy5xdWVyeVBhcmFtcy5wYXJhbXMgPSB7fTsNCiAgICAgIGlmIChudWxsICE9IHRoaXMuZGF0ZXJhbmdlQ2hlY2tUaW1lICYmICIiICE9IHRoaXMuZGF0ZXJhbmdlQ2hlY2tUaW1lKSB7DQogICAgICAgIHRoaXMucXVlcnlQYXJhbXMucGFyYW1zWyJiZWdpbkNoZWNrVGltZSJdID0gdGhpcy5kYXRlcmFuZ2VDaGVja1RpbWVbMF07DQogICAgICAgIHRoaXMucXVlcnlQYXJhbXMucGFyYW1zWyJlbmRDaGVja1RpbWUiXSA9IHRoaXMuZGF0ZXJhbmdlQ2hlY2tUaW1lWzFdOw0KICAgICAgfQ0KICAgICAgaWYgKG51bGwgIT0gdGhpcy5kYXRlcmFuZ2VDcmVhdGVUaW1lICYmICIiICE9IHRoaXMuZGF0ZXJhbmdlQ3JlYXRlVGltZSkgew0KICAgICAgICB0aGlzLnF1ZXJ5UGFyYW1zLnBhcmFtc1siYmVnaW5DcmVhdGVUaW1lIl0gPQ0KICAgICAgICAgIHRoaXMuZGF0ZXJhbmdlQ3JlYXRlVGltZVswXTsNCiAgICAgICAgdGhpcy5xdWVyeVBhcmFtcy5wYXJhbXNbImVuZENyZWF0ZVRpbWUiXSA9IHRoaXMuZGF0ZXJhbmdlQ3JlYXRlVGltZVsxXTsNCiAgICAgIH0NCiAgICAgIGxpc3RTdWdhcih0aGlzLnF1ZXJ5UGFyYW1zKS50aGVuKChyZXNwb25zZSkgPT4gew0KICAgICAgICB0aGlzLnN1Z2FyTGlzdCA9IHJlc3BvbnNlLnJvd3M7DQogICAgICAgIHRoaXMudG90YWwgPSByZXNwb25zZS50b3RhbDsNCiAgICAgICAgdGhpcy5sb2FkaW5nID0gZmFsc2U7DQogICAgICB9KTsNCiAgICB9LA0KICAgIC8vIOWPlua2iOaMiemSrg0KICAgIGNhbmNlbCgpIHsNCiAgICAgIHRoaXMub3BlbiA9IGZhbHNlOw0KICAgICAgdGhpcy5yZXNldCgpOw0KICAgIH0sDQogICAgLy8g6KGo5Y2V6YeN572uDQogICAgcmVzZXQoKSB7DQogICAgICB0aGlzLmZvcm0gPSB7DQogICAgICAgIGlkOiBudWxsLA0KICAgICAgICB2YWx1ZTogbnVsbCwNCiAgICAgICAgcGF0aWVudElkOiBudWxsLA0KICAgICAgICBpZE5vOiBudWxsLA0KICAgICAgICBlcXVpcElkOiBudWxsLA0KICAgICAgICBlcXVpcENvZGU6IG51bGwsDQogICAgICAgIHZhbHVlVHlwZTogbnVsbCwNCiAgICAgICAgZGF0YVNvdXJjZTogbnVsbCwNCiAgICAgICAgZGF0YVR5cGU6IG51bGwsDQogICAgICAgIGNoZWNrVGltZTogbnVsbCwNCiAgICAgICAgY3JlYXRlVGltZTogbnVsbCwNCiAgICAgICAgY3JlYXRlQnk6IG51bGwsDQogICAgICAgIHVwZGF0ZVRpbWU6IG51bGwsDQogICAgICAgIHVwZGF0ZUJ5OiBudWxsLA0KICAgICAgICB1bml0SWQ6IG51bGwsDQogICAgICAgIHdhcm5JZjogbnVsbCwNCiAgICAgICAgcGF0aWVudE5hbWU6IG51bGwsDQogICAgICB9Ow0KICAgICAgdGhpcy5yZXNldEZvcm0oImZvcm0iKTsNCiAgICAgIGlmICh0aGlzLiRyZWZzLnBhdGllbnRJbnB1dFNlbGVjdCkgdGhpcy4kcmVmcy5wYXRpZW50SW5wdXRTZWxlY3QuY2xlYXIoKTsNCiAgICB9LA0KICAgIC8qKiDmkJzntKLmjInpkq7mk43kvZwgKi8NCiAgICBoYW5kbGVRdWVyeSgpIHsNCiAgICAgIHRoaXMucXVlcnlQYXJhbXMucGFnZU51bSA9IDE7DQogICAgICB0aGlzLmdldExpc3QoKTsNCiAgICB9LA0KICAgIC8qKiDph43nva7mjInpkq7mk43kvZwgKi8NCiAgICByZXNldFF1ZXJ5KCkgew0KICAgICAgdGhpcy5kYXRlcmFuZ2VDaGVja1RpbWUgPSBbXTsNCiAgICAgIHRoaXMuZGF0ZXJhbmdlQ3JlYXRlVGltZSA9IFtdOw0KICAgICAgdGhpcy5yZXNldEZvcm0oInF1ZXJ5Rm9ybSIpOw0KICAgICAgdGhpcy5oYW5kbGVRdWVyeSgpOw0KICAgIH0sDQogICAgLy8g5aSa6YCJ5qGG6YCJ5Lit5pWw5o2uDQogICAgaGFuZGxlU2VsZWN0aW9uQ2hhbmdlKHNlbGVjdGlvbikgew0KICAgICAgdGhpcy5pZHMgPSBzZWxlY3Rpb24ubWFwKChpdGVtKSA9PiBpdGVtLmlkKTsNCiAgICAgIHRoaXMuc2luZ2xlID0gc2VsZWN0aW9uLmxlbmd0aCAhPT0gMTsNCiAgICAgIHRoaXMubXVsdGlwbGUgPSAhc2VsZWN0aW9uLmxlbmd0aDsNCiAgICB9LA0KICAgIC8qKiDmlrDlop7mjInpkq7mk43kvZwgKi8NCiAgICBoYW5kbGVBZGQoKSB7DQogICAgICB0aGlzLnJlc2V0KCk7DQogICAgICB0aGlzLm9wZW4gPSB0cnVlOw0KICAgICAgdGhpcy50aXRsZSA9ICLmt7vliqDooYDns5borrDlvZUiOw0KICAgIH0sDQogICAgLyoqIOS/ruaUueaMiemSruaTjeS9nCAqLw0KICAgIGhhbmRsZVVwZGF0ZShyb3cpIHsNCiAgICAgIHRoaXMucmVzZXQoKTsNCiAgICAgIGNvbnN0IGlkID0gcm93LmlkIHx8IHRoaXMuaWRzOw0KICAgICAgZ2V0U3VnYXIoaWQpLnRoZW4oKHJlc3BvbnNlKSA9PiB7DQogICAgICAgIHRoaXMuZm9ybSA9IHJlc3BvbnNlLmRhdGE7DQogICAgICAgIHRoaXMub3BlbiA9IHRydWU7DQogICAgICAgIHRoaXMudGl0bGUgPSAi5L+u5pS56KGA57OW6K6w5b2VIjsNCiAgICAgIH0pOw0KICAgIH0sDQogICAgLyoqIOaPkOS6pOaMiemSriAqLw0KICAgIHN1Ym1pdEZvcm0oKSB7DQogICAgICB0aGlzLiRyZWZzWyJmb3JtIl0udmFsaWRhdGUoKHZhbGlkKSA9PiB7DQogICAgICAgIGlmICh2YWxpZCkgew0KICAgICAgICAgIGlmICh0aGlzLmZvcm0uaWQgIT0gbnVsbCkgew0KICAgICAgICAgICAgdXBkYXRlU3VnYXIodGhpcy5mb3JtKS50aGVuKChyZXNwb25zZSkgPT4gew0KICAgICAgICAgICAgICB0aGlzLiRtb2RhbC5tc2dTdWNjZXNzKCLkv67mlLnmiJDlip8iKTsNCiAgICAgICAgICAgICAgdGhpcy5vcGVuID0gZmFsc2U7DQogICAgICAgICAgICAgIHRoaXMuZ2V0TGlzdCgpOw0KICAgICAgICAgICAgfSk7DQogICAgICAgICAgfSBlbHNlIHsNCiAgICAgICAgICAgIGFkZFN1Z2FyKHRoaXMuZm9ybSkudGhlbigocmVzcG9uc2UpID0+IHsNCiAgICAgICAgICAgICAgdGhpcy4kbW9kYWwubXNnU3VjY2Vzcygi5paw5aKe5oiQ5YqfIik7DQogICAgICAgICAgICAgIHRoaXMub3BlbiA9IGZhbHNlOw0KICAgICAgICAgICAgICB0aGlzLmdldExpc3QoKTsNCiAgICAgICAgICAgIH0pOw0KICAgICAgICAgIH0NCiAgICAgICAgfQ0KICAgICAgfSk7DQogICAgfSwNCiAgICAvKiog5Yig6Zmk5oyJ6ZKu5pON5L2cICovDQogICAgaGFuZGxlRGVsZXRlKHJvdykgew0KICAgICAgY29uc3QgaWRzID0gcm93LmlkIHx8IHRoaXMuaWRzOw0KICAgICAgdGhpcy4kbW9kYWwNCiAgICAgICAgLmNvbmZpcm0oJ+aYr+WQpuehruiupOWIoOmZpOihgOezluiusOW9lee8luWPt+S4uiInICsgaWRzICsgJyLnmoTmlbDmja7pobnvvJ8nKQ0KICAgICAgICAudGhlbihmdW5jdGlvbiAoKSB7DQogICAgICAgICAgcmV0dXJuIGRlbFN1Z2FyKGlkcyk7DQogICAgICAgIH0pDQogICAgICAgIC50aGVuKCgpID0+IHsNCiAgICAgICAgICB0aGlzLmdldExpc3QoKTsNCiAgICAgICAgICB0aGlzLiRtb2RhbC5tc2dTdWNjZXNzKCLliKDpmaTmiJDlip8iKTsNCiAgICAgICAgfSkNCiAgICAgICAgLmNhdGNoKCgpID0+IHt9KTsNCiAgICB9LA0KICAgIC8qKiDlr7zlh7rmjInpkq7mk43kvZwgKi8NCiAgICBoYW5kbGVFeHBvcnQoKSB7DQogICAgICB0aGlzLmRvd25sb2FkKA0KICAgICAgICAiY2dtL3N1Z2FyL2V4cG9ydCIsDQogICAgICAgIHsNCiAgICAgICAgICAuLi50aGlzLnF1ZXJ5UGFyYW1zLA0KICAgICAgICB9LA0KICAgICAgICBgc3VnYXJfJHtuZXcgRGF0ZSgpLmdldFRpbWUoKX0ueGxzeGANCiAgICAgICk7DQogICAgfSwNCiAgfSwNCn07DQo="}, {"version": 3, "sources": ["index.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAgZA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "index.vue", "sourceRoot": "src/views/cgm/sugar", "sourcesContent": ["<template>\r\n  <div class=\"app-container\">\r\n    <el-form\r\n      :model=\"queryParams\"\r\n      ref=\"queryForm\"\r\n      size=\"small\"\r\n      :inline=\"true\"\r\n      v-show=\"showSearch\"\r\n      label-width=\"68px\"\r\n    >\r\n      <!-- <el-form-item label=\"血糖值\" prop=\"value\">\r\n        <el-input\r\n          v-model=\"queryParams.value\"\r\n          placeholder=\"请输入血糖值\"\r\n          clearable\r\n          @keyup.enter.native=\"handleQuery\"\r\n        />\r\n      </el-form-item> -->\r\n      <!-- <el-form-item label=\"患者id\" prop=\"patientId\">\r\n        <el-input\r\n          v-model=\"queryParams.patientId\"\r\n          placeholder=\"请输入患者id\"\r\n          clearable\r\n          @keyup.enter.native=\"handleQuery\"\r\n        />\r\n      </el-form-item> -->\r\n      <el-form-item label=\"患者身份证\" label-width=\"100px\" prop=\"idNo\">\r\n        <el-input\r\n          v-model=\"queryParams.idNo\"\r\n          placeholder=\"请输入患者身份证\"\r\n          clearable\r\n          @keyup.enter.native=\"handleQuery\"\r\n        />\r\n      </el-form-item>\r\n      <!-- <el-form-item label=\"设备id\" prop=\"equipId\">\r\n        <el-input\r\n          v-model=\"queryParams.equipId\"\r\n          placeholder=\"请输入设备id\"\r\n          clearable\r\n          @keyup.enter.native=\"handleQuery\"\r\n        />\r\n      </el-form-item> -->\r\n      <el-form-item label=\"设备编号\" prop=\"equipCode\">\r\n        <el-input\r\n          v-model=\"queryParams.equipCode\"\r\n          placeholder=\"请输入设备编号\"\r\n          clearable\r\n          @keyup.enter.native=\"handleQuery\"\r\n        />\r\n      </el-form-item>\r\n      <el-form-item label=\"数值类型\" prop=\"valueType\">\r\n        <el-select\r\n          v-model=\"queryParams.valueType\"\r\n          placeholder=\"请选择数值类型\"\r\n          clearable\r\n        >\r\n          <el-option\r\n            v-for=\"dict in dict.type.sugar_value_type\"\r\n            :key=\"dict.value\"\r\n            :label=\"dict.label\"\r\n            :value=\"dict.value\"\r\n          />\r\n        </el-select>\r\n      </el-form-item>\r\n      <el-form-item label=\"数据来源\" prop=\"dataSource\">\r\n        <!-- <el-input\r\n          v-model=\"queryParams.dataSource\"\r\n          placeholder=\"请输入数据来源\"\r\n          clearable\r\n          @keyup.enter.native=\"handleQuery\"\r\n        /> -->\r\n        <el-select\r\n          v-model=\"queryParams.dataSource\"\r\n          placeholder=\"请选择数据来源\"\r\n          clearable\r\n        >\r\n          <el-option\r\n            v-for=\"dict in dict.type.equip_use_type\"\r\n            :key=\"dict.value\"\r\n            :label=\"dict.label\"\r\n            :value=\"dict.value\"\r\n          />\r\n        </el-select>\r\n      </el-form-item>\r\n      <el-form-item label=\"数据类型\" prop=\"dataType\">\r\n        <el-select\r\n          v-model=\"queryParams.dataType\"\r\n          placeholder=\"请选择数据类型\"\r\n          clearable\r\n        >\r\n          <el-option\r\n            v-for=\"dict in dict.type.sugar_data_type\"\r\n            :key=\"dict.value\"\r\n            :label=\"dict.label\"\r\n            :value=\"dict.value\"\r\n          />\r\n        </el-select>\r\n      </el-form-item>\r\n      <el-form-item label=\"测量时间\">\r\n        <el-date-picker\r\n          v-model=\"daterangeCheckTime\"\r\n          style=\"width: 240px\"\r\n          value-format=\"yyyy-MM-dd\"\r\n          type=\"daterange\"\r\n          range-separator=\"-\"\r\n          start-placeholder=\"开始日期\"\r\n          end-placeholder=\"结束日期\"\r\n        ></el-date-picker>\r\n      </el-form-item>\r\n      <!-- <el-form-item label=\"机构ID\" prop=\"unitId\">\r\n        <el-input\r\n          v-model=\"queryParams.unitId\"\r\n          placeholder=\"请输入机构ID\"\r\n          clearable\r\n          @keyup.enter.native=\"handleQuery\"\r\n        />\r\n      </el-form-item>\r\n      <el-form-item label=\"是否预警\" prop=\"warnIf\">\r\n        <el-select\r\n          v-model=\"queryParams.warnIf\"\r\n          placeholder=\"请选择是否预警\"\r\n          clearable\r\n        >\r\n          <el-option\r\n            v-for=\"dict in dict.type.sys_yes_no\"\r\n            :key=\"dict.value\"\r\n            :label=\"dict.label\"\r\n            :value=\"dict.value\"\r\n          />\r\n        </el-select>\r\n      </el-form-item> -->\r\n      <el-form-item>\r\n        <el-button\r\n          type=\"primary\"\r\n          icon=\"el-icon-search\"\r\n          size=\"mini\"\r\n          @click=\"handleQuery\"\r\n          >搜索</el-button\r\n        >\r\n        <el-button icon=\"el-icon-refresh\" size=\"mini\" @click=\"resetQuery\"\r\n          >重置</el-button\r\n        >\r\n      </el-form-item>\r\n    </el-form>\r\n\r\n    <el-row :gutter=\"10\" class=\"mb8\">\r\n      <el-col :span=\"1.5\">\r\n        <el-button\r\n          type=\"primary\"\r\n          plain\r\n          icon=\"el-icon-plus\"\r\n          size=\"mini\"\r\n          @click=\"handleAdd\"\r\n          v-hasPermi=\"['cgm:sugar:add']\"\r\n          >新增</el-button\r\n        >\r\n      </el-col>\r\n      <el-col :span=\"1.5\">\r\n        <el-button\r\n          type=\"primary\"\r\n          plain\r\n          icon=\"el-icon-edit\"\r\n          size=\"mini\"\r\n          :disabled=\"single\"\r\n          @click=\"handleUpdate\"\r\n          v-hasPermi=\"['cgm:sugar:edit']\"\r\n          >修改</el-button\r\n        >\r\n      </el-col>\r\n      <el-col :span=\"1.5\">\r\n        <el-button\r\n          type=\"primary\"\r\n          plain\r\n          icon=\"el-icon-delete\"\r\n          size=\"mini\"\r\n          :disabled=\"multiple\"\r\n          @click=\"handleDelete\"\r\n          v-hasPermi=\"['cgm:sugar:remove']\"\r\n          >删除</el-button\r\n        >\r\n      </el-col>\r\n      <!-- <el-col :span=\"1.5\">\r\n        <el-button\r\n          type=\"warning\"\r\n          plain\r\n          icon=\"el-icon-download\"\r\n          size=\"mini\"\r\n          @click=\"handleExport\"\r\n          v-hasPermi=\"['cgm:sugar:export']\"\r\n          >导出</el-button\r\n        >\r\n      </el-col> -->\r\n      <right-toolbar\r\n        :showSearch.sync=\"showSearch\"\r\n        @queryTable=\"getList\"\r\n      ></right-toolbar>\r\n    </el-row>\r\n\r\n    <el-table\r\n      v-loading=\"loading\"\r\n      :data=\"sugarList\"\r\n      @selection-change=\"handleSelectionChange\"\r\n    >\r\n      <el-table-column type=\"selection\" width=\"55\" align=\"center\" />\r\n      <!-- <el-table-column label=\"id\" align=\"center\" prop=\"id\" /> -->\r\n      <el-table-column label=\"患者姓名\" align=\"center\" prop=\"patientName\" />\r\n      <el-table-column\r\n        label=\"患者身份证\"\r\n        align=\"center\"\r\n        prop=\"idNo\"\r\n        width=\"120\"\r\n      />\r\n      <el-table-column label=\"血糖值\" align=\"center\" prop=\"value\" />\r\n\r\n      <!-- <el-table-column label=\"设备id\" align=\"center\" prop=\"equipId\" /> -->\r\n      <el-table-column label=\"设备编号\" align=\"center\" prop=\"equipCode\" />\r\n      <el-table-column label=\"数值类型\" align=\"center\" prop=\"valueType\">\r\n        <template slot-scope=\"scope\">\r\n          <dict-tag\r\n            :options=\"dict.type.sugar_value_type\"\r\n            :value=\"scope.row.valueType\"\r\n          />\r\n        </template>\r\n      </el-table-column>\r\n      <el-table-column label=\"数据来源\" align=\"center\" prop=\"dataSource\">\r\n        <template slot-scope=\"scope\">\r\n          <dict-tag\r\n            :options=\"dict.type.equip_use_type\"\r\n            :value=\"scope.row.dataSource\"\r\n          />\r\n        </template>\r\n      </el-table-column>\r\n      <el-table-column label=\"数据类型\" align=\"center\" prop=\"dataType\">\r\n        <template slot-scope=\"scope\">\r\n          <dict-tag\r\n            :options=\"dict.type.sugar_data_type\"\r\n            :value=\"scope.row.dataType\"\r\n          />\r\n        </template>\r\n      </el-table-column>\r\n      <el-table-column\r\n        label=\"测量时间\"\r\n        align=\"center\"\r\n        prop=\"checkTime\"\r\n        width=\"180\"\r\n      >\r\n        <template slot-scope=\"scope\">\r\n          <span>{{ parseTime(scope.row.checkTime) }}</span>\r\n        </template>\r\n      </el-table-column>\r\n      <el-table-column\r\n        label=\"创建时间\"\r\n        align=\"center\"\r\n        prop=\"createTime\"\r\n        width=\"180\"\r\n      >\r\n        <template slot-scope=\"scope\">\r\n          <span>{{ parseTime(scope.row.createTime) }}</span>\r\n        </template>\r\n      </el-table-column>\r\n      <!-- <el-table-column label=\"机构ID\" align=\"center\" prop=\"unitId\" /> -->\r\n      <!-- <el-table-column label=\"是否预警\" align=\"center\" prop=\"warnIf\">\r\n        <template slot-scope=\"scope\">\r\n          <dict-tag :options=\"dict.type.sys_yes_no\" :value=\"scope.row.warnIf\" />\r\n        </template>\r\n      </el-table-column> -->\r\n      <el-table-column\r\n        label=\"操作\"\r\n        align=\"center\"\r\n        class-name=\"small-padding fixed-width\"\r\n      >\r\n        <template slot-scope=\"scope\">\r\n          <el-button\r\n            size=\"mini\"\r\n            type=\"text\"\r\n            icon=\"el-icon-edit\"\r\n            @click=\"handleUpdate(scope.row)\"\r\n            v-hasPermi=\"['cgm:sugar:edit']\"\r\n            >修改</el-button\r\n          >\r\n          <el-button\r\n            size=\"mini\"\r\n            type=\"text\"\r\n            icon=\"el-icon-delete\"\r\n            @click=\"handleDelete(scope.row)\"\r\n            v-hasPermi=\"['cgm:sugar:remove']\"\r\n            >删除</el-button\r\n          >\r\n        </template>\r\n      </el-table-column>\r\n    </el-table>\r\n\r\n    <pagination\r\n      v-show=\"total > 0\"\r\n      :total=\"total\"\r\n      :page.sync=\"queryParams.pageNum\"\r\n      :limit.sync=\"queryParams.pageSize\"\r\n      @pagination=\"getList\"\r\n    />\r\n\r\n    <!-- 添加或修改血糖记录对话框 -->\r\n    <el-dialog :title=\"title\" :visible.sync=\"open\" width=\"500px\" append-to-body>\r\n      <el-form ref=\"form\" :model=\"form\" :rules=\"rules\" label-width=\"80px\">\r\n        <el-form-item label=\"血糖值\" prop=\"value\">\r\n          <el-input v-model=\"form.value\" placeholder=\"请输入血糖值\" />\r\n        </el-form-item>\r\n        <el-form-item label=\"患者\" prop=\"patientId\">\r\n          <!-- <el-input v-model=\"form.patientId\" placeholder=\"请输入患者id\" /> -->\r\n          <PatientInputSelect\r\n            ref=\"patientInputSelect\"\r\n            :pid=\"form.patientId\"\r\n            @change=\"patientSelect\"\r\n          />\r\n        </el-form-item>\r\n        <!-- <el-form-item label=\"患者身份证\" prop=\"idNo\">\r\n          <el-input v-model=\"form.idNo\" placeholder=\"请输入患者身份证\" />\r\n        </el-form-item> -->\r\n        <!-- <el-form-item label=\"设备id\" prop=\"equipId\">\r\n          <el-input v-model=\"form.equipId\" placeholder=\"请输入设备id\" />\r\n        </el-form-item> -->\r\n        <!-- <el-form-item label=\"设备编号\" prop=\"equipCode\">\r\n          <el-input v-model=\"form.equipCode\" placeholder=\"请输入设备编号\" />\r\n        </el-form-item> -->\r\n        <el-form-item label=\"数值类型\" prop=\"valueType\">\r\n          <el-select\r\n            style=\"width: 100%\"\r\n            v-model=\"form.valueType\"\r\n            placeholder=\"请选择数值类型\"\r\n          >\r\n            <el-option\r\n              v-for=\"dict in dict.type.sugar_value_type\"\r\n              :key=\"dict.value\"\r\n              :label=\"dict.label\"\r\n              :value=\"parseInt(dict.value)\"\r\n            ></el-option>\r\n          </el-select>\r\n        </el-form-item>\r\n        <el-form-item label=\"数据来源\" prop=\"dataSource\">\r\n          <!-- <el-input v-model=\"form.dataSource\" placeholder=\"请输入数据来源\" /> -->\r\n          <el-select\r\n            style=\"width: 100%\"\r\n            v-model=\"form.dataSource\"\r\n            placeholder=\"请选择数据来源\"\r\n          >\r\n            <el-option\r\n              v-for=\"dict in dict.type.equip_use_type\"\r\n              :key=\"dict.value\"\r\n              :label=\"dict.label\"\r\n              :value=\"parseInt(dict.value)\"\r\n            ></el-option>\r\n          </el-select>\r\n        </el-form-item>\r\n        <el-form-item label=\"数据类型\" prop=\"dataType\">\r\n          <el-select\r\n            style=\"width: 100%\"\r\n            v-model=\"form.dataType\"\r\n            placeholder=\"请选择数据类型\"\r\n          >\r\n            <el-option\r\n              v-for=\"dict in dict.type.sugar_data_type\"\r\n              :key=\"dict.value\"\r\n              :label=\"dict.label\"\r\n              :value=\"parseInt(dict.value)\"\r\n            ></el-option>\r\n          </el-select>\r\n        </el-form-item>\r\n        <el-form-item label=\"测量时间\" prop=\"checkTime\">\r\n          <el-date-picker\r\n            clearable\r\n            style=\"width: 100%\"\r\n            v-model=\"form.checkTime\"\r\n            type=\"datetime\"\r\n            value-format=\"yyyy-MM-dd HH:mm:ss\"\r\n            placeholder=\"请选择测量时间\"\r\n          >\r\n          </el-date-picker>\r\n        </el-form-item>\r\n        <!--<el-form-item label=\"机构ID\" prop=\"unitId\">\r\n          <el-input v-model=\"form.unitId\" placeholder=\"请输入机构ID\" />\r\n        </el-form-item>\r\n        <el-form-item label=\"是否预警\" prop=\"warnIf\">\r\n          <el-radio-group v-model=\"form.warnIf\">\r\n            <el-radio\r\n              v-for=\"dict in dict.type.sys_yes_no\"\r\n              :key=\"dict.value\"\r\n              :label=\"parseInt(dict.value)\"\r\n              >{{ dict.label }}</el-radio\r\n            >\r\n          </el-radio-group>\r\n        </el-form-item> -->\r\n      </el-form>\r\n      <div slot=\"footer\" class=\"dialog-footer\">\r\n        <el-button type=\"primary\" @click=\"submitForm\">确 定</el-button>\r\n        <el-button @click=\"cancel\">取 消</el-button>\r\n      </div>\r\n    </el-dialog>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport {\r\n  listSugar,\r\n  getSugar,\r\n  delSugar,\r\n  addSugar,\r\n  updateSugar,\r\n} from \"@/api/cgm/sugar\";\r\nimport PatientInputSelect from \"@/components/PatientInputSelect\";\r\n\r\nexport default {\r\n  name: \"Sugar\",\r\n  dicts: [\r\n    \"sugar_value_type\",\r\n    \"sys_yes_no\",\r\n    \"sugar_data_type\",\r\n    \"equip_use_type\",\r\n  ],\r\n  components: { PatientInputSelect },\r\n  data() {\r\n    return {\r\n      // 遮罩层\r\n      loading: true,\r\n      // 选中数组\r\n      ids: [],\r\n      // 非单个禁用\r\n      single: true,\r\n      // 非多个禁用\r\n      multiple: true,\r\n      // 显示搜索条件\r\n      showSearch: true,\r\n      // 总条数\r\n      total: 0,\r\n      // 血糖记录表格数据\r\n      sugarList: [],\r\n      // 弹出层标题\r\n      title: \"\",\r\n      // 是否显示弹出层\r\n      open: false,\r\n      // 是否预警时间范围\r\n      daterangeCheckTime: [],\r\n      // 是否预警时间范围\r\n      daterangeCreateTime: [],\r\n      // 查询参数\r\n      queryParams: {\r\n        pageNum: 1,\r\n        pageSize: 10,\r\n        value: null,\r\n        patientId: null,\r\n        idNo: null,\r\n        equipId: null,\r\n        equipCode: null,\r\n        valueType: null,\r\n        dataSource: null,\r\n        dataType: null,\r\n        checkTime: null,\r\n        unitId: null,\r\n        warnIf: null,\r\n      },\r\n      // 表单参数\r\n      form: {},\r\n      // 表单校验\r\n      rules: {},\r\n    };\r\n  },\r\n  created() {\r\n    this.getList();\r\n  },\r\n  methods: {\r\n    patientSelect(patient) {\r\n      this.form.patientId = patient.id;\r\n      this.form.idNo = patient.idNo;\r\n      this.form.patientName = patient.name;\r\n    },\r\n    /** 查询血糖记录列表 */\r\n    getList() {\r\n      this.loading = true;\r\n      listSugar(this.queryParams).then((response) => {\r\n        this.sugarList = response.rows;\r\n        this.total = response.total;\r\n        this.loading = false;\r\n      });\r\n    },\r\n    /** 查询血糖记录列表 */\r\n    getList() {\r\n      this.loading = true;\r\n      this.queryParams.params = {};\r\n      if (null != this.daterangeCheckTime && \"\" != this.daterangeCheckTime) {\r\n        this.queryParams.params[\"beginCheckTime\"] = this.daterangeCheckTime[0];\r\n        this.queryParams.params[\"endCheckTime\"] = this.daterangeCheckTime[1];\r\n      }\r\n      if (null != this.daterangeCreateTime && \"\" != this.daterangeCreateTime) {\r\n        this.queryParams.params[\"beginCreateTime\"] =\r\n          this.daterangeCreateTime[0];\r\n        this.queryParams.params[\"endCreateTime\"] = this.daterangeCreateTime[1];\r\n      }\r\n      listSugar(this.queryParams).then((response) => {\r\n        this.sugarList = response.rows;\r\n        this.total = response.total;\r\n        this.loading = false;\r\n      });\r\n    },\r\n    // 取消按钮\r\n    cancel() {\r\n      this.open = false;\r\n      this.reset();\r\n    },\r\n    // 表单重置\r\n    reset() {\r\n      this.form = {\r\n        id: null,\r\n        value: null,\r\n        patientId: null,\r\n        idNo: null,\r\n        equipId: null,\r\n        equipCode: null,\r\n        valueType: null,\r\n        dataSource: null,\r\n        dataType: null,\r\n        checkTime: null,\r\n        createTime: null,\r\n        createBy: null,\r\n        updateTime: null,\r\n        updateBy: null,\r\n        unitId: null,\r\n        warnIf: null,\r\n        patientName: null,\r\n      };\r\n      this.resetForm(\"form\");\r\n      if (this.$refs.patientInputSelect) this.$refs.patientInputSelect.clear();\r\n    },\r\n    /** 搜索按钮操作 */\r\n    handleQuery() {\r\n      this.queryParams.pageNum = 1;\r\n      this.getList();\r\n    },\r\n    /** 重置按钮操作 */\r\n    resetQuery() {\r\n      this.daterangeCheckTime = [];\r\n      this.daterangeCreateTime = [];\r\n      this.resetForm(\"queryForm\");\r\n      this.handleQuery();\r\n    },\r\n    // 多选框选中数据\r\n    handleSelectionChange(selection) {\r\n      this.ids = selection.map((item) => item.id);\r\n      this.single = selection.length !== 1;\r\n      this.multiple = !selection.length;\r\n    },\r\n    /** 新增按钮操作 */\r\n    handleAdd() {\r\n      this.reset();\r\n      this.open = true;\r\n      this.title = \"添加血糖记录\";\r\n    },\r\n    /** 修改按钮操作 */\r\n    handleUpdate(row) {\r\n      this.reset();\r\n      const id = row.id || this.ids;\r\n      getSugar(id).then((response) => {\r\n        this.form = response.data;\r\n        this.open = true;\r\n        this.title = \"修改血糖记录\";\r\n      });\r\n    },\r\n    /** 提交按钮 */\r\n    submitForm() {\r\n      this.$refs[\"form\"].validate((valid) => {\r\n        if (valid) {\r\n          if (this.form.id != null) {\r\n            updateSugar(this.form).then((response) => {\r\n              this.$modal.msgSuccess(\"修改成功\");\r\n              this.open = false;\r\n              this.getList();\r\n            });\r\n          } else {\r\n            addSugar(this.form).then((response) => {\r\n              this.$modal.msgSuccess(\"新增成功\");\r\n              this.open = false;\r\n              this.getList();\r\n            });\r\n          }\r\n        }\r\n      });\r\n    },\r\n    /** 删除按钮操作 */\r\n    handleDelete(row) {\r\n      const ids = row.id || this.ids;\r\n      this.$modal\r\n        .confirm('是否确认删除血糖记录编号为\"' + ids + '\"的数据项？')\r\n        .then(function () {\r\n          return delSugar(ids);\r\n        })\r\n        .then(() => {\r\n          this.getList();\r\n          this.$modal.msgSuccess(\"删除成功\");\r\n        })\r\n        .catch(() => {});\r\n    },\r\n    /** 导出按钮操作 */\r\n    handleExport() {\r\n      this.download(\r\n        \"cgm/sugar/export\",\r\n        {\r\n          ...this.queryParams,\r\n        },\r\n        `sugar_${new Date().getTime()}.xlsx`\r\n      );\r\n    },\r\n  },\r\n};\r\n</script>\r\n"]}]}