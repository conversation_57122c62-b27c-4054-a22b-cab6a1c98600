{"remainingRequest": "D:\\shangchen\\casz-fat-j21\\casz-fat-ui\\node_modules\\babel-loader\\lib\\index.js!D:\\shangchen\\casz-fat-j21\\casz-fat-ui\\node_modules\\eslint-loader\\index.js??ref--13-0!D:\\shangchen\\casz-fat-j21\\casz-fat-ui\\src\\api\\openIM\\msg.js", "dependencies": [{"path": "D:\\shangchen\\casz-fat-j21\\casz-fat-ui\\src\\api\\openIM\\msg.js", "mtime": 1752668934333}, {"path": "D:\\shangchen\\casz-fat-j21\\casz-fat-ui\\babel.config.js", "mtime": 1752668935560}, {"path": "D:\\shangchen\\casz-fat-j21\\casz-fat-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1747273083419}, {"path": "D:\\shangchen\\casz-fat-j21\\casz-fat-ui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1747273094151}, {"path": "D:\\shangchen\\casz-fat-j21\\casz-fat-ui\\node_modules\\eslint-loader\\index.js", "mtime": 1747273087917}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:InVzZSBzdHJpY3QiOwoKdmFyIF9pbnRlcm9wUmVxdWlyZURlZmF1bHQgPSByZXF1aXJlKCJEOi9zaGFuZ2NoZW4vY2Fzei1mYXQtajIxL2Nhc3otZmF0LXVpL25vZGVfbW9kdWxlcy9AYmFiZWwvcnVudGltZS9oZWxwZXJzL2ludGVyb3BSZXF1aXJlRGVmYXVsdC5qcyIpLmRlZmF1bHQ7Ck9iamVjdC5kZWZpbmVQcm9wZXJ0eShleHBvcnRzLCAiX19lc01vZHVsZSIsIHsKICB2YWx1ZTogdHJ1ZQp9KTsKZXhwb3J0cy5nZXRfc29ydGVkX2NvbnZlcnNhdGlvbl9saXN0ID0gZ2V0X3NvcnRlZF9jb252ZXJzYXRpb25fbGlzdDsKdmFyIF9pbVJlcXVlc3QgPSBfaW50ZXJvcFJlcXVpcmVEZWZhdWx0KHJlcXVpcmUoIkAvdXRpbHMvaW1SZXF1ZXN0IikpOwovL+iOt+WPlueUqOaIt+WlveWPi+WIl+ihqApmdW5jdGlvbiBnZXRfc29ydGVkX2NvbnZlcnNhdGlvbl9saXN0KHBhcmFtcykgewogIHJldHVybiAoMCwgX2ltUmVxdWVzdC5kZWZhdWx0KSh7CiAgICB1cmw6ICcvY29udmVyc2F0aW9uL2dldF9zb3J0ZWRfY29udmVyc2F0aW9uX2xpc3QnLAogICAgbWV0aG9kOiAncG9zdCcsCiAgICBkYXRhOiBwYXJhbXMKICB9KTsKfQ=="}, {"version": 3, "names": ["_imRequest", "_interopRequireDefault", "require", "get_sorted_conversation_list", "params", "request", "url", "method", "data"], "sources": ["D:/shangchen/casz-fat-j21/casz-fat-ui/src/api/openIM/msg.js"], "sourcesContent": ["import request  from '@/utils/imRequest'\r\n\r\n//获取用户好友列表\r\nexport function get_sorted_conversation_list(params){\r\n    return request({\r\n        url: '/conversation/get_sorted_conversation_list',\r\n        method: 'post',\r\n        data: params\r\n    })\r\n}\r\n"], "mappings": ";;;;;;;AAAA,IAAAA,UAAA,GAAAC,sBAAA,CAAAC,OAAA;AAEA;AACO,SAASC,4BAA4BA,CAACC,MAAM,EAAC;EAChD,OAAO,IAAAC,kBAAO,EAAC;IACXC,GAAG,EAAE,4CAA4C;IACjDC,MAAM,EAAE,MAAM;IACdC,IAAI,EAAEJ;EACV,CAAC,CAAC;AACN", "ignoreList": []}]}