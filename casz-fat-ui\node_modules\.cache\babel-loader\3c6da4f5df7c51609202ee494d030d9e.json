{"remainingRequest": "D:\\shangchen\\casz-fat-j21\\casz-fat-ui\\node_modules\\babel-loader\\lib\\index.js!D:\\shangchen\\casz-fat-j21\\casz-fat-ui\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!D:\\shangchen\\casz-fat-j21\\casz-fat-ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\shangchen\\casz-fat-j21\\casz-fat-ui\\src\\components\\EquipSelect\\index.vue?vue&type=script&lang=js", "dependencies": [{"path": "D:\\shangchen\\casz-fat-j21\\casz-fat-ui\\src\\components\\EquipSelect\\index.vue", "mtime": 1752668934586}, {"path": "D:\\shangchen\\casz-fat-j21\\casz-fat-ui\\babel.config.js", "mtime": 1752668935560}, {"path": "D:\\shangchen\\casz-fat-j21\\casz-fat-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1747273083419}, {"path": "D:\\shangchen\\casz-fat-j21\\casz-fat-ui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1747273094151}, {"path": "D:\\shangchen\\casz-fat-j21\\casz-fat-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1747273083419}, {"path": "D:\\shangchen\\casz-fat-j21\\casz-fat-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1747273098384}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "names": ["_equipment", "require", "_pinyinPro", "name", "dicts", "props", "eid", "type", "String", "default", "data", "equipId", "options", "waitOptions", "equipList", "watch", "val", "created", "getEquipmentList", "methods", "_this", "listEquipment", "pageNum", "pageSize", "then", "response", "rows", "for<PERSON>ach", "item", "push", "value", "id", "label", "equipCode", "vender", "selectDictLabel", "dict", "equip_type", "clear", "change", "equipment", "find", "$emit", "focus", "filter", "undefined", "trim", "arr", "itemPinyin", "pinyin", "toneType", "separator", "nonZh", "itemFirstPinyin", "pattern", "console", "log", "indexOf"], "sources": ["src/components/EquipSelect/index.vue"], "sourcesContent": ["<template>\r\n  <el-select\r\n    v-model=\"equipId\"\r\n    filterable\r\n    :filter-method=\"filter\"\r\n    placeholder=\"请选择设备\"\r\n    style=\"width: 100%\"\r\n    @change=\"change\"\r\n    @focus=\"focus\"\r\n  >\r\n    <el-option\r\n      v-for=\"item in waitOptions\"\r\n      :key=\"item.value\"\r\n      :label=\"item.label\"\r\n      :value=\"item.value\"\r\n    >\r\n    </el-option>\r\n  </el-select>\r\n</template>\r\n\r\n<script>\r\nimport { listEquipment } from \"@/api/equip/equipment\";\r\nimport { pinyin } from \"pinyin-pro\";\r\nexport default {\r\n  name: \"EquipSelect\",\r\n  dicts: [\"equip_type\"],\r\n  props: {\r\n    eid: { type: String, default: null },\r\n  },\r\n  data() {\r\n    return {\r\n      equipId: \"\",\r\n      options: [],\r\n      waitOptions: [],\r\n      equipList: [],\r\n    };\r\n  },\r\n  watch: {\r\n    eid(val) {\r\n      if (val) {\r\n        this.equipId = val;\r\n      }\r\n    },\r\n  },\r\n  created() {\r\n    this.getEquipmentList();\r\n  },\r\n  methods: {\r\n    /** 查询患者数据列表 */\r\n    getEquipmentList() {\r\n      listEquipment({ pageNum: 1, pageSize: 1000000 }).then((response) => {\r\n        this.equipList = response.rows;\r\n        this.options = [];\r\n        this.equipList.forEach((item) => {\r\n          this.options.push({\r\n            value: item.id,\r\n            label:\r\n              item.name +\r\n              \"/\" +\r\n              item.equipCode +\r\n              \"/\" +\r\n              item.vender +\r\n              \"/\" +\r\n              this.selectDictLabel(this.dict.type.equip_type, item.type),\r\n          });\r\n        });\r\n        this.waitOptions = this.options;\r\n      });\r\n    },\r\n    clear() {\r\n      this.equipId = \"\";\r\n    },\r\n    change(value) {\r\n      let equipment = this.equipList.find((item) => {\r\n        return item.id === value;\r\n      });\r\n      this.$emit(\"change\", equipment);\r\n    },\r\n    focus() {\r\n      this.waitOptions = this.options;\r\n    },\r\n    filter(val) {\r\n      if (val === null || val === undefined || val.trim() === \"\") {\r\n        this.waitOptions = this.options;\r\n        return;\r\n      }\r\n      val = val.trim();\r\n      const arr = this.options.filter((item) => {\r\n        const itemPinyin = pinyin(item.label, {\r\n          toneType: \"none\",\r\n          separator: \"\",\r\n          nonZh: \"removed\",\r\n        }); // 'hanyupinyin' 汉语拼音\r\n        const itemFirstPinyin = pinyin(item.label, {\r\n          pattern: \"first\",\r\n          separator: \"\",\r\n          toneType: \"none\",\r\n          nonZh: \"removed\",\r\n        }); // 'hypy'\r\n        console.log(itemPinyin, itemFirstPinyin);\r\n        return (\r\n          item.label.indexOf(val) > -1 ||\r\n          itemPinyin.indexOf(val) > -1 ||\r\n          itemFirstPinyin.indexOf(val) > -1\r\n        );\r\n      });\r\n      console.log(\"@@@\", arr);\r\n      this.waitOptions = arr;\r\n    },\r\n  },\r\n};\r\n</script>\r\n\r\n<style>\r\n</style>"], "mappings": ";;;;;;;;;;;;;;;;;AAqBA,IAAAA,UAAA,GAAAC,OAAA;AACA,IAAAC,UAAA,GAAAD,OAAA;;;;;;;;;;;;;;;;;;;;;iCACA;EACAE,IAAA;EACAC,KAAA;EACAC,KAAA;IACAC,GAAA;MAAAC,IAAA,EAAAC,MAAA;MAAAC,OAAA;IAAA;EACA;EACAC,IAAA,WAAAA,KAAA;IACA;MACAC,OAAA;MACAC,OAAA;MACAC,WAAA;MACAC,SAAA;IACA;EACA;EACAC,KAAA;IACAT,GAAA,WAAAA,IAAAU,GAAA;MACA,IAAAA,GAAA;QACA,KAAAL,OAAA,GAAAK,GAAA;MACA;IACA;EACA;EACAC,OAAA,WAAAA,QAAA;IACA,KAAAC,gBAAA;EACA;EACAC,OAAA;IACA,eACAD,gBAAA,WAAAA,iBAAA;MAAA,IAAAE,KAAA;MACA,IAAAC,wBAAA;QAAAC,OAAA;QAAAC,QAAA;MAAA,GAAAC,IAAA,WAAAC,QAAA;QACAL,KAAA,CAAAN,SAAA,GAAAW,QAAA,CAAAC,IAAA;QACAN,KAAA,CAAAR,OAAA;QACAQ,KAAA,CAAAN,SAAA,CAAAa,OAAA,WAAAC,IAAA;UACAR,KAAA,CAAAR,OAAA,CAAAiB,IAAA;YACAC,KAAA,EAAAF,IAAA,CAAAG,EAAA;YACAC,KAAA,EACAJ,IAAA,CAAAzB,IAAA,GACA,MACAyB,IAAA,CAAAK,SAAA,GACA,MACAL,IAAA,CAAAM,MAAA,GACA,MACAd,KAAA,CAAAe,eAAA,CAAAf,KAAA,CAAAgB,IAAA,CAAA7B,IAAA,CAAA8B,UAAA,EAAAT,IAAA,CAAArB,IAAA;UACA;QACA;QACAa,KAAA,CAAAP,WAAA,GAAAO,KAAA,CAAAR,OAAA;MACA;IACA;IACA0B,KAAA,WAAAA,MAAA;MACA,KAAA3B,OAAA;IACA;IACA4B,MAAA,WAAAA,OAAAT,KAAA;MACA,IAAAU,SAAA,QAAA1B,SAAA,CAAA2B,IAAA,WAAAb,IAAA;QACA,OAAAA,IAAA,CAAAG,EAAA,KAAAD,KAAA;MACA;MACA,KAAAY,KAAA,WAAAF,SAAA;IACA;IACAG,KAAA,WAAAA,MAAA;MACA,KAAA9B,WAAA,QAAAD,OAAA;IACA;IACAgC,MAAA,WAAAA,OAAA5B,GAAA;MACA,IAAAA,GAAA,aAAAA,GAAA,KAAA6B,SAAA,IAAA7B,GAAA,CAAA8B,IAAA;QACA,KAAAjC,WAAA,QAAAD,OAAA;QACA;MACA;MACAI,GAAA,GAAAA,GAAA,CAAA8B,IAAA;MACA,IAAAC,GAAA,QAAAnC,OAAA,CAAAgC,MAAA,WAAAhB,IAAA;QACA,IAAAoB,UAAA,OAAAC,iBAAA,EAAArB,IAAA,CAAAI,KAAA;UACAkB,QAAA;UACAC,SAAA;UACAC,KAAA;QACA;QACA,IAAAC,eAAA,OAAAJ,iBAAA,EAAArB,IAAA,CAAAI,KAAA;UACAsB,OAAA;UACAH,SAAA;UACAD,QAAA;UACAE,KAAA;QACA;QACAG,OAAA,CAAAC,GAAA,CAAAR,UAAA,EAAAK,eAAA;QACA,OACAzB,IAAA,CAAAI,KAAA,CAAAyB,OAAA,CAAAzC,GAAA,UACAgC,UAAA,CAAAS,OAAA,CAAAzC,GAAA,UACAqC,eAAA,CAAAI,OAAA,CAAAzC,GAAA;MAEA;MACAuC,OAAA,CAAAC,GAAA,QAAAT,GAAA;MACA,KAAAlC,WAAA,GAAAkC,GAAA;IACA;EACA;AACA", "ignoreList": []}]}