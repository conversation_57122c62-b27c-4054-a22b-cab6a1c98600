# Cascader 级联选择器使用示例

## 基本用法

在 formData 中添加 cascader 类型的配置项：

```javascript
{
    id: "region_cascader",
    compType: "cascader",
    label: "地区选择",
    placeholder: "请选择地区",
    showLabel: true,
    required: true,
    disabled: false,
    
    // 数据类型：static（静态数据）或 dynamic（动态数据）
    dataType: 'dynamic',
    
    // 动态数据时的接口标识（对应字典类型）
    action: 'cityDict',
    
    // 级联选择器配置
    popupTitle: '请选择地区',
    split: '/',
    
    // 字段映射配置（可选）
    cascaderMap: {
        text: 'label',    // 显示字段
        value: 'value',   // 值字段
        children: 'children'  // 子节点字段
    },
    
    // 静态数据示例（当 dataType 为 'static' 时使用）
    options: [
        {
            label: '福建省',
            value: 'fujian',
            children: [
                {
                    label: '泉州市',
                    value: 'quanzhou',
                    children: [
                        { label: '鲤城区', value: 'licheng' },
                        { label: '丰泽区', value: 'fengze' },
                        { label: '洛江区', value: 'luojiang' }
                    ]
                },
                {
                    label: '福州市',
                    value: 'fuzhou',
                    children: [
                        { label: '鼓楼区', value: 'gulou' },
                        { label: '台江区', value: 'taijiang' }
                    ]
                }
            ]
        }
    ]
}
```

## 完整示例

```vue
<template>
    <view class="content">
        <form-item 
            :item="cascaderItem" 
            :value="form.region"
            @input="handleInput"
        />
        
        <view class="result">
            选中的值：{{ form.region }}
        </view>
    </view>
</template>

<script>
import FormItem from '@/pageWork/components/uni-active-form/form-item.vue';

export default {
    components: {
        FormItem
    },
    data() {
        return {
            form: {
                region: [] // 级联选择器的值是数组
            },
            cascaderItem: {
                id: "region_cascader",
                compType: "cascader",
                label: "地区选择",
                placeholder: "请选择地区",
                showLabel: true,
                required: true,
                dataType: 'dynamic',
                action: 'cityDict', // 对应后端接口返回的地区数据
                popupTitle: '请选择地区',
                split: '/'
            }
        };
    },
    methods: {
        handleInput(id, value) {
            this.form[id] = value;
            console.log('级联选择器值变化:', value);
        }
    }
};
</script>
```

## 配置参数说明

| 参数名 | 类型 | 必填 | 默认值 | 说明 |
|--------|------|------|--------|------|
| compType | String | 是 | - | 组件类型，固定为 'cascader' |
| dataType | String | 是 | 'static' | 数据类型：'static'（静态）或 'dynamic'（动态） |
| action | String | 否 | - | 动态数据时的接口标识，对应字典类型 |
| options | Array | 否 | [] | 静态数据时的选项数组 |
| popupTitle | String | 否 | '请选择' | 弹窗标题 |
| split | String | 否 | '/' | 选中项的分隔符 |
| cascaderMap | Object | 否 | - | 字段映射配置 |

## 数据格式

### 静态数据格式
```javascript
[
    {
        label: '显示文本',
        value: '选项值',
        children: [
            {
                label: '子项显示文本',
                value: '子项值',
                children: [...]
            }
        ]
    }
]
```

### 动态数据格式
当 `dataType` 为 'dynamic' 时，组件会调用 `DictApi.getDictDataByDictType(action)` 获取数据。
后端应返回符合上述格式的树形结构数据。

## 事件

- `@input`: 值变化时触发，参数为 (id, value)
- `@nodeclick`: 节点点击时触发（内部事件）

## 注意事项

1. 级联选择器的值是数组格式，表示选中的路径
2. 动态数据需要后端接口支持，返回树形结构数据
3. 可以通过 `cascaderMap` 自定义字段映射
4. 组件基于 `uni-data-picker` 实现，支持多级级联选择
